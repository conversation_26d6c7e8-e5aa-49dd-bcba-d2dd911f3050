/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/ban-ts-comment */
const baseURL = import.meta.env.VITE_APP_BASE_API || ""
import logo from '@/assets/Images/Logo/ZKDIGIMAX.png';


export function captcha(
  { bindEl },
  { sliderTitleText, theme },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  validSuccess = (res, c, tac) => {},
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  validFail = (res, c, tac) => {},
  onClose = () => {}
) {
  const config = {
    // 生成接口 (必选项,必须配置, 要符合tianai-captcha默认验证码生成接口规范)
    requestCaptchaDataUrl: `${baseURL}/global/v1/captcha/slider/gen`,
    // 验证接口 (必选项,必须配置, 要符合tianai-captcha默认验证码校验接口规范)
    validCaptchaUrl: `${baseURL}/global/v1/captcha/slider/check`,
    // 验证码绑定的div块 (必选项,必须配置)
    bindEl,
    // 验证成功回调函数(必选项,必须配置)
    validSuccess: (res, c, tac) => {
      // 销毁验证码服务
      tac.destroyWindow();
      validSuccess(res, c, tac);
      // console.log('验证成功，后端返回的数据为', res);
      // 调用具体的login方法
      // login(res.data.token);
    },
    // 验证失败的回调函数(可忽略，如果不自定义 validFail 方法时，会使用默认的)
    validFail: (res, c, tac) => {
      // console.error('验证码验证失败回调...');
      // 验证失败后重新拉取验证码
      tac.reloadCaptcha();
      validFail(res, c, tac);
    },
    // 刷新按钮回调事件
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    btnRefreshFun: (el, tac) => {
      // console.log('刷新按钮触发事件...');
      tac.reloadCaptcha();
    },
    // 关闭按钮回调事件
    btnCloseFun: (el, tac) => {
      // console.log('关闭按钮触发事件...');
      onClose();
      tac.destroyWindow();
    }
  };

  // 一些样式配置， 可不传
  const style = {
    sliderTitleText,
    theme,
    // logoUrl: null // 去除logo
    logoUrl: logo, // 替换成自定义的logo
    i18n: {
        tips_success: "耗时${useTimes}秒",
        tips_error : "验证失败，请重新尝试!",
        slider_title:"拖动滑块完成拼图",
    }
  };
  // 参数1 为 tac文件是目录地址， 目录里包含 tac的js和css等文件
  // 参数2 为 tac验证码相关配置
  // 参数3 为 tac窗口一些样式配置
  window
    // @ts-expect-error
    ?.initTAC('/captcha/', config, style)
    .then((tac) => {
      tac?.init(); // 调用init则显示验证码
    })
    .catch((e) => {
      // eslint-disable-next-line no-console
      console.error('验证码初始化完毕', e);
    });
}
