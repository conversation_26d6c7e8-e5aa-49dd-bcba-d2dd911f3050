import { selectDataPermission } from "@s/api/dataPermission.js";
import { getClientId, getClientCode } from "@/hooks/client.js";
import { getAppList } from "@s/api/application";

/**
 * 持久化零售商  ID
 * @returns
 */
export const getDepartmentId = () => {
    // 获取新的 clientCode
    // 这里假设 getClientCode() 是一个异步函数，返回一个 Promise
    const newId = getClientId();
    if (newId) {
        localStorage.setItem("departmentId", newId);
    } else {
        // 如果 getClientCode() 返回 null 或 undefined，尝试使用存储的值
        const storedId = localStorage.getItem("departmentId");
        if (!storedId) {
            console.warn("无法获取新的 clientCode，且没有存储的 tenantCode");
        }
        return storedId;
    }
    return newId;
};

/**
 * 持久化零售商  Code
 * @returns
 */
export const getDepartmentCode = () => {
    const newId = getClientCode();
    if (newId) {
        localStorage.setItem("tenantCode", newId);
    } else {
        // 如果 getClientCode() 返回 null 或 undefined，尝试使用存储的值
        const storedId = localStorage.getItem("tenantCode");
        if (!storedId) {
            console.warn("无法获取新的 clientCode，且没有存储的 tenantCode");
        }
        return storedId;
    }
    return newId;
};

// 获取下拉 数据权限列表数据
export const getSelectList = (setPermissionList, departmentId) => {
    let params = {
        name: "",
        type: "",
        departmentId: departmentId,
    };
    selectDataPermission(params).then((res) => {
        setPermissionList(res?.data);
    });
};

/**
 *
 * @param {获取应用列表} setApplicationList
 */
export const getApplicationList = (setApplicationList) => {
    getAppList().then((res) => {
        if (res?.code == "00000000") {
            setApplicationList(res?.data);
        } else {
            setApplicationList([]);
        }
    });
};

/**
 *  @param {上传图片方法}
 * @param {*} file
 * @param {*} setImageUrl
 * @param {*} setFileUrl
 */
export const handleUpload = (file, setImageUrl, setFileUrl) => {
    setFileUrl(file);
    const reader = new FileReader();
    reader.onload = (e) => {
        setImageUrl(e.target.result);
    };
    reader.readAsDataURL(file);
};

export const useTableRequest = (apiFunc, defaultParams = {}) => {
    const [data, setData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isRefetching, setIsRefetching] = useState(false);
    const [isError, setIsError] = useState(false);
    const [rowCount, setRowCount] = useState(0);

    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 5,
    });

    const [filters, setFilters] = useState(defaultParams); // 额外搜索条件

    const fetchData = useCallback(async () => {
        const params = {
            page: pagination.pageIndex + 1,
            pageSize: pagination.pageSize,
            ...filters,
        };

        if (!data.length) {
            setIsLoading(true);
        } else {
            setIsRefetching(true);
        }

        try {
            const res = await apiFunc(params);
            setData(res.data.data || []);
            setRowCount(res.data.total || 0);
            setIsError(false);
        } catch (err) {
            console.error(err);
            setIsError(true);
        } finally {
            setIsLoading(false);
            setIsRefetching(false);
        }
    }, [pagination, filters, apiFunc]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const search = (newFilters = {}) => {
        setFilters(newFilters);
        setPagination((prev) => ({ ...prev, pageIndex: 0 })); // 搜索后回第一页
    };

    const reset = ((keepParams = {}) => {
        setFilters({ ...keepParams });
        setPagination({ pageIndex: 0, pageSize: 5 });
    });

    return {
        data,
        isLoading,
        isRefetching,
        isError,
        rowCount,
        pagination,
        setPagination,
        fetchData,
        search,
        reset,
    };
};


