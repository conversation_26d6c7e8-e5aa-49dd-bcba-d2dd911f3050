import React from "react";
import CustInput from "@c/CustInput";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import * as Yup from "yup";
function PersonalInfo() {
  // 国际化
  const { t } = useTranslation();

  const areaFormik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
    },
    onSubmit: (values) => {},

    validationSchema: Yup.object().shape({
      firstName: Yup.string().required(t("名称是必传的")),
      lastName: Yup.string().required(t("名称是必传的")),
    }),
  });

  return (
    <form noValidate onSubmit={areaFormik.handleSubmit}>
      <Grid xs={12} md={6} lg={12} sm={4}>
        <Grid>
          <CustInput
            name={"firstName"}
            label={"First Name"}
            required={true}
            formik={areaFormik}></CustInput>
        </Grid>

        <Grid mt={2}>
          <CustInput
            name={"lastName"}
            label={"Last Name"}
            required={true}
            formik={areaFormik}></CustInput>
        </Grid>

        <Button
          id="AddOutlet-button-01"
          variant="contained"
          size="medium"
          className="text-transform-none"
          type="submit"
          style={{
            borderRadius: "15px",
            opacity: 1,
            padding: "12px 16px",
            width: "248px",
            height: "65px",
            position: "absolute",
            right: "40px",
            marginTop: "20px",
            font: "normal normal bold 16px/20px Proxima Nova",
            color: "#FFFFFF",
            background:
              "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
          }}>
          Save Changes
        </Button>
      </Grid>
    </form>
  );
}

export default PersonalInfo;
