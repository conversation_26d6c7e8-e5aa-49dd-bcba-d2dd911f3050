import React from "react";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/ViewBox";
import { Avatar } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import { getParnerDetail } from "@s/api/partner";
import InfoBox from "@c/ViewBox.jsx";
function ViewPartner(props) {
  // 国际化
  const { t } = useTranslation();
  const { state } = useLocation();
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [imageUrl, setImageUrl] = useState(null);
  useEffect(() => {
    if (state?.type == "view" || state?.type == "editor") {
      getParnerDetail(state?.id).then((res) => {
        setData(res?.data);
        setImageUrl(res?.data?.photo);
      });
    }
  }, []);

  let titleStyle = {
    font: `normal normal normal 14px/18px Proxima Nova`,
    color: "#474B4F",
    opacity: 0.5,
  };

  let textStyle = {
    fontSize: "16px",
    color: "#474B4F",
    marginTop: "10px",
  };

  return (
    <React.Fragment>
      <RightViewLayout
        title={t("partner.view")}
        navigateBack={"/partner"}
        handleCancle={() => {
          navigate("/partner");
        }}
        isShowSave={false}>
        <Grid container flexDirection={"column"} gap={4}>
          <Avatar
            className="avatar radial-button"
            alt="ZK"
            src={imageUrl}
            sx={{
              width: "120px",
              height: "120px",
              borderRadius: "50%",
            }}></Avatar>

          <InfoBox title={t("partner_user.first_name")} content={data?.name} />

          <InfoBox title={t("partner_user.email")} content={data?.email} />

          <InfoBox
            title={t("common.common_mobile")}
            content={
              `${data?.countryCode}`
                ? `+ ${data?.countryCode} ${data?.phone}`
                : null
            }
          />

          <InfoBox
            title={t("partner_user.area_name")}
            content={data?.areaName}
          />

          <InfoBox title={t("partner_user.address")} content={data?.address} />
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default ViewPartner;
