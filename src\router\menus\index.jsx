import MainLayout from "@/layout/MainLayout";

import dashboardRoute from "./dashboard/dashboard";
import areadRoute from "./area/area";
import AuthRoute from "./permission/index";
import MenuItem from "./menuItem/index";
import scriptionRoute from "./subscription/index";
import ApplicationCenter from "./applicationCenter/index";
import information from "./infomation";
import Log from "./Log/index";
import RetailMainRoute from "./retailMainData";
import SystemRoute from "./system";
import dataPermissionRoute from "./dataPermission/index";
import DeviceManage from "./deviceManage/index";
import organizationRoute from "./organization";

const bizRoutes = [
  {
    path: "/",
    element: <MainLayout />,
    children: [
      ...dashboardRoute,
      ...areadRoute,
      ...scriptionRoute,
      ...MenuItem,
      ...AuthRoute,
      ...ApplicationCenter,
      ...information,
      ...Log,
      ...SystemRoute,
      ...organizationRoute,
      ...RetailMainRoute,
      ...dataPermissionRoute,
      ...DeviceManage,
    ],
  },
];
export default bizRoutes;
