/** @type {import('tailwindcss').Config} */
const colors = require('tailwindcss/colors')
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    './node_modules/@material-ui/core/**/*.{js,ts,jsx,tsx}'
  ],
  theme: {
    screens: {
      'sm': '640px',
      'xs':'390px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    fontSize: {
      sm: '0.8rem',
      base: '1rem',
      xl: '1.25rem',
      '2xl': '1.563rem',
      '3xl': '1.953rem',
      '4xl': '2.441rem',
      '5xl': '3.052rem',
    },
    colors: {
        transparent: 'transparent',
        current: 'currentColor',
        black: colors.black,
        white: colors.white,
        gray: colors.trueGray,
        indigo: colors.indigo,
        red: colors.rose,
        yellow: colors.amber,
        // 注释掉的其他颜色...
        primary: {
          50: '#f0f9eb',
          100: '#e0f3d8',
          200: '#c9e7b7',
          300: '#a8d694',
          400: '#87c471',
          500: '#7ac143', // 主色
          600: '#65a83a',
          700: '#528f31',
          800: '#3f7628',
          900: '#2c5d1f',
        }
    },
    fontFamily: {
      'proxima': ['"Proxima Nova"'],
    },
    extend: {
      gridTemplateColumns: {
        // 保持与现有设计一致的列宽
        '16': 'repeat(auto-fit, minmax(300px, 1fr))'
      }
      // height: {
      //   'full': '100vh', // 这里可以覆盖或添加自定义的高度值
      // },
      // width:{
      //   'full': '100vw',
      // }
    },
  },
  plugins: [],
}

