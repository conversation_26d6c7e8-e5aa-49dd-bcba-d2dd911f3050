import React from "react";
import DataTable from "../components/DataTable";
import { PrincipalsColumns, PartnerColumns } from "../components/Colums";
import { getPrincipaList } from "@/service/api/principal.js";
import { getParnerList } from "@/service/api/partner.js";
import { useTranslation } from "react-i18next";

const LeftTable = forwardRef((props, ref) => {
  const { dataType, tableRef, rowSelection, setRowSelection } = props;
  const { t } = useTranslation();
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  // 构建参数
  const buildParams = (name) => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      name: name,
    };

    return params;
  };

  const loadData = (name = null) => {
    setIsLoading(true);
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    const params = buildParams(name);
    try {
      if (dataType == "6") {
        getPrincipaList(params).then((res) => {
          setData(res.data.data || []);
          setRowCount(res.data.total || 0);
          setIsLoading(false);
        });
      } else if (dataType == "5") {
        getParnerList(params).then((res) => {
          setData(res.data.data || []);
          setRowCount(res.data.total || 0);
          setIsLoading(false);
        });
      }
    } catch (error) {
      setIsError(true);
      setData([]);
      setRowCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  useImperativeHandle(ref, () => ({
    loadData,
  }));

  useEffect(() => {
    loadData();
  }, [pagination.pageIndex, pagination.pageSize, dataType]);

  return (
    <Grid item xs={5.6}>
      <DataTable
        title={
          dataType == "6"
            ? t("datascope.principals_list")
            : t("datascope.partner_list")
        }
        columns={dataType == "6" ? PrincipalsColumns() : PartnerColumns()}
        enableRowActions={false}
        data={data}
        rowCount={rowCount}
        isRefetching={isRefetching}
        isError={isError}
        state={{
          // 加载状态
          isLoading: isLoading,
          rowSelection,
        }}
        tableInstanceRef={tableRef} // 绑定 ref
        rowsPerPage={pagination.pageSize}
        currentPage={pagination.pageIndex}
        getRowId={(originalRow) => originalRow.id} // 确保 ID 唯一
        enableRowSelection={true}
        //行选中
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        onRowSelectionChange={setRowSelection}
        onPageChange={(pageIndex) => {
          setPagination((prev) => ({
            ...prev,
            pageIndex, // 更新页码
          }));
        }}
        onPageSizeChange={(pageSize) => {
          setPagination({
            pageIndex: 0, // 重置页码
            pageSize, // 更新每页行数
          });
        }}></DataTable>
    </Grid>
  );
});

export default LeftTable;
