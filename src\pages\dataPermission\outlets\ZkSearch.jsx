import React from "react";
import ZkInput from "../components/ZKInput.jsx";
import { useTranslation } from "react-i18next";
function ZkSearch(props) {
  const { rowId, searchName, setSearchName, rightTableRef } = props;

  const { t } = useTranslation();

  const handleSearch = () => {
    if (rightTableRef.current) {
      rightTableRef.current.loadData(searchName);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      handleSearch();
    }
  };
  return (
    <React.Fragment>
      <Grid container xs={5.7} spacing={2} mb={2}>
        <Grid item xs={6}>
          <ZkInput
            sx={{
              "& .MuiOutlinedInput-input": {
                fontSize: "14px",
              },
            }}
            disabled={true}
            value={rowId?.name}
            isSearch={false}></ZkInput>
        </Grid>

        <Grid item xs={6}>
          <ZkInput
            placeholder={t("datascope.principals_selection_placeholder")}
            sx={{
              "& .MuiOutlinedInput-input": {
                fontSize: "14px",
              },
            }}
            name="name"
            value={searchName}
            handleChange={(e) => setSearchName(e.target.value)}
            isClear={true}
            onClick={handleSearch}
            onKeyPress={handleKeyPress}
            handlerClear={() => {
              setSearchName(null);
              rightTableRef.current.loadData();
            }}></ZkInput>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default ZkSearch;
