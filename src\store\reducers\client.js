// FILEPATH: src/store/reducers/menu.js

import { createSlice } from "@reduxjs/toolkit";

// initial state
const initialState = {
  clientId: null,
  clientCode: null, // 添加 retailId 到初始状态
};

const client = createSlice({
  name: "retail",
  initialState,
  reducers: {
    setClientId(state, action) {
      state.clientId = action.payload;
    },

    setClientCode(state, action) {
      state.clientCode = action.payload;
    },
  },
});

export default client.reducer;

export const { setClientId, setClientCode } = client.actions;
