// 看板
const dataPermissionRoute = [
  {
    path: "/device/manage/list",
    component: () => import("@p/deviceManage/index"),
    meta: {
      title: "Device Managerment",
      i18n: "device_manager",
      id: "3164898081434351577346",
    },
  },

  {
    path: "/add/device",
    component: () => import("@p/deviceManage/components/AddDeive"),
    meta: {
      title: "Add Device",
      i18n: "add_device_manager",
      id: "3160953344d34351577346",
    },
  },



  {
    path: "/view/device",
    component: () => import("@p/deviceManage/components/ViewDevice"),
    meta: {
      title: "View Device",
      i18n: "view_device_manager",
      id: "3160953344d34351577346",
    },
  },
];
export default dataPermissionRoute;
