/* eslint-disable no-undef */

import React, { useState, useEffect } from "react";
// import GoogleMapReact from "google-map-react";
import { compose, withProps } from "recompose";
// import { Marker, InfoWindow } from "@react-google-maps/api";
// import { listStoreStatistics } from "@/service/api/dashboard";
import { Paper, Typography, Box, Stack } from "@mui/material";
import RoomIcon from "@mui/icons-material/Room";
import { useConfirm } from "@/components/zkconfirm";

import { Grid, Button } from "@mui/material";
import { useTranslation } from "react-i18next";
// https://maps.googleapis.com/maps/api/js?key=AIzaSyA9MaTVJlWIWpINjcgyJl5eS6JDhe60238&v=3.exp&libraries=geometry,drawing,places
const withPropsConfig = {
  googleMapURL:
    "https://maps.googleapis.com/maps/api/js?key=AIzaSyA9MaTVJlWIWpINjcgyJl5eS6JDhe60238&v=3.exp&libraries=geometry,drawing,places",
  loadingElement: <div style={{ height: `100%` }} />,
  containerElement: <div style={{ height: `500px` }} />,
  mapElement: <div style={{ height: `100%` }} />,
};
import {
  withScriptjs,
  withGoogleMap,
  GoogleMap,
  Marker,
  InfoWindow,
} from "react-google-maps";
const Map = compose(
  withProps(withPropsConfig),
  withScriptjs,
  withGoogleMap
)((props) => {
  const { t } = useTranslation();
  const [markers, setMarkers] = useState([]); //存储标记点位置数据
  const [data, setData] = useState([]); //存储数据
  const [selectedMarkerIndex, setSelectedMarkerIndex] = useState(null); //判断点击的是那个卡片
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedCoordinates, setSelectedCoordinates] = useState(null);
  const confirm = useConfirm();
  const {
    defaultLocation,
    zoom,
    searchLocationAddress,
    onCoordinatesChange,
    storeInfo,
  } = props;
  // 地图数据
  const mapStyles = [
    {
      featureType: "all",
      elementType: "labels.text",
      stylers: [
        {
          color: "#878787",
        },
      ],
    },
    {
      featureType: "all",
      elementType: "labels.text.stroke",
      stylers: [
        {
          visibility: "off",
        },
      ],
    },
    {
      featureType: "landscape",
      elementType: "all",
      stylers: [
        {
          color: "#f9f5ed",
        },
      ],
    },
    {
      featureType: "road.highway",
      elementType: "all",
      stylers: [
        {
          color: "#f5f5f5",
        },
      ],
    },
    {
      featureType: "road.highway",
      elementType: "geometry.stroke",
      stylers: [
        {
          color: "#c9c9c9",
        },
      ],
    },
    {
      featureType: "water",
      elementType: "all",
      stylers: [
        {
          color: "#aee0f4",
        },
      ],
    },
  ];

  // 逆定理
  const getGeocode = (lat, lng) => {
    return new Promise((reslove, reject) => {
      fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=AIzaSyA9MaTVJlWIWpINjcgyJl5eS6JDhe60238`
      )
        .then((response) => response.json())
        .then((data) => {
          if (data.status === "OK" && data.results.length > 0) {
            // const address = data.results[0].formatted_address;
            reslove({
              data: data.results[0].address_components,
              address: data.results[0].formatted_address,
            });
            // console.log("地址信息:", address);
          } else {
            reject("error");
            // console.log("无法获取地址信息");
          }
        })
        .catch((error) => {
          reject("error");
          // console.log("错误:", error);
        });
    });
  };

  const handleMapClick = async ({ lat, lng }) => {
    //谷歌地图逆定理
    const { data, address } = await getGeocode(lat, lng);
    confirm({
      title: t("common.common_confirm_location"),
      description: t("common.common_select_current_location", {
        address: `${address}`,
        lng: String(lng),
        lat: String(lat),
      }),
      //  `当前选择的城市为：${location.address}，选择的经纬度为：${e.latlng.lng},${e.latlng.lat}`,
    })
      .then(async () => {
        // 需要逆定理
        onCoordinatesChange(lat, lng, address, data);
      })
      .catch(() => {});
  };
  const [selectedMarker, setSelectedMarker] = React.useState(null);
  const [storeMarker, setStoreMarker] = useState(null);
  const [searchMarker, setSearchMarker] = useState(null);
  const [center, setCenter] = useState(defaultLocation);
  const [storeLocation, setStoreLocation] = useState(null);
  useEffect(() => {
    if (storeInfo) {
      // todo先以默认标记点
      const locations = storeInfo.location.split(",");
      const location = {
        lat: parseFloat(locations[1]),
        lng: parseFloat(locations[0]),
      };
      setStoreLocation(location);
      setStoreMarker(location);
      setCenter(location);
    }
    if (searchLocationAddress) {
      setSearchMarker(searchLocationAddress.location);
      setCenter(searchLocationAddress.location);
    }
  }, [searchLocationAddress]);

  const WrappedMap = withScriptjs(
    withGoogleMap((props) => (
      <GoogleMap
        defaultZoom={8}
        defaultCenter={{ lat: -34.397, lng: 150.644 }}
        onLoad={handleMapLoad}>
        {props.isScriptLoaded && !props.isScriptLoadSucceed && (
          <div>无法加载地图，请检查网络连接并稍后再试。</div>
        )}
        {props.isScriptLoaded && props.isScriptLoadSucceed && (
          <Marker position={{ lat: -34.397, lng: 150.644 }} />
        )}
      </GoogleMap>
    ))
  );

  return (
    <>
      <GoogleMap
        options={{ styles: mapStyles, disableDefaultUI: true }}
        defaultZoom={6}
        zoom={zoom}
        defaultCenter={defaultLocation}
        center={center}
        onClick={(e) => {
          const lat = e.latLng.lat();
          const lng = e.latLng.lng();
          handleMapClick({ lat, lng });
        }}>
        {storeInfo && (
          <Marker
            position={storeLocation}
            onClick={(e) => {
              handleMapClick({ lat: e.latLng.lat(), lng: e.latLng.lng() });
              setStoreMarker(storeLocation);
            }}>
            {storeMarker && (
              <InfoWindow
                position={{ lat: storeMarker.lat, lng: storeMarker.lng }}
                onCloseClick={() => setStoreMarker(null)}>
                <Box
                  sx={{ maxWidth: "250px", minWidth: "200px", border: "none" }}>
                  {/* <div style="font-size: 14px;line-height: 24px;position: absolute;top: 2px;color: rgb(85, 85, 85);
                  width: 238px;height: 120px;overflow: auto;"> */}
                  <Stack>
                    <Typography
                      sx={{
                        fontSize: "14px",
                        lineHeight: "24px",
                        color: "rgb(85, 85, 85)",
                      }}>
                      {t("common.common_store_location_info")}:{" "}
                      <span>{storeInfo?.name}</span>
                    </Typography>
                    <Typography
                      sx={{
                        fontSize: "14px",
                        lineHeight: "24px",
                        color: "rgb(85, 85, 85)",
                      }}>
                      {t("common.common_current_location_e")}:{" "}
                      <span>{storeInfo?.address}</span>
                    </Typography>
                    <Typography
                      sx={{
                        fontSize: "14px",
                        lineHeight: "24px",
                        color: "rgb(85, 85, 85)",
                      }}>
                      {t("common.common_current_location_e")}:{" "}
                      <span>
                        {storeMarker.lat}, {storeMarker.lng}
                      </span>
                    </Typography>
                  </Stack>
                </Box>
              </InfoWindow>
            )}
          </Marker>
        )}

        {searchLocationAddress && (
          <Marker
            position={searchLocationAddress.location}
            onClick={(e) => {
              handleMapClick({ lat: e.latLng.lat(), lng: e.latLng.lng() });
              setStoreMarker(searchLocationAddress.location);
            }}>
            {searchMarker && (
              <InfoWindow
                position={searchMarker}
                onCloseClick={() => setSearchMarker(null)}>
                <Box
                  sx={{ maxWidth: "250px", minWidth: "200px", border: "none" }}>
                  <Stack>
                    <Typography>{t("common.common_search_info")}: </Typography>
                    {searchLocationAddress?.description}
                  </Stack>
                  <Stack>
                    <Typography>
                      {t("common.common_current_lng_lat")}:{" "}
                    </Typography>
                    {searchLocationAddress?.location?.lat},
                    {searchLocationAddress?.location?.lng}
                  </Stack>
                </Box>
              </InfoWindow>
            )}
          </Marker>
        )}
      </GoogleMap>
    </>
  );
});

// function InfoCard({ lat, lng, addressData }) {
//   return (
//     <div
//       style={{
//         zIndex: 1000,
//       }}
//     >
//       <Paper
//         style={{
//           backgroundColor: "#DEEDDE",
//           border: "3px solid #7cb305",
//           alignItems: "center",
//           marginLeft: "-200px", // 根据条件设置左侧边距
//           position: "absolute",
//           whiteSpace: "nowrap",
//           borderRadius: "10px",
//           padding: "10px 10px 10px 10px",
//           transform: "translate(-50%, -20%)", // 根据条件设置 transform 属性
//           zIndex: 1111111111111,
//         }}
//       >
//         <Typography variant="subtitle2">搜索信息</Typography>
//         <Typography variant="body2">地点名称 : {addressData}</Typography>
//         <Typography variant="body2">经度 : {lat}</Typography>
//         <Typography variant="body2">维度 : {lng}</Typography>
//       </Paper>
//     </div>
//   );
// }

export default Map;
