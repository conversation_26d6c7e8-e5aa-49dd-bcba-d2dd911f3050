# Schema Form 配置化组件

一个基于JSON Schema的声明式表单配置系统，提供了更强大、更灵活的表单构建能力。

## 🚀 特性

### 与现有Config组件的对比

| 特性 | 现有Config风格 | 新Schema风格 |
|------|---------------|-------------|
| 配置方式 | 命令式，基于type字段 | 声明式，基于JSON Schema |
| 类型安全 | 弱类型，运行时检查 | 强类型，Schema验证 |
| 扩展性 | 硬编码if-else | 插件化注册机制 |
| 布局能力 | 简单Grid布局 | 多种布局组件 |
| 条件逻辑 | 基础条件渲染 | 丰富的条件表达式 |
| 验证机制 | 依赖外部验证 | 内置Schema验证 |
| 维护性 | 代码重复，难维护 | 配置驱动，易维护 |

### 核心优势

1. **声明式配置**：通过JSON Schema描述表单结构，更直观易懂
2. **类型安全**：基于Schema的强类型验证，减少运行时错误
3. **插件化架构**：字段组件可插拔，易于扩展和自定义
4. **丰富的布局**：支持Group、Grid、Card、Tabs等多种布局
5. **强大的条件逻辑**：支持复杂的字段依赖和条件渲染
6. **国际化支持**：内置多语言支持
7. **更好的开发体验**：配置即文档，易于理解和维护

## 📦 安装使用

```jsx
import { SchemaFormRenderer } from '@/components/SchemaForm';

// 基础使用
<SchemaFormRenderer
  schema={formSchema}
  formData={data}
  onChange={handleChange}
  onValidate={handleValidate}
/>
```

## 🔧 核心概念

### 1. Schema配置

```javascript
const schema = {
  type: 'object',
  title: '表单标题',
  properties: {
    fieldName: {
      type: 'string',        // 字段类型
      title: '字段标题',      // 显示标签
      required: true,        // 是否必填
      minLength: 3,          // 最小长度
      pattern: '^[a-zA-Z]+$' // 正则验证
    }
  }
};
```

### 2. UI Schema配置

```javascript
const uiSchema = {
  fieldName: {
    widget: 'textarea',      // 使用特定Widget
    placeholder: '请输入',   // 占位符
    help: '帮助文本',        // 帮助信息
    clearable: true          // 是否可清空
  }
};
```

### 3. 布局系统

```javascript
const layoutSchema = {
  layout: {
    type: 'grid',           // 布局类型
    columns: 2,             // 网格列数
    title: '布局标题'       // 布局标题
  },
  fields: [...]             // 子字段
};
```

## 📋 支持的字段类型

### 基础类型
- `string` - 文本输入
- `number` / `integer` - 数字输入
- `boolean` - 布尔值（复选框）
- `array` - 数组（多选）
- `object` - 对象（嵌套表单）

### 格式化类型
- `email` - 邮箱输入
- `password` - 密码输入
- `url` - URL输入
- `tel` - 电话输入
- `date` / `datetime` / `time` - 日期时间

### Widget变体
- `textarea` - 文本区域
- `select` - 下拉选择
- `radio` - 单选按钮
- `checkbox` - 复选框
- `switch` - 开关

## 🎨 布局组件

### Group 分组
```javascript
{
  layout: {
    type: 'group',
    title: '分组标题',
    description: '分组描述'
  },
  fields: [...]
}
```

### Grid 网格
```javascript
{
  layout: {
    type: 'grid',
    columns: 2,           // 列数
    spacing: 2            // 间距
  },
  fields: [...]
}
```

### Card 卡片
```javascript
{
  layout: {
    type: 'card',
    title: '卡片标题',
    collapsible: true     // 是否可折叠
  },
  fields: [...]
}
```

### Tabs 标签页
```javascript
{
  layout: {
    type: 'tabs',
    tabs: [
      { key: 'tab1', label: '标签1', fields: [...] },
      { key: 'tab2', label: '标签2', fields: [...] }
    ]
  }
}
```

## ⚡ 条件逻辑

### 条件显示
```javascript
{
  name: 'conditionalField',
  type: 'string',
  title: '条件字段',
  if: {
    field: 'triggerField',
    operator: 'equals',
    value: 'showCondition'
  }
}
```

### 条件必填
```javascript
{
  name: 'requiredField',
  type: 'string',
  title: '条件必填字段',
  requiredIf: {
    field: 'triggerField',
    operator: 'not_empty'
  }
}
```

### 支持的操作符
- `equals` / `!=` - 等于/不等于
- `in` / `not_in` - 包含/不包含
- `>` / `>=` / `<` / `<=` - 大小比较
- `contains` / `starts_with` / `ends_with` - 字符串匹配
- `is_empty` / `is_not_empty` - 空值检查
- `regex` - 正则匹配

## 🔌 扩展机制

### 注册自定义字段
```javascript
import { SchemaFieldRegistry } from '@/components/SchemaForm';

const registry = new SchemaFieldRegistry();

// 注册新字段类型
registry.registerField('custom', CustomFieldComponent);

// 注册Widget变体
registry.registerWidget('string', 'custom-input', CustomInputComponent);

// 使用自定义注册表
<SchemaFormRenderer
  schema={schema}
  fieldRegistry={registry}
/>
```

### 自定义验证器
```javascript
registry.registerValidator('customValidation', (value, schema) => {
  // 自定义验证逻辑
  return value.length > 5 ? null : '长度必须大于5';
});
```

## 📖 完整示例

查看 `examples/BasicExample.jsx` 了解完整的使用示例，包括：
- 基础表单配置
- 布局组合使用
- 条件逻辑实现
- 数据验证处理

## 🔄 迁移指南

### 从现有Config组件迁移

1. **配置转换**：将基于type的配置转换为Schema格式
2. **组件映射**：现有组件可以注册到新的字段注册表中
3. **逐步迁移**：可以在同一项目中并存使用，逐步迁移

### 迁移示例

**原Config风格：**
```javascript
const config = [
  {
    type: 'input',
    name: 'username',
    label: '用户名',
    required: true
  }
];
```

**新Schema风格：**
```javascript
const schema = {
  properties: {
    username: {
      type: 'string',
      title: '用户名',
      required: true
    }
  }
};
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个组件系统！
