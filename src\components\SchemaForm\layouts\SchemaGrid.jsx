import React from 'react';
import {
  Grid,
  Box,
  Typography,
  Divider
} from '@mui/material';

/**
 * Schema网格布局组件
 */
const SchemaGrid = ({
  title,
  description,
  children,
  columns = 2,
  spacing = 2,
  alignItems = 'stretch',
  justifyContent = 'flex-start',
  divider = false,
  sx,
  ...props
}) => {
  // 计算每个子项的宽度
  const getItemWidth = (childrenCount) => {
    if (columns === 1) return 12;
    if (columns === 2) return 6;
    if (columns === 3) return 4;
    if (columns === 4) return 3;
    if (columns === 6) return 2;
    return Math.floor(12 / columns);
  };

  const itemWidth = getItemWidth(React.Children.count(children));

  const gridContent = (
    <Grid 
      container 
      spacing={spacing}
      alignItems={alignItems}
      justifyContent={justifyContent}
      sx={sx}
      {...props}
    >
      {React.Children.map(children, (child, index) => (
        <Grid
          item
          xs={12}
          sm={itemWidth}
          key={index}
        >
          {child}
        </Grid>
      ))}
    </Grid>
  );

  return (
    <Box className="schema-form-grid">
      {(title || description) && (
        <Box sx={{ mb: 2 }}>
          {title && (
            <Typography variant="h6" component="h3" sx={{ mb: description ? 1 : 0 }}>
              {title}
            </Typography>
          )}
          
          {description && (
            <Typography
              variant="body2"
              color="text.secondary"
            >
              {description}
            </Typography>
          )}
          
          {divider && <Divider sx={{ mt: 1 }} />}
        </Box>
      )}
      
      {gridContent}
    </Box>
  );
};

export default SchemaGrid;
