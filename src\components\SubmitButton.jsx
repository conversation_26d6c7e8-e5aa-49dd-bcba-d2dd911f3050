import AnimateButton from "@c/@extended/AnimateButton";
import LoadingButton from "@mui/lab/LoadingButton";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
function SubmitButton(props) {
  const cancel = () => {
    formik.resetForm();
    navigate(callbackRoute);
  };
  const {
    formik,
    callbackRoute,
    loading,
    affirmText,
    cancelText,
    handlerSubmit,
    CancelSubmit = cancel,
  } = props;
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <Grid item xs={12}>
      <Stack
        direction="row"
        justifyContent="flex-end"
        alignItems="flex-end"
        spacing={2}>
        <Button
          variant="outlined"
          onClick={CancelSubmit}
          style={{
            width: "182px",
            height: "60px",
            borderRadius: "10px",
            opacity: 1,
            border: "1px solid #E3E3E3",
          }}
          disableElevation
          color="info">
          {cancelText || t("Cancel")}
        </Button>

        <AnimateButton>
          <LoadingButton
            loading={loading}
            disableElevation
            disabled={formik?.isSubmitting}
            fullWidth
            type="submit"
            onClick={handlerSubmit}
            variant="contained"
            style={{
              width: "182px",
              height: "60px",
              borderRadius: "10px",
              opacity: 1,
              background:
                "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
            }}>
            {affirmText || t("Save")}
          </LoadingButton>
        </AnimateButton>

        {formik?.errors.submit && (
          <Grid item xs={12}>
            <FormHelperText error>{formik?.errors.submit}</FormHelperText>
          </Grid>
        )}
      </Stack>
    </Grid>
  );
}

export default SubmitButton;
