import request from "@/utils/request";

const baseProfixURI = `${import.meta.env.VITE_APICODE}/auth/role`;

/**
 *  查询区域树形列表
 */
export const getRoleList = (params) => {
  return request({
    url: `${baseProfixURI}/query/tree`,
    method: "get",
    params: params,
  });
};

/**
 *  获取菜单权限列表
 */
export const getPermissionList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/auth/resource/tree`,
    method: "get",
    params: params,
  });
};

/**
 * 新增角色
 */

export const addRoles = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "post",
    data: params,
  });
};

/**
 * 编辑角色
 */

export const editRoles = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "put",
    data: params,
  });
};

/**
 * 删除角色
 */

export const deteleRoles = (ids) => {
  return request({
    url: `${baseProfixURI}/${ids}`,
    method: "delete",
  });
};

/**
 * 根据ID 查询角色详情
 */

export const getRoleDetail = (id) => {
  return request({
    url: `${baseProfixURI}/query/${id}`,
    method: "get",
  });
};

/**
 * 查询部门角色列表
 */

export const getPartRoleList = (params) => {
  return request({
    url: `${baseProfixURI}/query/list`,
    method: "GET",
    params: params,
  });
};

/**
 * 获取左侧菜单列表
 */

export const getMenuList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/auth/resource/menu`,
    method: "GET",
    params: params,
  });
};


/**
 *  查询所有应用部门角色  新增各级用户时绑定角色使用
 */

export const getAllPartRoleList = (params) => {
  return request({
    url: `${baseProfixURI}/list`,
    method: "GET",
    params: params,
  });
}



/**
 *    分页查询 角色列表
 * 
 */


export const getPageRoleList = (params) => {
  return request({
    url: `${baseProfixURI}/query/page`,
    method: "GET",
    params: params,
  });
}