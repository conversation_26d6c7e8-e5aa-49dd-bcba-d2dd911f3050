import React from 'react';
import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';

// 异常页面专用布局容器
const ExceptionContainer = styled(Box)(({ theme }) => ({
  width: '100vw',
  height: '100vh',
  overflow: 'auto',
  position: 'relative',
  backgroundColor: theme.palette.background.default,
  // 确保异常页面占满整个屏幕，不受其他布局影响
  zIndex: 1000,
}));

/**
 * 异常页面布局组件
 * 用于404、403、500等错误页面，不显示菜单栏和其他导航元素
 */
const ExceptionLayout = ({ children }) => {
  return (
    <ExceptionContainer>
      {children}
    </ExceptionContainer>
  );
};

export default ExceptionLayout;
