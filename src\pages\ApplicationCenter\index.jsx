/* eslint-disable react/prop-types */
import { Link } from "react-router-dom";
import MainCard from "@/components/MainCard";
import React, { useEffect, useState } from "react";
import screenDirectLogo from "@/assets/Home/<EMAIL>";
import <PERSON>ata<PERSON>ogo from "@/assets/Home/<EMAIL>";
import NuTagLogo from "@/assets/Home/<EMAIL>";
import fristBgPhoto from "@/assets/Images/Rectangle <EMAIL>";
import computer from "@/assets/Images/applicationCenter/pngwing.com (44)@2x.png";
import cmsContent from "@/assets/Images/applicationCenter/CMSZKDIGIMAXPNG.png";
import nutagContent from "@/assets/Images/applicationCenter/<EMAIL>";
import zataContent from "@/assets/Images/applicationCenter/zata.png";
import cmsBgContent from "@/assets/Images/applicationCenter/<EMAIL>";
import eplLabel from "@/assets/Images/applicationCenter/eplLabel.png";
import AICamera from "@/assets/Images/applicationCenter/AICamera.png";
import { pxToRem } from "@/utils/zkUtils.js";
import { getApplicationList } from "@/utils/commonUtils.js";
import { useTranslation } from "react-i18next";
import actions from "@/utils/actions";
import { getToken } from "@/utils/auth";
import { useStateUserInfo } from "@/hooks/user";
import { getAuthButton } from "@/service/api/user";
import { useNavigate } from "react-router-dom";

const index = () => {
  const { t } = useTranslation();
  const userInfo = useStateUserInfo();
  const [useAppList, setUseAppList] = useState([]);
  const [appIds, setAppIds] = useState([]);

  useEffect(() => {
    getApplicationList(setUseAppList);
  }, []);

  useEffect(() => {
    let codes = useAppList.map((item) => {
      return item.code;
    });
    setAppIds(codes);
  }, [useAppList]);

  return (
    <React.Fragment>
      <MainCard style={{ marginBottom: "10px" }}>
        <Typography variant="h4">{t("appCenter.title")}</Typography>
      </MainCard>

      <Grid
        sx={{
          display: "flex",
          justifyContent: "space-between",
        }}>
        <MyApplicationCard
          applicationLogo={screenDirectLogo}
          applicationCode={"SD"}
          text={"Screen Direct"}
          onClick={async () => {
            const res = await getAuthButton("SD");
            const combinedPermissions = [
              ...(userInfo?.permissions || []),
              ...(res?.data || []),
            ];

            const updatedUserInfo = {
              ...userInfo,
              permissions: combinedPermissions,
            };

            actions.setGlobalState({
              token: getToken(),
              userInfo: updatedUserInfo,
            });
          }}
          bgImage={fristBgPhoto}
          disabled={appIds.includes("SD") ? false : true}
          lastImage={cmsContent}
          pathRouter={"/cms-app"}
          linkButtonText={t("appCenter.open")}
          background={`transparent linear-gradient(180deg, #FFFFFF 0%, #F3F3F333 78%, #F0F0F000 100%) 0% 0% no-repeat padding-box`}>
          <Grid
            sx={{
              padding: "0px 80px 20px 40px",
              font: `normal normal bold 18px/26px Proxima Nova`,
              color: "#222222",
            }}>
            {t("appCenter.cms_content")}
          </Grid>
          <img
            src={cmsBgContent}
            alt="无法加载"
            srcSet=""
            style={{
              zIndex: "11111111",
              width: "75%",
              height: "100%",
              marginLeft: "13%",
              marginTop: "5%",
              border: "10px solid #000",
            }}
          />
        </MyApplicationCard>

        <MyApplicationCard
          applicationLogo={NuTagLogo}
          applicationCode={"NT"}
          text={"NuTag"}
          disabled={false}
          onClick={async () => {
            const res = await getAuthButton("NT");
            const combinedPermissions = [
              ...(userInfo?.permissions || []),
              ...(res?.data || []),
            ];

            const updatedUserInfo = {
              ...userInfo,
              permissions: combinedPermissions,
            };

            actions.setGlobalState({
              token: getToken(),
              userInfo: updatedUserInfo,
            });
          }}
          bgImage={fristBgPhoto}
          lastImage={nutagContent}
          pathRouter={"/e-price-tag-app"}
          linkButtonText={t("appCenter.subscribe")}
          background={`transparent linear-gradient(180deg, #FFFFFF 0%, #F3F3F333 78%, #F0F0F000 100%) 0% 0% no-repeat padding-box`}>
          <Grid
            sx={{
              padding: "0px 80px 20px 40px",
              font: `normal normal bold 18px/26px Proxima Nova`,
              color: "#222222",
            }}>
            {t("appCenter.nutag_content")}
          </Grid>

          <Grid
            sx={{
              display: "flex",
              justifyContent: "center",
            }}>
            <img
              src={eplLabel}
              alt="无法加载"
              srcSet=""
              style={{
                zIndex: "11111111",
                width: "80%",

                marginLeft: "5%",
                marginTop: "5%",
              }}
            />
          </Grid>
        </MyApplicationCard>

        <MyApplicationCard
          disabled={false}
          applicationLogo={ZataLogo}
          applicationCode={"ZT"}
          text={"Zata"}
          onClick={async () => {
            const res = await getAuthButton("ZT");
            const combinedPermissions = [
              ...(userInfo?.permissions || []),
              ...(res?.data || []),
            ];

            const updatedUserInfo = {
              ...userInfo,
              permissions: combinedPermissions,
            };

            actions.setGlobalState({
              token: getToken(),
              userInfo: updatedUserInfo,
            });
          }}
          bgImage={fristBgPhoto}
          lastImage={zataContent}
          pathRouter={"/retail-ai-app"}
          linkButtonText={t("appCenter.subscribe")}
          background={`transparent linear-gradient(180deg, #FFFFFF 0%, #F3F3F333 78%, #F0F0F000 100%) 0% 0% no-repeat padding-box`}>
          <Grid
            sx={{
              padding: "0px 80px 20px 40px",
              font: `normal normal bold 18px/26px Proxima Nova`,
              color: "#222222",
            }}>
            {t("appCenter.zata_content")}
          </Grid>

          <Grid
            sx={{
              position: "relative",
              display: "flex",
              justifyContent: "center",
            }}>
            <img
              src={AICamera}
              alt="无法加载"
              style={{
                zIndex: "1",
                left: "35px",
                top: "14px",
                position: "relative",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "50%",
                marginLeft: "-130px",
                top: "80px",
                // left:'260px',
                // transform: 'translate(-50%, -50%)',
                width: "0",
                height: "0",
                borderLeft: "130px solid transparent",
                borderRight: "130px solid transparent",
                borderBottom: "240px solid #78BC2757",
                background:
                  "linear-gradient(180deg, #78BC2757 0%, #78BC2700 100%)",
                zIndex: "2",
              }}></div>
          </Grid>
        </MyApplicationCard>
      </Grid>
    </React.Fragment>
  );
};
export default index;

const MyApplicationCard = (props) => {
  const userInfo = useStateUserInfo();
  const navigate = useNavigate();
  const {
    applicationLogo,
    applicationCode,
    text,
    children,
    lastImage,
    pathRouter,
    linkButtonText,
    background,
    onClick = () => {},
    disabled = false,
  } = props;
  return (
    <Grid
      item
      sx={{
        position: "relative",
      }}>
      <Grid
        sx={{
          position: "relative",
          minHeight: "680px",
          // boxShadow: "0px 0px 10px #0000000D",
          // border: "1px solid #E0E0E0",
          // backgroundColor: "#ffffff",
          borderRadius: "10px",
          maxWidth: pxToRem(490),
          width: "100%",
          background: background,
          opacity: 1,
          boxSizing: "border-box",
        }}>
        <Grid
          sx={{
            padding: "20px 20px 20px 40px",
            marginTop: "20px",
            justifyContent: "space-between",
          }}
          container
          display="flex"
          position="relative"
          zIndex={2}>
          <Grid
            sx={{
              display: "flex",
              alignItems: "center",
            }}>
            <Grid item>
              <img
                src={applicationLogo}
                alt="加载失败"
                style={{
                  width: pxToRem(81),
                  height: pxToRem(81),
                }}
              />
            </Grid>

            <Grid item m={2} width={"50px"}>
              <Typography variant="h4">{text}</Typography>
            </Grid>
          </Grid>

          <Grid
            sx={{
              display: "flex",
              alignItems: "center",
            }}
            item>
            <Link
              disabled={true}
              to={pathRouter}
              onClick={onClick}
              style={{
                border: "1px solid #BBBBBB",
                borderRadius: "10px",
                height: "50px",
                minWidth: "100px",
                opacity: 1,
                textDecorationLine: "none",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: disabled ? "#8987879e" : "#000000",
                fontWeight: "600",
                boxShadow: `0px 3px 6px #00000029`,
                cursor: disabled ? "not-allowed" : "pointer",
              }}>
              {linkButtonText}
            </Link>
          </Grid>
        </Grid>

        <Grid>{children}</Grid>

        <Grid
          sx={{
            position: "absolute",
            bottom: "-70px",
            width: "100%",
            height: "185px",
            display: "flex",
            justifyContent: "center",
          }}>
          <Grid
            sx={{
              position: "relative",
              width: "100%",
              height: "185px",
              maxWidth: "329px",
            }}>
            {/* 笔记本电脑图片 */}
            <img
              style={{
                width: "90%", // 笔记本图片占满父容器
                height: "90%", // 笔记本图片占满父容器
                zIndex: 1, // 确保背景图片在底层
                position: "absolute", // 笔记本电脑图片的定位
                top: 0,
                left: 0,
              }}
              src={computer}
              alt="加载失败"
            />

            {/* 显示在屏幕上的图片 */}
            <img
              style={{
                width: "64%", // 根据屏幕比例调整大小
                height: "58%", // 根据屏幕比例调整大小
                zIndex: 2, // 确保屏幕图片在笔记本电脑之上
                position: "absolute", // 屏幕图片绝对定位
                top: "12%", // 调整位置，使其居中显示在屏幕上
                left: "13%", // 调整位置，使其居中显示在屏幕上
                borderRadius: "3px",
                transform: "perspective(1000px) rotateX(1deg) rotateY(-2deg)", // 3D 旋转效果
              }}
              src={lastImage}
              alt="加载失败"
            />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};
