/* eslint-disable no-undef */
import React, {
  useEffect,
  useState,
  forwardRef,
  useRef,
  useImperativeHandle,
} from "react";
import Grid from "@mui/material/Grid";

import Typography from "@mui/material/Typography";
import "./del.less";

import MapChart from "./components/Map";
import MapInput from "./components/GoogleMaps";
import {
  BootstrapDialog,
  BootstrapContent,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
import { textAlign } from "@mui/system";

const selectMap = forwardRef((props, ref) => {
  const { getLocation, currentPosition, storeInfo } = props;
  const { t } = useTranslation();
  const mapRef = useRef(null);
  const [zoom, setZoom] = useState(4);

  const [open, setOpen] = useState(false);
  const [searchLocation, setSearchLocation] = useState(null);

  const [coordinates, setCoordinates] = useState({
    lng: 115.31266293399872,
    lat: 35.52631901944564,
  });

  useImperativeHandle(ref, () => ({
    handleClose,
    handleClickOpen,
  }));

  //获取项目地址名称
  const handlerDetailAddress = (newDetailAddress) => {
    console.log("newDetailAddress", newDetailAddress);
    setSearchLocation(newDetailAddress);
  };

  const handleClickOpen = () => {
    if (currentPosition) {
      setCoordinates({
        lat: Number(currentPosition.lat),
        lng: Number(currentPosition.lng),
      });
      setZoom(16);
    } else {
      // 检查浏览器是否支持 Geolocation API
      if ("geolocation" in navigator) {
        // 获取当前位置
        navigator.geolocation.getCurrentPosition(
          function (position) {
            var latitude = position.coords.latitude;
            var longitude = position.coords.longitude;

            const currentPosition = {
              lat: latitude,
              lng: longitude,
            };
            setCoordinates(currentPosition);
          },
          function (error) {
            setCoordinates({
              lat: 34.932032320983154,
              lng: 103.8510534864727,
            });
            setZoom(16);

            // console.error("无法获取当前位置：", error);
          }
        );
      } else {
        setCoordinates({
          lat: 34.932032320983154,
          lng: 103.8510534864727,
        });
        // console.error("浏览器不支持 Geolocation API");
      }
    }

    setOpen(true);
  };

  // 页面关闭事件
  const handleClose = () => {
    setSearchLocation(null);
    setOpen(false);
  };

  //获取地图中的经纬度传递给storeForm
  const handleLatlng = (lat, lng, addressData, cityComponent) => {
    let country, province, city, district;
    cityComponent.forEach((component) => {
      component.types.forEach((type) => {
        if (type === "country") {
          country = component.long_name;
        } else if (type === "administrative_area_level_1") {
          province = component.long_name;
        } else if (
          type === "locality" ||
          type === "administrative_area_level_2"
        ) {
          city = component.long_name;
        } else if (type === "postal_code") {
          district = component.long_name;
        }
      });
    });
    const location = {
      addressComponents: {
        country: country,
        province: province,
        city: city,
        district: district,
      },
      address: addressData,
    };
    // console.log(country, province, city);
    getLocation(lat, lng, location);
    setSearchLocation(null);
    setOpen(false);
  };

  return (
    <>
      <BootstrapDialog
        fullWidth
        maxWidth="lg"
        aria-labelledby="customized-dialog-title"
        open={open}>
        <BootstrapDialogTitle
          id="customized-dialog-title"
          onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("common.common_select_location")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent>
          <Grid
            container
            direction="row"
            spacing={0}
            justifyContent="space-between">
            <Grid item xs={12}>
              <Grid
                item
                container
                direction="row"
                justifyContent="flex-end"
                xs={12}>
                <Grid
                  item
                  container
                  xs={3}
                  direction="row"
                  justifyContent="flex-end"
                  alignItems="center">
                  <MapInput
                    setZoom={setZoom}
                    onDetailAddress={handlerDetailAddress}
                    placeholder={t("common.common_input_location_search")}
                    sx={{
                      height: "50px",
                      width: "100%",
                      ".MuiAutocomplete-endAdornment": { textAlign: "center" },
                    }}></MapInput>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} mt={2}>
              <MapChart
                ref={mapRef}
                defaultLocation={coordinates}
                searchLocationAddress={searchLocation}
                zoom={zoom}
                onCoordinatesChange={handleLatlng}
                storeInfo={storeInfo}
              />
            </Grid>
            {/* </Grid> */}
          </Grid>
        </BootstrapContent>
      </BootstrapDialog>
    </>
  );
});

export default selectMap;
