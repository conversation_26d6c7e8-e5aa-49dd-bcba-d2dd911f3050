import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setMenuList } from "@/store/reducers/menu";
export const useStateMenuList = () =>
  useSelector((store) => store.menu.menuList);

export function useDispatchMenu() {
  const dispatch = useDispatch();
  const stateSetMenuList = useCallback(
    (menuList) => dispatch(setMenuList(menuList)),
    [dispatch]
  );

  return { stateSetMenuList };
}
