import React from "react";
import { pxToRem } from "@u/zkUtils";
import { useLocation } from "react-router-dom";
function MenuTreeCard(props) {
  const { menuList, selectedNode, onChange } = props;
  const { state } = useLocation();
  // 渲染节点
  const renderNode = (node, level = 0) => {
    return (
      <div key={node.id}>
        <Grid container key={node.id}>
          <FormGroup
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              gap: 2,
              ml: level * 4, // 根据层级调整缩进
            }}
          >
            <Grid item lg={2} display={"flex"}>
              <FormControlLabel
                control={
                  <Checkbox
                    disabled={state?.type == "view"}
                    id={`permission-checkbox-${node.id}`}
                    checked={selectedNode?.includes(node.id)}
                    onChange={() => onChange(node.id, node?.parentId)}
                  />
                }
                label={node.name}
                sx={{
                  textAlign: "center",
                  font: "normal normal medium 18px/24px Roboto",
                  fontWeight: "bold",
                  whiteSpace: "nowrap",
                  color: "#474B4F",
                }}
              />
            </Grid>
          </FormGroup>
        </Grid>

        {/* 如果有子节点，继续渲染 */}
        <div
          style={{
            display: "flex",
            flexWrap: "wrap",
            marginTop: "5px",
          }}
        >
          {node.children &&
            node.children.map((child) => renderNode(child, level + 1))}
        </div>
      </div>
    );
  };

  return (
    <React.Fragment>
      {menuList?.map((item, index) => {
        return (
          <Card
            elevation={0}
            sx={{
              fontWeight: "bold",
              borderRadius: "8px",
              mt: 5,
            }}
            key={index}
          >
            {/* 主节点 */}
            <FormGroup
              sx={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                background: "#F4F4F7  0% 0% no-repeat padding-box",
                boxShadow: "#F4F4F7 0% 0% no-repeat padding-box",
                pl: 6,
              }}
            >
              <Grid item lg={2} ml={4}>
                <FormControlLabel
                  key={item?.id}
                  control={
                    <Checkbox
                      disabled={state?.type == "view"}
                      id={`permission-checkbox-${item?.id}`}
                      checked={selectedNode?.includes(item?.id)}
                      onChange={() => onChange(item?.id, item.parentId)}
                    />
                  }
                  label={item?.name}
                  sx={{
                    textAlign: "left",
                    font: "normal normal medium 28px/38px Roboto",
                    fontWeight: "bold",
                    height: pxToRem(55),
                    color: "#474B4F",
                  }}
                />
              </Grid>
            </FormGroup>

            {/* 子节点 */}
            {item?.children && item?.children.length > 0 && (
              <CardContent
                sx={{
                  textAlign: "left",
                  font: "normal normal medium 32px/38px Roboto",
                  color: "#474B4F",
                  border: "1px solid #c186861a",
                  boxShadow: "inset -2px -3px 6px #9147471a",
                  borderRadius: "0 0 15px 15px",
                  pl: 14,
                }}
              >
                {item?.children?.map((child) => renderNode(child))}
              </CardContent>
            )}
          </Card>
        );
      })}
    </React.Fragment>
  );
}

export default MenuTreeCard;
