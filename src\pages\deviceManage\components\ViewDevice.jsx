import React, { forwardRef, useEffect, useState, useRef } from "react";
import {
  Box,
  Stack,
  IconButton,
  Grid,
  Button,
  Divider,
  Typography,
} from "@mui/material";
import MainCard from "@/components/MainCard";
import DictTag from "@/components/DictTag";
import { useNavigate, useLocation } from "react-router-dom";
import Descriptions from "@/components/descriptions";
import { useTranslation } from "react-i18next";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { screenStatus, deviceTypies, adbSwitchTab } from "@/dict/commonDict";
import TabContext from "@mui/lab/TabContext";
import TabPanel from "@mui/lab/TabPanel";
import { getDeviceDetail } from "@s/api/device.js";
import useDict from "@/hooks/useDict.js";
const ViewDevice = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [data, setData] = useState({});
  const dicts = useDict(["dev_device_status"]);
  const handleGetScreenInfo = async () => {
    await getDeviceDetail(state?.id).then((res) => {
      const { data } = res;
      setData(res?.data);
    });
  };

  useEffect(() => {
    handleGetScreenInfo();
  }, []);

  return (
    <>
      <MainCard
        divider={false}
        title={
          <Stack direction="row" alignItems="center" spacing={1}>
            <IconButton
              onClick={() => {
                navigate(-1);
              }}>
              <ArrowBackIcon />
            </IconButton>
            <Stack
              direction="row"
              justifyContent="center"
              alignItems="center"
              divider={<Divider orientation="vertical" flexItem />}
              spacing={1}>
              <Typography variant="h4" component="p">
                {t("device.device_detail")}
              </Typography>
            </Stack>
          </Stack>
        }
        border={false}>
        <TabContext>
          <TabPanel sx={{ padding: "10px" }}>
            <Descriptions
              title={null}
              bordered
              size="large"
              colon={false}
              column={2}>
              <Descriptions.Item label={t("ips.ips_device")}>
                {data.name ? data.name : "-"}
              </Descriptions.Item>
              <Descriptions.Item label={t("ips.ips_device_sn")}>
                {data.sn ? data.sn : "-"}
              </Descriptions.Item>
              <Descriptions.Item label={t("ips.ips_device_online")}>
                {data.status ? (
                  <DictTag
                    dicts={dicts.current?.dev_device_status}
                    fieldName={{
                      value: "value",
                      title: "label",
                      color: "color",
                    }}
                    value={String(data.status)}
                  />
                ) : (
                  "-"
                )}
              </Descriptions.Item>
              <Descriptions.Item label={t("ips.ips_fwversion")}>
                {data.fwVersion ? data.fwVersion : "-"}
              </Descriptions.Item>
              <Descriptions.Item label={t("ips.ips_screen_model")}>
                {data.deviceModel ? data.deviceModel : "-"}
              </Descriptions.Item>
              <Descriptions.Item label={t("common.common_deviceType")}>
                {data.deviceType !== null ? (
                  <DictTag
                    dicts={deviceTypies}
                    fieldName={{
                      value: "value",
                      title: "label",
                      listClass: "listClass",
                    }}
                    value={String(data.deviceType)}
                  />
                ) : (
                  "-"
                )}
              </Descriptions.Item>
              <Descriptions.Item label={t("ips.ips_type")}>
                {data.dmsDeviceType ? data.dmsDeviceType : "-"}
              </Descriptions.Item>
              <Descriptions.Item label={t("common.common_macAddress")}>
                {data.mac ? data.mac : "-"}
              </Descriptions.Item>
              <Descriptions.Item label={t("ips.ips_device_ip")}>
                {data.ip ? data.ip : "-"}
              </Descriptions.Item>
              <Descriptions.Item label={t("ips.ips_screen_alias")}>
                {data.deviceAlias ? data.deviceAlias : "-"}
              </Descriptions.Item>
              <Descriptions.Item label={t("common.common_outlet_owner")}>
                {data.outletName ? data.outletName : "-"}
              </Descriptions.Item>
              <Descriptions.Item label={t("device.device_protocolType")}>
                {data.protocolType ? data.protocolType : "-"}
              </Descriptions.Item>
            </Descriptions>
          </TabPanel>
        </TabContext>

        <Grid
          container
          justifyContent="flex-end"
          alignItems="center"
          sx={{ marginTop: 2 }}>
          <Button
            color="info"
            variant="outlined"
            onClick={() => {
              navigate("/device/manage/list");
            }}>
            {t("common.common_op_return")}
          </Button>
        </Grid>
      </MainCard>
    </>
  );
});

export default ViewDevice;
