/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/rules-of-hooks */
/**
 * @param {string} pathname 当前路由路径
 * @param {object} meta 当前路由自定义meta字段
 * @return {string} 需要跳转到其他页时，就返回一个该页的path路径，或返回resolve该路径的promise对象
 */
import { getToken } from "@/utils/auth";
import { getLoginInfor } from "@/service/api/user";
import { useDispatch, useSelector } from "react-redux";
import { getMenuList } from "@s/api/premission";
import { store } from "@/store";
import { setMenuList } from "@/store/reducers/menu";
import i18n from "i18next";
import { setInfoLoaded } from "@/store/reducers/user";
import { useDispatchUser } from "@/hooks/user.js";
import { isMicroAppPath } from "@/components/MicroAppContainer";
import { toast } from 'react-toastify';
const flattenTree = (nodes) => {
  let result = [];
  nodes.forEach((node) => {
    result.push(node.id);
    if (node.children) {
      let ids = flattenTree(node.children);
      result = [...result, ...ids];
    }
  });
  return result;
};

const onRouteBefore = async ({ pathname, meta }) => {
  // 如果是微前端路由，直接放行
  if (pathname.startsWith('/cms-app') ||
    pathname.startsWith('/retail-ai-app') ||
    pathname.startsWith('/e-price-tag-app')) {
    return;
  }

  // 如果是错误页面，直接放行
  if (pathname === '/404' || pathname === '/403' ||
    pathname === '/500' || pathname === '/network-error') {
    return;
  }

  // 动态修改页面标题
  if (meta.i18n) {
    document.title = i18n.t("menu." + meta.i18n);
  }

  const { isInfoLoaded, userInfor } = store.getState().user;
  const dispatch = useDispatch();

  const { stateSetPermission, stateSetUser } = useDispatchUser()

  // 示例：动态修改页面title
  if (meta.i18n !== undefined) {
    document.title = i18n.t("menu." + meta?.i18n);
  }

  try {
    // 示例：判断未登录跳转登录页
    if (meta.id || meta.idEditor) {
      if (!meta.idEditor && !getToken()) {
        return "/login";
      } else {
        if (!userInfor || !isInfoLoaded) {
          try {
            const [userRes, menuRes] = await Promise.all([
              getLoginInfor(),
              getMenuList({ applicationCode: "L3" }),
            ]);

            if (userRes?.data && menuRes?.data) {
              dispatch(stateSetUser(userRes.data));
              dispatch(stateSetPermission(userRes?.data?.permissions));
              dispatch(setMenuList(menuRes.data));
              dispatch(setInfoLoaded(true));

              // 成功加载用户信息和菜单后，不做任何跳转，让路由正常进行
              return;
            } else {
              return "/login";
            }
          } catch (err) {
            console.error("Error fetching user info or menus:", err);
            // 不要直接跳转到500页面，而是返回登录页
            toast.error("获取用户信息失败，请重新登录");
            return "/login";
          }
        }
      }
    }
  } catch (error) {
    console.error("路由守卫错误:", error);
    // 不要直接跳转到500页面，而是返回登录页
    toast.error("路由守卫错误，请重新登录");
    return "/login";
  }
};
export default onRouteBefore;
