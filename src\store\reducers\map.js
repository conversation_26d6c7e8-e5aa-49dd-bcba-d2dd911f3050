// import { createSlice } from '@reduxjs/toolkit';
// import { getLoginInfor } from '@/service/api/user';
// // 初始化状态
// const initialState = {
//     currentLocation: {
//         lat: 24.608642112444407,
//         lng: 118.0361120933512
//     }
// };

// // 创建分片
// const user = createSlice({
//     name: 'map',
//     initialState,
//     reducers: {
//         setCurrentLocation(state, action) {
//             console.log('action.payload', action);
//             state.currentLocation = action.payload;
//         }
//     }
// });

// // 导出
// export default user.reducer;

// export const { setCurrentLocation } = user.actions;
