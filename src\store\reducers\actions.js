import { getDictByType } from "@/service/api/dict";
import { setDicts } from "./dict";

// action - account reducer
export const LOGIN = "@auth/LOGIN";
export const LOGOUT = "@auth/LOGOUT";
export const REGISTER = "@auth/REGISTER";

export const fetchDictionary = (type) => async (dispatch) => {
  try {
    const { data } = await getDictByType(type);


    dispatch(setDicts({ type, data }));
  } catch (error) {
    console.error(`获取字典失败 ${type}:`, error);
  }
};
