import { getTenantUserDetail } from "@s/api/tenant";

export const getBranchDetail = (
  id,
  setDetailData,
  setImageUrl,
  setSelectRole
) => {
  getTenantUserDetail(id).then((res) => {
    let data = res?.data;
    setImageUrl(data?.photo);
    setDetailData(data);

    const safeSelectRole = Array.isArray(data?.roles) ? data?.roles : []
    setSelectRole(safeSelectRole || []);

  });
};

export const getFormConfig = (t, type, permissionList) => {
  let formConfig = [
    {
      name: "firstName",
      label: t("branch_user.firstName"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch_user.enter_firstName"),
        },
      ],
    },
    {
      name: "lastName",
      label: t("branch_user.lastName"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch_user.enter_lastName"),
        },
      ],
    },

    {
      name: "email",
      label: t("branch_user.email"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch_user.enter_email"),
        },
        {
          type: "email",
          message: t("outlets.invalid_email_format"),
        },
      ],
    },
    {
      codename: "countryCode", // 对应区号字段名
      name: "phone", // 对应电话号码字段名
      label: t("common.common_mobile"),
      type: "mobile",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_required_mobile"),
        },
        {
          type: "matches",
          matches: /^\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}$/,
          message: t("common.common_mobile_format"),
        },
      ],
    },

    {
      name: "password",
      label: t("common.common_password"),
      type: "password",
      viewPwd: true,
      required: type !== "editor" ? true : false,
      display: type !== "editor" ? true : false,
      validation:
        type !== "editor"
          ? [
            {
              type: "string",
              message: "",
            },
            {
              type: "required",
              message: t("common.common_required_password"),
            },
            {
              type: "matches",
              matches: /^(?=(?:.*[A-Z]){1})(?=(?:.*[a-z]){1})(?=(?:.*\d){1})(?=(?:.*[^\w\s]){1})(?!.*[\s\u{1F300}-\u{1F9FF}\u{2600}-\u{27BF}\u{1F1E6}-\u{1F1FF}]).{8,64}$/u,
              message: t("common.common_format"),
            },
          ]
          : null,
    },

    {
      name: "confirmPassword",
      label: t("common.common_confirm_password"),
      type: "password",
      viewPwd: true,
      display: type !== "editor" ? true : false,
      required: type !== "editor" ? true : false,
      validation:
        type !== "editor"
          ? [
            {
              type: "string",
              message: "",
            },
            {
              type: "required",
              message: t("common.common_required_confirm_password"),
            },
            {
              type: "secondConfirm",
              ref: "password",
              message: t("common.common_confirm_password_not_match"),
            },
          ]
          : null,
    },

    {
      name: "dataScopeId",
      label: t("data_permission.add_permission"),
      type: "autoComplate",
      options: permissionList,
      typevalue: 5,
      placeholder: t("data_permission.select_type"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("data_permission.required"),
        },
      ],
    },
  ];

  return formConfig;
};
