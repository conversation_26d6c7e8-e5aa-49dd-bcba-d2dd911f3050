import request from "@/utils/request";

const baseProfixURI = `${import.meta.env.VITE_APICODE}/partner`;

/**
 *
 * @param {获取代理商分页列表} params
 * @returns
 */
export const getParnerList = (params) => {
  return request({
    url: `${baseProfixURI}/query/page`,
    method: "get",
    params: params,
  });
};

export const addParner = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "POST",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const editParner = (data) => {
  return request({
    url: `${baseProfixURI}`,
    method: "PUT",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const deleteParner = (ids) => {
  return request({
    url: `${baseProfixURI}/${ids}`,
    method: "DELETE",
  });
};

export const getParnerDetail = (id) => {
  return request({
    url: `${baseProfixURI}/query/${id}`,
    method: "get",
  });
};

/**
 * -------------------------------------------------------------------------------
 *
 *                 以下为 Paener 用户接口
 */

export const getParnerListUser = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/partner_employee/query/page`,
    method: "get",
    params: params,
  });
};

export const addParnerUser = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/partner_employee`,
    method: "POST",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const editParnerUser = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/partner_employee`,
    method: "PUT",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const deleteParnerUser = (ids) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/partner_employee/${ids}`,
    method: "DELETE",
  });
};

export const getParnerDetailUser = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/partner_employee/query/${id}`,
    method: "get",
  });
};
