const common = {
  zoontime: {
    International: "(GMT-12:00) International Date Line West",
    Coordinated: "(UTC-11)Coordinated Universal Time-11",
    Hawaii: "(UTC-10)Hawaii",
    Alaska: "(UTC-9)Alaska",
    California: "(UTC-8)Pacific time (American and Canada) Baja California",
    Arizona: "(UTC-7)La Paz, The mountain time (American and Canada), Arizona",
    America: "(UTC-6)Saskatchewan, Central time, Central America",
    Eastern:
      "(UTC-5)Bogota, Lima, Quito, Rio Branco, Eastern time, Indiana(East)",
    Caracas: "(UTC-4:30)Caracas",
    Atlantic: "(UTC-4)Atlantic time, Cuiaba, Georgetown, La Paz, Santiago",
    Newfoundland: "(UTC-3:30)Newfoundland",
    Brasilia: "(UTC-3)Brasilia, Buenos Aires, Greenland, Cayenne",
    International2: "(UTC-2)The International Date Line West-02",
    Azores: "(UTC-1)Cape Verde Islands, Azores",
    Edinburgh:
      "(UTC)Dublin, Edinburgh, Lisbon, London, The International Date Line West",
    Brussels: "(UTC+1)Amsterdam, Brussels, Sarajevo",
    Damascus:
      "(UTC+2)Beirut, Damascus, Eastern Europe, Cairo,Athens, Jerusalem",
    Kuwait: "(UTC+3)Baghdad, Kuwait, Moscow, St Petersburg,Nairobi",
    Tehran: "(UTC+3:30)Teheran or Tehran",
    Yerevan: "(UTC+4)Abu Dhabi, Yerevan, Baku, Port Louis, Samarra",
    Kabul: "(UTC+4:30)Kabul",
    Karachi: "(UTC+5)Ashgabat, Islamabad, Karachi",
    Calcutta: "(UTC+5:30)Chennai, Calcutta Mumbai, New Delhi",
    Kathmandu: "(UTC+5:45)Kathmandu",
    Novosibirsk: "(UTC+6)Astana, Dhaka, Novosibirsk",
    Yangon: "(UTC+6:30)Yangon",
    Jakarta: "(UTC+7)Bangkok, Hanoi, Jakarta",
    Beijing:
      "(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi,Kuala Lumpur, Singapore",
    Yakutsk: "(UTC+9)Osaka, Tokyo, Seoul, Yakutsk",
    Adelaide: "(UTC+9:30)Adelaide, Darwin",
    Canberra: "(UTC+10)Brisbane, Vladivostok, Guam, Canberra",
    Islands: "(UTC+11)Solomon Islands, New Caledonia",
    Oakland: "(UTC+12)Anadyr, Oakland, Wellington, Fiji",
    alofa: "(UTC+13)Nuku'alofa, The Samoa Islands",
    Island: "(UTC+14)Christmas Island",
  },

  common: {
    common_refresh_success: "Refresh Success",
    common_edit_ok: "Ok",
    common_edit_cancel: "Cancel",
    common_delete: "Delete",
    common_confirm: "Confirm",
    common_confirm_delete: "Are you sure you want to delete?",
    common_confirm_delete_tip: "This operation cannot be undone",
    common_relatedOp: "Operation",
    common_loading_error: "Error loading data",
    common_rule_area_len60:
      "The length of the region name cannot exceed 60 digits",
    common_area_name_regTip:
      "Os nomes de região só podem começar com letras, caracteres chineses, e só podem conter letras, números, espaços chineses e sublinhados",
    common_area_name_not_null: "The area cannot be empty",
    common_area_center_location: "Region center point location",
    common_please_area_center_location:
      " Please select the region center point location.",
    common_input_sort_null: "Sort cannot be empty",
    common_connot_lower_zero: "Cannot be lower than 0",
    common_latitude: "Latitude",
    common_submit: "Submit",
    common_Longitude: "Longitude",
    common_online: "Online",
    common_offline: "Offline",
    common_op_return: "Back",
    common_success: "Successful",
    common_failed: "Failed",
    common_search_name: "Please enter name",
    common_delete_confirm: "Delete Confirmation",
    common_delete_sure:
      "Are you sure you want to permanently delete this authorization level?",
    common_delete_not:
      "This authorization level is in use and cannot be deleted.",
    common_password: "Password",
    common_required_password: "Password is required",
    common_format:
      "Must contain uppercase and lowercase letters, numbers, and special characters, and be between 8 and 64 characters in length.",
    common_mobile: "Contact Number",
    common_required_mobile: "Phone number is required",
    common_mobile_enter: "Please enter contact number",
    common_mobile_format: "Invalid phone number format",
    common_comtract_id: "Contract ID",
    common_comtract_id_enter: "Please enter contract ID",
    common_comtract_id_format: "Invalid contract ID format",
    common_comtract_id_not_null: "Contract ID cannot be empty",
    common_comtract_amount: "Contract Amount",
    common_required_comtract_amount: "Please enter contract amount",
    common_contract_unit: "Contract Unit",
    common_required_contract_unit: "Please select contract unit",
    common_query: "Query",
    common_reset: "Reset",
    common_enter_mobile: "Please enter mobile number",
    common_enter: "Please enter",
    common_company_logo: "Company Logo",
    common_maximum: "File size maximum is 5MB",
    common_upload: "Upload",
    common_remove: "Remove",
    common_allowed: "Only JPEG, JPG, PNG allowed",
    common_view: "Preview",
    common_editor: "Edit",
    common_user_setting: "Employee Manegement",
    common_delete: "Delete",
    common_send_email: "Send Email",
    common_union: "Retail Main Data",
    common_confirm_password: "Confirm Password",
    common_required_confirm_password: "Confirm password is required",
    common_confirm_password_format:
      "Must contain uppercase and owercase etters, numlersand special characters, and be between 8 and 64 characters inlength.",
    common_confirm_password_not_match:
      "Confirm password does not match the password",
    common_enter_confirm_password: "Please enter confirm password",
    common_perpage: "Records per page",
    common_change_password: "Change Password",
    common_no_content_found: "No Content found.",
    common_click_add_content: "Click on the " + " button to add Content",
    common_language: "Language",
    my_profile: "My Profile",
    my_subscription: "My Subscription",
    about: "About",
    logout: "Logout",
    occupation: "Occupation",
    location: "Location",
    common_email: "Email",
    common_mobile: "Mobile",
    enter_mobile: "Please Enter Mobile",
    enter_email: "Please Enter Branch Owner Email",
    enter_location: "Please Enter Location",
    enter_occupation: "Please Enter Occupation",
    system_service: "Display system service indicators",
    operation_time: "Operation time is 7 days *24 hours per week",
    abnormal_exceed: "The abnormal time does not exceed 8 hours per year",
    reseponse_time: "The service response time does not exceed 4 hours",
    current_password: "Current password",
    new_password: "New password",
    enter_current_password: "Please enter current password",
    enter_new_password: "Please enter new password",
    enter_confirm_password: "Please enter confirm password",
    common_confirm_password: "Confirm password",
    common_save: "Save",
    common_login: "Login",
    common_base_info: "Base Information",
    common_security: "Security Setting",
    common_org_details: "Organization Details",
    common_switch_org: "Switch Organization",
    common_org_list: "Organization List",
    common_principal_list: "Principal List",
    common_no_data: "No data found",
    common_enter_current_password: "Please enter current password",
    common_enter_new_password: "Please enter new password",
    common_enter_confirm_password: "Please enter confirm password",
    common_password_not_same: "Passwords do not match",
    file_type_error: "File type error",

    common_authorization_level_placeholder: "Please Select Authorization Level",
    common_handover: "Handover",
  },

  errorCode: {
    G0000025: "Failed to read valid token",
    G0000026: "Login account has expired, please log in again",
    default: "Internal service error",
    networkError: "Backend interface connection exception",
    timeout: "System interface request timeout",
    error: "Service temporarily unavailable",
    data_repeat: "Data is being processed, please do not resubmit",
    400: "Bad Request",
    401: "Unauthorized, please log in again",
    403: "Forbidden",
    404: "Request address error",
    408: "Request timeout",
    500: "Internal server error",
    501: "Service not implemented",
    502: "Gateway error",
    503: "Service unavailable",
    504: "Gateway timeout",
  },
};

export default common;
