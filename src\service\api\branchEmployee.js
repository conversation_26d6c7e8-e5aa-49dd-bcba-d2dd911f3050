import request from "@/utils/request";

/**
 *
 * @param {获取租户用户} params
 * @returns
 */
export const getUserList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/branch_employee/query/page`,
    method: "get",
    params: params,
  });
};

export const addUser = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/branch_employee`,
    method: "POST",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const editUser = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/branch_employee`,
    method: "PUT",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const deleteUser = (ids) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/branch_employee/${ids}`,
    method: "DELETE",
  });
};

export const getUserDetail = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/branch_employee/query/${id}`,
    method: "get",
  });
};
