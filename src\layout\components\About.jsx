/* eslint-disable no-constant-condition */
import React, { useEffect, useState } from "react";
import {
  <PERSON>tra<PERSON>D<PERSON>og,
  BootstrapContent,
  BootstrapActions,
  BootstrapDialogTitle,
} from "@/components/dialog";
import {
  Stack,
  Skeleton,
  Card,
  Grid,
  Button,
  Typography,
  CircularProgress,
} from "@mui/material";
import enLogo from "@/assets/Icons/about_logo_en.svg";
import zhLogo from "@/assets/Icons/about_logo_zh.svg";
import { styled } from "@mui/material/styles";
import { getStoreLang } from "@/utils/langUtils";
// import { userSubscribeInfo } from "@/service/api/user";
import LinearProgress, {
  linearProgressClasses,
} from "@mui/material/LinearProgress";
const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 10,
  borderRadius: 5,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor:
      theme.palette.grey[theme.palette.mode === "light" ? 200 : 800],
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 5,
    backgroundColor: theme.palette.primary.main,
    // backgroundColor: theme.palette.mode === "light" ? "#1a90ff" : "#308fe8",
  },
}));

// i18n
import { useTranslation } from "react-i18next";
import { getSystemInfo } from "@/service/api/common";
import useLoading from "@/hooks/useLoading";
import { useTheme } from "@mui/material/styles";
export default function About({ open, onClose = () => {}, setOpen }) {
  const theme = useTheme();
  const [loading, toggleLoading] = useLoading();
  const [logoSrc, setLogoSrc] = useState();
  const { t } = useTranslation();

  useEffect(() => {
    const lang = getStoreLang();
    if (lang == "en" || lang == "es") {
      setLogoSrc(enLogo);
    } else {
      setLogoSrc(zhLogo);
    }
  }, []);
  const [info, setInfo] = useState({});
  // const { systemInfo } = useStateSystemBaseInfo();
  useEffect(() => {
    if (open) {
      getSystemInfo()
        .then((res) => {
          setInfo(res.data);
          setTimeout(() => {
            toggleLoading(); // 关闭加载状态
          }, 500);
        })
        .catch(() => {
          toggleLoading(); // 关闭加载状态
        });
      return () => {
        toggleLoading(); // 关闭加载状态
      };
    }

    toggleLoading();
  }, [open]);
  const [subData, setSubData] = useState({});
  const [subLoading, setSubLoading] = useState(true);
  // useEffect(() => {
  //   if (open) {
  //     setSubLoading(true);
  //     userSubscribeInfo()
  //       .then((res) => {
  //         setSubData(res?.data);
  //         setSubLoading(false);
  //       })
  //       .catch(() => {
  //         setSubLoading(false);
  //       });
  //   }
  // }, [open]);
  // 加载图标的样式
  const loadingStyle = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "calc(400px)",
  };
  const getEffectexpireDayBarColor = () => {
    const value =
      subData.usedExpireDay === -1
        ? 0
        : subData.usedExpireDay == subData.maxExpiredDay
        ? 100
        : (subData.usedExpireDay / subData.maxExpiredDay) * 100;
    console.log(theme.palette.primary.main);

    let color = theme.palette.primary.main; // 默认为绿色
    if (value > 50) {
      color = "yellow"; // 如果超过 50%，则将颜色设置为黄色
    }
    if (value > 80) {
      color = "red"; // 如果超过 80%，则将颜色设置为红色
    }
    return color;
  };
  const getEffectdeviceCountBarColor = () => {
    const value =
      subData.usedDeviceCount === -1
        ? 0
        : subData.usedDeviceCount >= subData.maxDeviceCount
        ? 100
        : (subData.usedDeviceCount / subData.maxDeviceCount) * 100;
    let color = theme.palette.primary.main; // 默认为绿色
    if (value > 50) {
      color = "yellow"; // 如果超过 50%，则将颜色设置为黄色
    }
    if (value > 80) {
      color = "red"; // 如果超过 80%，则将颜色设置为红色
    }
    return color;
  };
  return (
    <BootstrapDialog
      fullWidth
      // maxWidth="md"
      // sx={{ minHeight: "800px" }}
      open={open}
      onClose={() => setOpen(false)}
      aria-labelledby="customized-dialog-title">
      <BootstrapDialogTitle onClose={() => setOpen(false)}>
        <Typography variant="h4" component="p">
          {t("menu.media_personal_about")}
        </Typography>
      </BootstrapDialogTitle>
      <BootstrapContent dividers={true}>
        <div style={loading ? loadingStyle : null}>
          {loading ? (
            <CircularProgress />
          ) : (
            <Grid container spacing={0} sx={{ minHeight: 350 }}>
              <Grid item xs={6}>
                <Grid container>
                  <Grid item xs={12}>
                    <Grid container sx={{ width: "100%" }} spacing={[0, 1]}>
                      <Grid item xs={12}>
                        <Stack
                          direction="column"
                          justifyContent="flex-start"
                          alignItems="flex-start"
                          spacing={0}>
                          <Typography
                            variant="body1"
                            display="block"
                            sx={{ fontWeight: "bold" }}
                            component="p">
                            {t("common.common_system_version")}
                          </Typography>
                          <Typography variant="body2" gutterBottom>
                            {info?.name} {info?.version}
                          </Typography>
                        </Stack>
                      </Grid>
                      <Grid item xs={12}>
                        <Stack
                          direction="column"
                          justifyContent="flex-start"
                          alignItems="flex-start"
                          spacing={0}>
                          <Typography
                            variant="body1"
                            display="block"
                            sx={{ fontWeight: "bold" }}
                            component="p">
                            {t("system.base_system_browsers")}
                          </Typography>
                          <Typography variant="body2" gutterBottom>
                            Firefox 27+/Chrome 33+/Edge
                          </Typography>
                        </Stack>
                      </Grid>
                      <Grid item xs={12}>
                        <Stack
                          direction="column"
                          justifyContent="flex-start"
                          alignItems="flex-start"
                          spacing={0}>
                          <Typography
                            variant="body1"
                            display="block"
                            sx={{ fontWeight: "bold" }}
                            component="p">
                            {t("system.base_system_resolution")}
                          </Typography>
                          <Typography variant="body2" gutterBottom>
                            {t("common.common_system_resoltion_recommond")}
                          </Typography>
                        </Stack>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={6}>
                <Grid item xs={12}>
                  <Stack
                    direction="column"
                    justifyContent="flex-start"
                    alignItems="flex-start"
                    spacing={0}>
                    <Typography
                      variant="body1"
                      display="block"
                      sx={{ fontWeight: "bold" }}
                      component="p">
                      {t("common.system_service")}
                    </Typography>
                    <Typography
                      variant="body2"
                      gutterBottom
                      style={{
                        width: "250px",
                        marginTop: "10px",
                      }}>
                      {t("common.operation_time")}
                    </Typography>

                    <Typography
                      variant="body2"
                      gutterBottom
                      style={{
                        width: "250px",
                        marginTop: "10px",
                      }}>
                      {t("common.abnormal_exceed")}
                    </Typography>

                    <Typography
                      variant="body2"
                      gutterBottom
                      style={{
                        width: "250px",
                        marginTop: "10px",
                      }}>
                      {t("common.reseponse_time")}
                    </Typography>
                  </Stack>
                </Grid>
              </Grid>
            </Grid>
          )}
        </div>
      </BootstrapContent>
      <BootstrapActions>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Stack
              direction="row"
              justifyContent="center"
              alignItems="center"
              spacing={2}>
              <img
                src={logoSrc}
                alt=""
                srcset=""
                style={{
                  width: "80px",
                  height: "20px",
                }}
              />
              <Typography variant="label" display="block" gutterBottom>
                {info?.copyright}
              </Typography>
            </Stack>
          </Grid>
        </Grid>
      </BootstrapActions>
    </BootstrapDialog>
  );
}

//  <Grid container sx={{ width: "100%" }} spacing={[0, 1]}>
//    <Grid item xs={12}>
//      <div>
//        <Typography
//          variant="h4"
//          display="block"
//          sx={{ fontWeight: "bold" }}
//          component="p">
//          {t("common.common_package_details")}
//        </Typography>
//      </div>
//    </Grid>
//    <Grid item xs={12}>
//      <Grid container sx={{ width: "100%" }} spacing={[0, 1]}>
//        <Grid item xs={12}>
//          <Card variant="outlined" sx={{ padding: "10px", height: 90 }}>
//            {subLoading ? (
//              <Stack spacing={0}>
//                <Skeleton />
//                <Skeleton />
//                <Skeleton />
//              </Stack>
//            ) : (
//              <Stack spacing={1}>
//                <Typography
//                  variant="h5"
//                  display="block"
//                  sx={{ fontWeight: "bold" }}
//                  component="p">
//                  {t("common.common_remaining_day")}
//                </Typography>
//                <BorderLinearProgress
//                  variant="determinate"
//                  value={
//                    subData.usedExpireDay === -1
//                      ? 0
//                      : subData.usedExpireDay >= subData.maxExpiredDay
//                      ? 100
//                      : (subData.usedExpireDay / subData.maxExpiredDay) *
//                        100
//                  }
//                  sx={{
//                    marginBottom: 0.3,
//                    "& .MuiLinearProgress-bar": {
//                      backgroundColor: getEffectexpireDayBarColor(),
//                    },
//                  }}
//                />

//                <Stack
//                  justifyContent={"space-between"}
//                  flexDirection={"row"}>
//                  <Typography>
//                    {subData.maxExpiredDay === -1
//                      ? t("common.common_no_limit")
//                      : t("common.common_used_day", {
//                          day: subData.usedExpireDay,
//                        })}
//                  </Typography>
//                  <Typography>
//                    {t("common.common_gross")}
//                    {subData.maxExpiredDay === -1
//                      ? t("common.common_no_limit")
//                      : ` ${subData.maxExpiredDay}${t(
//                          "common.common_day"
//                        )}`}
//                  </Typography>
//                </Stack>
//              </Stack>
//            )}
//          </Card>
//        </Grid>
//        <Grid item xs={12}>
//          <Card variant="outlined" sx={{ padding: "10px", height: 90 }}>
//            {subLoading ? (
//              <Stack spacing={0}>
//                <Skeleton />
//                <Skeleton />
//                <Skeleton />
//              </Stack>
//            ) : (
//              <Stack spacing={1}>
//                <Typography
//                  variant="h5"
//                  display="block"
//                  sx={{ fontWeight: "bold" }}
//                  component="p">
//                  {t("common.common_remaining_device_count")}
//                </Typography>
//                <BorderLinearProgress
//                  variant="determinate"
//                  value={
//                    subData.usedDeviceCount === -1
//                      ? 0
//                      : subData.usedDeviceCount >= subData.maxDeviceCount
//                      ? 100
//                      : (subData.usedDeviceCount /
//                          subData.maxDeviceCount) *
//                        100
//                  }
//                  sx={{
//                    marginBottom: 0.3,
//                    "& .MuiLinearProgress-bar": {
//                      backgroundColor: getEffectdeviceCountBarColor(),
//                    },
//                  }}
//                />
//                <Stack
//                  justifyContent={"space-between"}
//                  flexDirection={"row"}>
//                  <Typography>
//                    {subData.usedDeviceCount === -1
//                      ? t("common.common_no_limit")
//                      : t("common.common_add_device_num", {
//                          count: subData.usedDeviceCount,
//                        })}
//                  </Typography>
//                  <Typography>
//                    {t("common.common_gross")}
//                    {subData.maxDeviceCount === -1
//                      ? t("common.common_no_limit")
//                      : ` ${subData.maxDeviceCount}${t(
//                          "common.common_add_device_dot"
//                        )}`}
//                  </Typography>
//                </Stack>
//              </Stack>
//            )}
//          </Card>
//        </Grid>
//      </Grid>
//    </Grid>
//  </Grid>;
