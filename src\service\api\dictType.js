import request from "@/utils/request";

const baseProfixURI = `${import.meta.env.VITE_APICODE}/dict_type`;

/**
 *  查询字典类型
 */
export const getDictTypeList = (params) => {
  return request({
    url: `${baseProfixURI}/query/page`,
    method: "get",
    params: params,
  });
};


/**
 * 
 * 保存字典类型
 * 
 */

export const saveDictType = (data) => {
  return request({
    url: `${baseProfixURI}`,
    method: "POST",
    data: data,
  });
};

/**
 * 
 * 更新字典类型
 *
 */
export const updateDictType = (data) => {
  return request({
    url: `${baseProfixURI}`,
    method: "put",
    data: data,
  });
};

/**
 * 
 * 删除字典类型
 *
 */
export const deleteDictType = (id) => {
  return request({
    url: `${baseProfixURI}/${id}`,
    method: "delete",
  })
}



/**
 * 根据name 查询 字典类型
 */

export const getDictTypeByName = (name) => {
  return request({
    url: `${baseProfixURI}/query`,
    method: "get",
    params: name
  })
}