/* eslint-disable no-undef */
import React from "react";
import PersonalInfo from "./PersonalInfo";
import SecuritySettings from "./SecuritySettings";

export default function Right() {
  const [value, setValue] = React.useState(0);
  const { t } = useTranslation();
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <>
      <Box>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="basic tabs example"
          sx={{
            background: "white",
            height: "70px",
            display: "flex",
            width: "100%",
          }}
          TabIndicatorProps={{
            style: {
              height: "6px", // 设置选中 Tab 下方线条的高度
            },
          }}
        >
          <Tab
            label={t("system.system_user_info")}
            {...a11yProps(0)}
            sx={{
              flex: "1", // 每个 Tab 自动占满父容器的 50%
              fontSize: "20px",
              fontWeight: "bold",
              maxWidth: "100%",
              textAlign: "center", // 确保内容居中
              "&.Mui-selected": {
                fontWeight: "bold", // 选中时字体加粗（可选）
              },
            }}
          />
          <Tab
            label={t("common.common_security_settings")}
            {...a11yProps(1)}
            sx={{
              flex: "1", // 每个 Tab 自动占满父容器的 50%
              fontSize: "20px",
              fontWeight: "bold",
              maxWidth: "100%",
              textAlign: "center", // 确保内容居中
              "&.Mui-selected": {
                fontWeight: "bold", // 选中时字体加粗（可选）
              },
            }}
          />
        </Tabs>

        <TabPanel value={value} index={0}>
          <Box
            sx={{
              boxShadow:
                "rgb(145 158 171 / 20%) 0px 0px 2px 0px, rgb(145 158 171 / 12%) 0px 3px 10px -4px",
              width: "100%",
              minHeight: "calc(100vh - 110px)",
              background: "white",
              padding: "20px",
              // padding: "0px 20px 0px 20px",
              borderRadius: "0px 0px 10px 10px",
            }}
          >
            <PersonalInfo></PersonalInfo>
          </Box>
        </TabPanel>

        <TabPanel value={value} index={1}>
          <Box
            sx={{
              boxShadow:
                "rgb(145 158 171 / 20%) 0px 0px 2px 0px, rgb(145 158 171 / 12%) 0px 3px 10px -4px",
              width: "100%",
              minHeight: "calc(100vh - 110px)",
              background: "white",
              padding: "20px",
              borderRadius: "0px 0px 10px 10px",
            }}
          >
            <SecuritySettings></SecuritySettings>
          </Box>
        </TabPanel>
      </Box>
    </>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 0, paddingLeft: "0px", paddingRight: "0px" }}>
          {children}
        </Box>
      )}
    </div>
  );
}
