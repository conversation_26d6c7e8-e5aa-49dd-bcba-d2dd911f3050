import React, { useState } from "react";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  Button,
  Divider,
} from "@mui/material";
import { SchemaFormRenderer } from "../index";
import RenderingFromItem from "../../Config/RenderingFromItem";

/**
 * 新旧配置风格对比示例
 */
const ComparisonExample = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [oldFormData, setOldFormData] = useState({});
  const [newFormData, setNewFormData] = useState({});

  // 原有Config风格配置
  const oldConfigStyle = [
    {
      type: "input",
      name: "username",
      label: "用户名",
      required: true,
      placeholder: "请输入用户名",
      sx: 6,
    },
    {
      type: "input",
      name: "email",
      label: "邮箱",
      required: true,
      placeholder: "请输入邮箱",
      sx: 6,
    },
    {
      type: "password",
      name: "password",
      label: "密码",
      required: true,
      placeholder: "请输入密码",
      sx: 12,
      viewPwd: true,
    },
    {
      type: "select",
      name: "gender",
      label: "性别",
      options: [
        { label: "男", value: "male" },
        { label: "女", value: "female" },
        { label: "其他", value: "other" },
      ],
      sx: 6,
    },
    {
      type: "switch",
      name: "newsletter",
      label: "订阅邮件",
      sx: 6,
    },
    {
      type: "textArea",
      name: "bio",
      label: "个人简介",
      placeholder: "请输入个人简介",
      sx: 12,
    },
  ];

  // 新Schema风格配置
  const newSchemaStyle = {
    type: "object",
    title: "用户注册表单",
    properties: {
      basicInfo: {
        layout: {
          type: "card",
          title: "基础信息",
          description: "请填写您的基础信息",
        },
        fields: [
          {
            name: "username",
            type: "string",
            title: "用户名",
            required: true,
            minLength: 3,
            maxLength: 20,
            pattern: "^[a-zA-Z0-9_]+$",
          },
          {
            name: "email",
            type: "string",
            format: "email",
            title: "邮箱",
            required: true,
          },
          {
            name: "password",
            type: "string",
            format: "password",
            title: "密码",
            required: true,
            minLength: 6,
          },
        ],
      },
      personalInfo: {
        layout: {
          type: "grid",
          columns: 2,
          title: "个人信息",
        },
        fields: [
          {
            name: "gender",
            type: "string",
            title: "性别",
            widget: "radio",
            enum: ["male", "female", "other"],
            enumNames: ["男", "女", "其他"],
          },
          {
            name: "newsletter",
            type: "boolean",
            title: "订阅邮件通知",
            default: false,
          },
        ],
      },
      additionalInfo: {
        layout: {
          type: "group",
          title: "附加信息",
        },
        fields: [
          {
            name: "bio",
            type: "string",
            widget: "textarea",
            title: "个人简介",
            maxLength: 500,
          },
        ],
      },
    },
  };

  // 新Schema的UI配置
  const uiSchema = {
    username: {
      placeholder: "请输入用户名",
      help: "用户名只能包含字母、数字和下划线",
    },
    email: {
      placeholder: "请输入邮箱地址",
    },
    password: {
      placeholder: "请输入密码",
      showPasswordToggle: true,
    },
    bio: {
      placeholder: "请输入个人简介",
      showCharacterCount: true,
      rows: 4,
    },
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ maxWidth: 1200, margin: "0 auto", padding: 3 }}>
      <Typography variant="h4" gutterBottom>
        配置化组件风格对比
      </Typography>

      <Typography variant="body1" paragraph>
        这个示例展示了原有Config风格和新Schema风格的差异，帮助理解两种配置方式的优缺点。
      </Typography>

      <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
        <Tab label="原有Config风格" />
        <Tab label="新Schema风格" />
        <Tab label="配置对比" />
      </Tabs>

      {/* 原有Config风格 */}
      {activeTab === 0 && (
        <Box>
          <Typography variant="h5" gutterBottom>
            原有Config风格
          </Typography>

          <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
            <Grid container spacing={2}>
              <RenderingFromItem
                config={oldConfigStyle}
                formik={{
                  values: oldFormData,
                  errors: {},
                  touched: {},
                  handleChange: (e) => {
                    setOldFormData((prev) => ({
                      ...prev,
                      [e.target.name]: e.target.value,
                    }));
                  },
                  handleBlur: () => {},
                  setFieldValue: (name, value) => {
                    setOldFormData((prev) => ({
                      ...prev,
                      [name]: value,
                    }));
                  },
                }}
                sx={12}
              />
            </Grid>

            <Box sx={{ mt: 3 }}>
              <Button variant="contained" color="primary">
                提交表单
              </Button>
              <Button
                variant="outlined"
                sx={{ ml: 2 }}
                onClick={() => setOldFormData({})}>
                重置
              </Button>
            </Box>
          </Paper>

          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              当前数据：
            </Typography>
            <pre
              style={{
                background: "#f5f5f5",
                padding: "10px",
                borderRadius: "4px",
                overflow: "auto",
                fontSize: "12px",
              }}>
              {JSON.stringify(oldFormData, null, 2)}
            </pre>
          </Paper>
        </Box>
      )}

      {/* 新Schema风格 */}
      {activeTab === 1 && (
        <Box>
          <Typography variant="h5" gutterBottom>
            新Schema风格
          </Typography>

          <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
            <SchemaFormRenderer
              schema={newSchemaStyle}
              uiSchema={uiSchema}
              formData={newFormData}
              onChange={setNewFormData}
            />

            <Box sx={{ mt: 3 }}>
              <Button variant="contained" color="primary">
                提交表单
              </Button>
              <Button
                variant="outlined"
                sx={{ ml: 2 }}
                onClick={() => setNewFormData({})}>
                重置
              </Button>
            </Box>
          </Paper>

          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              当前数据：
            </Typography>
            <pre
              style={{
                background: "#f5f5f5",
                padding: "10px",
                borderRadius: "4px",
                overflow: "auto",
                fontSize: "12px",
              }}>
              {JSON.stringify(newFormData, null, 2)}
            </pre>
          </Paper>
        </Box>
      )}

      {/* 配置对比 */}
      {activeTab === 2 && (
        <Box>
          <Typography variant="h5" gutterBottom>
            配置方式对比
          </Typography>

          <Grid container spacing={3}>
            {/* 原有风格配置 */}
            <Grid item xs={12} md={6}>
              <Paper elevation={2} sx={{ p: 3, height: "100%" }}>
                <Typography variant="h6" gutterBottom color="primary">
                  原有Config风格
                </Typography>

                <Typography variant="subtitle2" gutterBottom>
                  特点：
                </Typography>
                <Box component="ul" sx={{ pl: 2, mb: 2 }}>
                  <li>命令式配置，基于type字段</li>
                  <li>简单直接，学习成本低</li>
                  <li>硬编码的if-else逻辑</li>
                  <li>布局能力有限</li>
                  <li>扩展性较差</li>
                </Box>

                <Typography variant="subtitle2" gutterBottom>
                  配置示例：
                </Typography>
                <pre
                  style={{
                    background: "#f5f5f5",
                    padding: "10px",
                    borderRadius: "4px",
                    overflow: "auto",
                    fontSize: "11px",
                    maxHeight: "300px",
                  }}>
                  {JSON.stringify(oldConfigStyle.slice(0, 3), null, 2)}
                </pre>
              </Paper>
            </Grid>

            {/* 新Schema风格配置 */}
            <Grid item xs={12} md={6}>
              <Paper elevation={2} sx={{ p: 3, height: "100%" }}>
                <Typography variant="h6" gutterBottom color="secondary">
                  新Schema风格
                </Typography>

                <Typography variant="subtitle2" gutterBottom>
                  特点：
                </Typography>
                <Box component="ul" sx={{ pl: 2, mb: 2 }}>
                  <li>声明式配置，基于JSON Schema</li>
                  <li>类型安全，强验证能力</li>
                  <li>插件化架构，易扩展</li>
                  <li>丰富的布局组件</li>
                  <li>强大的条件逻辑</li>
                </Box>

                <Typography variant="subtitle2" gutterBottom>
                  配置示例：
                </Typography>
                <pre
                  style={{
                    background: "#f5f5f5",
                    padding: "10px",
                    borderRadius: "4px",
                    overflow: "auto",
                    fontSize: "11px",
                    maxHeight: "300px",
                  }}>
                  {JSON.stringify(newSchemaStyle.properties.basicInfo, null, 2)}
                </pre>
              </Paper>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          {/* 对比表格 */}
          <Paper elevation={1} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              详细对比
            </Typography>

            <Box sx={{ overflowX: "auto" }}>
              <table style={{ width: "100%", borderCollapse: "collapse" }}>
                <thead>
                  <tr style={{ backgroundColor: "#f5f5f5" }}>
                    <th
                      style={{
                        padding: "12px",
                        textAlign: "left",
                        border: "1px solid #ddd",
                      }}>
                      特性
                    </th>
                    <th
                      style={{
                        padding: "12px",
                        textAlign: "left",
                        border: "1px solid #ddd",
                      }}>
                      原有Config风格
                    </th>
                    <th
                      style={{
                        padding: "12px",
                        textAlign: "left",
                        border: "1px solid #ddd",
                      }}>
                      新Schema风格
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td
                      style={{
                        padding: "12px",
                        border: "1px solid #ddd",
                        fontWeight: "bold",
                      }}>
                      配置方式
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      命令式，基于type字段
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      声明式，基于JSON Schema
                    </td>
                  </tr>
                  <tr>
                    <td
                      style={{
                        padding: "12px",
                        border: "1px solid #ddd",
                        fontWeight: "bold",
                      }}>
                      类型安全
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      弱类型，运行时检查
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      强类型，Schema验证
                    </td>
                  </tr>
                  <tr>
                    <td
                      style={{
                        padding: "12px",
                        border: "1px solid #ddd",
                        fontWeight: "bold",
                      }}>
                      扩展性
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      硬编码if-else，难扩展
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      插件化注册机制，易扩展
                    </td>
                  </tr>
                  <tr>
                    <td
                      style={{
                        padding: "12px",
                        border: "1px solid #ddd",
                        fontWeight: "bold",
                      }}>
                      布局能力
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      简单Grid布局
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      多种布局组件（Group、Card、Tabs等）
                    </td>
                  </tr>
                  <tr>
                    <td
                      style={{
                        padding: "12px",
                        border: "1px solid #ddd",
                        fontWeight: "bold",
                      }}>
                      条件逻辑
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      基础条件渲染
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      丰富的条件表达式
                    </td>
                  </tr>
                  <tr>
                    <td
                      style={{
                        padding: "12px",
                        border: "1px solid #ddd",
                        fontWeight: "bold",
                      }}>
                      验证机制
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      依赖外部验证（如Formik）
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      内置Schema验证
                    </td>
                  </tr>
                  <tr>
                    <td
                      style={{
                        padding: "12px",
                        border: "1px solid #ddd",
                        fontWeight: "bold",
                      }}>
                      维护性
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      代码重复，难维护
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      配置驱动，易维护
                    </td>
                  </tr>
                  <tr>
                    <td
                      style={{
                        padding: "12px",
                        border: "1px solid #ddd",
                        fontWeight: "bold",
                      }}>
                      学习成本
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      低，简单直接
                    </td>
                    <td style={{ padding: "12px", border: "1px solid #ddd" }}>
                      中等，需要了解JSON Schema
                    </td>
                  </tr>
                </tbody>
              </table>
            </Box>
          </Paper>
        </Box>
      )}
    </Box>
  );
};

export default ComparisonExample;
