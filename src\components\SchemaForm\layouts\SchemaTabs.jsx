import React, { useState } from 'react';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Divider
} from '@mui/material';

/**
 * TabPanel组件
 */
const TabPanel = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`schema-tabpanel-${index}`}
      aria-labelledby={`schema-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

/**
 * Schema标签页布局组件
 */
const SchemaTabs = ({
  title,
  description,
  tabs = [],
  children,
  orientation = 'horizontal',
  variant = 'standard',
  indicatorColor = 'primary',
  textColor = 'primary',
  centered = false,
  scrollButtons = 'auto',
  allowScrollButtonsMobile = false,
  divider = false,
  sx,
  ...props
}) => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // 如果没有提供tabs配置，使用children的索引
  const tabsToRender = tabs.length > 0 ? tabs : React.Children.map(children, (child, index) => ({
    label: `Tab ${index + 1}`,
    key: index
  }));

  const a11yProps = (index) => {
    return {
      id: `schema-tab-${index}`,
      'aria-controls': `schema-tabpanel-${index}`,
    };
  };

  return (
    <Box className="schema-form-tabs" sx={sx} {...props}>
      {(title || description) && (
        <Box sx={{ mb: 2 }}>
          {title && (
            <Typography variant="h6" component="h3" sx={{ mb: description ? 1 : 0 }}>
              {title}
            </Typography>
          )}
          
          {description && (
            <Typography
              variant="body2"
              color="text.secondary"
            >
              {description}
            </Typography>
          )}
          
          {divider && <Divider sx={{ mt: 1 }} />}
        </Box>
      )}
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          orientation={orientation}
          variant={variant}
          indicatorColor={indicatorColor}
          textColor={textColor}
          centered={centered}
          scrollButtons={scrollButtons}
          allowScrollButtonsMobile={allowScrollButtonsMobile}
        >
          {tabsToRender.map((tab, index) => (
            <Tab
              key={tab.key || index}
              label={tab.label}
              disabled={tab.disabled}
              icon={tab.icon}
              iconPosition={tab.iconPosition || 'start'}
              {...a11yProps(index)}
            />
          ))}
        </Tabs>
      </Box>
      
      {tabs.length > 0 ? (
        // 使用tabs配置中的内容
        tabs.map((tab, index) => (
          <TabPanel key={tab.key || index} value={activeTab} index={index}>
            {tab.content || tab.children}
          </TabPanel>
        ))
      ) : (
        // 使用传入的children
        React.Children.map(children, (child, index) => (
          <TabPanel key={index} value={activeTab} index={index}>
            {child}
          </TabPanel>
        ))
      )}
    </Box>
  );
};

export default SchemaTabs;
