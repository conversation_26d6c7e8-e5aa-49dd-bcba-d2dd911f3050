import React from "react";
import { pxToRem } from "@u/zkUtils";
import {
  FormHelperText,
  InputLabel,
  OutlinedInput,
  IconButton,
  Stack,
  InputAdornment,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import RequirePoint from "@c/RequirePoint";
import { useState } from "react";

const ZKInput = (props) => {
  const {
    formik = null,
    placeholder = "",
    handleBlur,
    handleChange,
    label,
    name,
    error,
    disabled = false,
    isClear = true,
    labelpostion,
    viewPwd = false,
    isSearch = true,
    value,
    type = "text",
    onClick = () => {},
    width,
    sx = {
      width: width,
      "& .MuiOutlinedInput-input": {
        height: pxToRem(35),
        fontSize: "14px",
      },
      borderRadius: "7px",
    },
    endAdornment,
    handlerClear,
    ...orther
  } = props;

  const [inputType, setInputType] = useState(type);
  const [inputValue, setInputValue] = useState(value || "");

  useEffect(() => {
    setInputValue(value || "");
  }, [value]);

  const blurFn = (e) => {
    if (formik?.handleBlur) {
      formik?.handleBlur(e);
    }

    if (handleBlur) {
      handleBlur(e);
    }
  };

  const changeFn = (e) => {
    if (formik?.handleChange) {
      formik?.handleChange(e);
    }
    if (handleChange) {
      handleChange(e);
    }
  };

  const clearInput = (e) => {
    e.stopPropagation();
    setInputValue("");
    if (formik) {
      formik.setFieldValue(name, "");
    }

    if (handlerClear) {
      handlerClear();
    }
  };

  return (
    <Stack spacing={1}>
      <Stack
        direction={labelpostion === "left" ? "row" : "column"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={1}>
        {label && (
          <InputLabel
            style={{
              marginTop: labelpostion === "left" ? "12px" : "",
              // color: "#474b4fcc",
              fontSize: "14px",
            }}
            htmlFor={"zkInput_" + name}>
            {label} {props.required && <RequirePoint></RequirePoint>}
          </InputLabel>
        )}

        <Stack
          sx={{
            flexGrow: 1,
            width: "100%",
          }}>
          <OutlinedInput
            id={"zkInput_" + name}
            type={inputType}
            autoComplete="off"
            value={inputValue}
            name={name}
            onBlur={blurFn}
            onChange={changeFn}
            placeholder={placeholder}
            fullWidth
            error={Boolean(error)} // 修改这里
            sx={sx}
            startAdornment={
              isSearch && (
                <InputAdornment position="start" onClick={onClick}>
                  <SearchIcon />
                </InputAdornment>
              )
            }
            endAdornment={
              endAdornment ? (
                endAdornment
              ) : isClear ? (
                <IconButton onClick={clearInput}>
                  <ClearIcon
                    fontSize="small"
                    sx={{
                      color: "#BEBEBE",
                      cursor: "pointer",
                    }}
                  />
                </IconButton>
              ) : null
            }
            disabled={disabled}
            {...orther}
          />
          {(value || error) && (
            <FormHelperText error id={`standard-weight-helper-text-${name}`}>
              {error}
            </FormHelperText>
          )}
        </Stack>
      </Stack>
    </Stack>
  );
};

export default ZKInput;
