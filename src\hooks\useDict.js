import { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchDictionary } from "@/store/reducers/actions";

const useDict = (types) => {
  const dictionaries = useRef({});
  const dispatch = useDispatch();
  const dictionariesFromRedux = useSelector((state) => state.dicts.dicts);
  const isFetched = useRef({});

  const typesArray = Array.isArray(types) ? types : [types];

  useEffect(() => {
    typesArray.forEach((type) => {
      if (!dictionariesFromRedux[type] && !isFetched.current[type]) {
        isFetched.current[type] = true;
        dispatch(fetchDictionary(type));
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, typesArray]); // 仅在 dispatch 和 typesArray 变化时触发

  useEffect(() => {
    const processedDictionaries = {};
    typesArray.forEach((type) => {
      if (dictionariesFromRedux[type]) {
        processedDictionaries[type] = processDictionaryData(
          dictionariesFromRedux[type]
        );
      }
    });
    dictionaries.current = processedDictionaries;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dictionariesFromRedux]); // 仅在 dictionariesFromRedux 和 typesArray 变化时触发

  const processDictionaryData = (data) => {
    // 在这里处理你的数据
    return data.map((item) => {
      const returnData = {
        ...item,
        title: item.label,
        listClass: item?.listClass,// "success,processing,default,warning,error"
        value: item.value,
      };
      return returnData;
    });
  };
  return dictionaries;
};

export default useDict;
