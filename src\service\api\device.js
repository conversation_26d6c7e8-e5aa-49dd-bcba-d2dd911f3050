import request from "@/utils/request";

const baseProfixURI = `${import.meta.env.VITE_APICODE}`;

/**
 *  获取设备列表
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const getDeviceList = (params) => {
  return request({
    url: `${baseProfixURI}/device/query/page`,
    method: "GET",
    params: params,
  });
};

/**
 *  获取设备预注册信息
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const getDevicePerInfo = (sn, params) => {
  return request({
    url: `${baseProfixURI}/device/pre_register/${sn}`,
    method: "GET",
    params: params,
  });
};

/**
 *  获取查看设备详情
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const getDeviceDetail = (id) => {
  return request({
    url: `${baseProfixURI}/device/query/${id}`,
    method: "get",
  });
};

/**
 *  新增设备
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const addDevice = (params) => {
  return request({
    url: `${baseProfixURI}/device`,
    method: "POST",
    data: params,
  });
};

/**
 *  修改设备
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const editDevice = (params) => {
  return request({
    url: `${baseProfixURI}/device`,
    method: "PUT",
    data: params,
  });
};

/**
 *  解绑设备
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const unbindDevice = (ids) => {
  return request({
    url: `${baseProfixURI}/device/${ids}`,
    method: "DELETE",
  });
};
