/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react/prop-types */
import React, {
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import * as echarts from "echarts";
// import BMap from 'bmap';
import "echarts/extension/bmap/bmap";
import CropFreeIcon from "@mui/icons-material/CropFree";
import { Grid } from "@mui/material";

// import LoadBMap from '../Map';
import { listStoreStatistics } from "@/service/api/dashboard";
import { useTranslation } from "react-i18next";
import { getStoreLang } from "@/utils/langUtils";
import StoreDetail from "@/pages/dashboard/storeDetail";

const MapChart = forwardRef((props, ref) => {
  const { zoom, setZoom, centerPosition, setCenterPosition } = props;
  //获取当前是哪个语言
  let lang = getStoreLang();
  const chartRef = useRef(null);
  const storeDetailRef = useRef(null);
  const zoomDataRef = useRef(0);
  // const [centerPosition, setCenterPosition] = useState([103.73, 36.03]);
  const { t } = useTranslation();
  // const [zoom, setZoom] = useState(5);
  const [flash, setFlash] = useState(true);
  const [filterData, setFilterData] = useState([]);
  const [zoomData, setZoomData] = useState(zoomDataRef.current);
  const [isDragging, setIsDragging] = useState(false);
  const [data, setData] = useState({
    totalStoreNum: 0,
    totalScreenNum: 0,
    onlineNum: 0,
    offlineNum: 0,
  });

  //地图左边四个标题框的padding-left值
  const [paddingLeft, setPaddingLeft] = useState([50, 20, 65, 65]);
  useImperativeHandle(ref, () => ({
    setCenterPositionRef,
  }));
  const setCenterPositionRef = (data) => {
    setCenterPosition(data);
    setZoom(10);
  };
  //由于第一次进入页面，数据定位会不准确，通过该定时器来修改data来重新渲染图层
  if (flash) {
    setTimeout(() => {
      setData(data);
      setFlash(false);
    }, 1000);
  }
  const handleListStoreStatistics = () => {
    listStoreStatistics().then((res) => {
      // 设置数据
      setData(res.data);
      setFilterData(res.data.storeInfoList);
    });
  };

  // const [myChart, setMyChart] = useState();
  const myChart = React.useRef();
  useEffect(() => {
    if (lang === "en") {
      setPaddingLeft([33, 20, 28, 28]);
    }
    handleListStoreStatistics();
    myChart.current = echarts.init(chartRef.current);
    myChart.current.off("click");
    myChart.current.on("click", function (params) {
      let storeIdList = [];
      storeIdList.push(params.data.id);
      storeDetailRef.current.handleOpen(storeIdList, params.data.retail);
    });
  }, []);

  const citySet = new Set();
  const locations = [];
  let datas = [];
  for (let i = 0; i < filterData.length; i++) {
    const address = filterData[i].address;
    const latLng = filterData[i].value;
    const matchResult = address.match(/([\u4e00-\u9fa5]+市)/);
    if (matchResult !== null) {
      const city = matchResult[0];
      // 判断当前的城市是否已经出现过，如果没有出现过，则进行后续操作
      if (!citySet.has(city)) {
        // 将当前的城市加入到集合 cityList 中
        citySet.add(city);
        // 获取当前城市的一个门店的纬度和经度
        const [lat, lng] = latLng;
        datas.push(filterData[i]);
        // console.log(`城市：${city}，门店坐标：[${lat}, ${lng}]`);
        locations.push({ lat, lng });
      }
    }
  }

  // 统计相同市名的数据数量
  const cityCount = filterData.reduce((count, item) => {
    const city = item.address.match(/[^省市区]+市/);
    if (city) {
      count[city[0]] = (count[city[0]] || 0) + 1;
    }
    return count;
  }, {});
  const cities = Object.keys(cityCount);
  const citiesWithData = [];
  cities.forEach((city) => {
    const count = cityCount[city];
    citiesWithData.push({ city, count });
  });

  useEffect(() => {
    const option = {
      title: [
        {
          text: t("ips.ips_total_outlet_num"),
          subtext: `${data.totalStoreNum}`,
          left: "20",
          top: "40",
          padding: [15, paddingLeft[0], 20, 15],
          textStyle: {
            fontSize: 15,
            color: "#7AC143",
          },
          backgroundColor: "#DEEDDE",
          borderColor: "#7AC143",
          borderWidth: 2,
          borderRadius: 8,
          subtextStyle: {
            fontSize: 20,
            color: "#7AC143",
          },
          itemGap: 15,
        },
        {
          text: t("ips.ips_total_signage_num"),
          subtext: `${data.totalScreenNum}`,
          left: "20",
          top: "155",
          padding: [15, paddingLeft[1], 20, 15],
          textStyle: {
            fontSize: 15,
            color: "#7AC143",
          },
          backgroundColor: "#DEEDDE",
          borderColor: "#7AC143",
          borderWidth: 2,
          borderRadius: 8,
          subtextStyle: {
            fontSize: 20,
            color: "#7AC143",
          },
          itemGap: 15,
        },
        {
          text: t("ips.ips_online_number"),
          subtext: `${data.onlineNum}`,
          left: "20",
          top: "270",
          padding: [15, paddingLeft[2], 20, 15],
          textStyle: {
            fontSize: 15,
            color: "#7AC143",
          },
          backgroundColor: "#DEEDDE",
          borderColor: "#7AC143",
          borderWidth: 2,
          borderRadius: 8,
          subtextStyle: {
            fontSize: 20,
            color: "#7AC143",
          },
          itemGap: 15,
        },
        {
          text: t("ips.ips_offline_number"),
          subtext: `${data.offlineNum}`,
          left: "20",
          top: "385",
          padding: [15, paddingLeft[3], 20, 15],
          textStyle: {
            fontSize: 15,
            color: "#7AC143",
          },
          backgroundColor: "#DEEDDE",
          borderColor: "#7AC143",
          borderWidth: 2,
          borderRadius: 8,
          subtextStyle: {
            fontSize: 20,
            color: "#7AC143",
          },
          itemGap: 15,
        },
      ],

      tooltip: {
        trigger: "item",
        // triggerOn: 'click',
        borderColor: "#7AC143 ",
        borderWidth: 2,
        backgroundColor: "#DEEDDE",

        formatter: function (params, ticket, callback) {
          const {
            name,
            address,
            contacts,
            email,
            retail,
            totalScreenNum,
            onlineNum,
            offlineNum,
          } = params.data;
          console.log(params);
          let context = `<span style='color:#7AC143'>${name}</span></br>`;
          if (retail) {
            context += `${t(
              "ips.ips_store_client_name"
            )} &nbsp; <span style='color:#7AC143'>${retail}</span></br>`;
          }
          const cityData = citiesWithData.find((item) => {
            return address.includes(item.city);
          });
          if (zoomData < 6) {
            if (cityData) {
              context += `${cityData.city}${t(
                "common.common_store_total"
              )} &nbsp;&nbsp; <span style='color:#7AC143'>${
                cityData.count
              }</span></br>`;
            }
          }
          context += `${t(
            "ips.ips_outlet_address"
          )} &nbsp;&nbsp;<span style='color:#7AC143'>${address}</span></br>`;
          context += `${t(
            "ips.ips_operator_name"
          )} &nbsp; <span style='color:#7AC143'>${contacts}</span></br>`;
          // context += `${t(
          //   "ips.ips_store_screen_number"
          // )} &nbsp; <span style='color:#7AC143'>${totalScreenNum}</span></br>`;
          // context += `${t(
          //   "ips.ips_online_number"
          // )} &nbsp; <span style='color:#7AC143'>${onlineNum}</span></br>`;
          // context += `${t(
          //   "ips.ips_offline_number"
          // )} &nbsp; <span style='color:#7AC143'>${offlineNum}</span></br>`;
          return context;
        },
      },
      toolbox: {
        itemSize: 30, // 设置图标大小
        show: true,
        iconStyle: {
          backgroundColor: "#fff",
          color: "#fff", // 图标默认颜色
        },
        feature: {
          myFull: {
            show: true,
            // title:"全屏",
            icon: "M0.989318 36.232261l0.465561 19.262604v1.047514L0 330.758066a40.736626 40.736626 0 0 0 39.456332 39.281747h27.177149A43.180824 43.180824 0 0 0 106.031618 326.044257l0.581952-143.625704 186.224575 187.330284a53.074004 53.074004 0 0 0 74.722611 0 52.375662 52.375662 0 0 0 0-74.315245l-186.806527-189.250725 150.31815 1.163904A37.943257 37.943257 0 0 0 370.41232 69.985465V40.887875C369.946759 19.879415 357.143819 2.362666 335.844383 1.838909L59.417279 0.791396c-0.407366 0-0.698342 1.454879-1.047514 1.45488L38.990771 0.151249A35.673645 35.673645 0 0 0 11.639036 9.346088 36.139207 36.139207 0 0 0 0.989318 36.232261zM58.19518 1021.825828c0.349171 0 0.640147 1.454879 1.047513 1.45488l276.427105-1.105709c21.299436-0.465561 34.102375-18.040506 34.567937-39.281746V954.5522a37.943257 37.943257 0 0 0-39.339942-37.652281l-150.31815 1.163903 186.631942-189.134334a52.375662 52.375662 0 0 0 0-74.315245 53.074004 53.074004 0 0 0-74.722611 0l-186.224575 187.330284-0.581952-143.625704a43.180824 43.180824 0 0 0-39.339942-44.111947H39.456332A40.736626 40.736626 0 0 0 0 693.314037l1.454879 275.321397-0.407366 19.262604A36.197402 36.197402 0 0 0 11.639036 1014.726016a35.673645 35.673645 0 0 0 27.118954 9.136643l19.378995-2.036831zM1022.722092 987.839843l-0.407366-19.262605v-1.047513l1.68766-274.273883a40.736626 40.736626 0 0 0-39.456332-39.281746h-27.177149a43.180824 43.180824 0 0 0-39.339942 44.111946l-0.581951 143.625704-186.224576-187.330284a53.074004 53.074004 0 0 0-74.722611 0 52.375662 52.375662 0 0 0 0 74.315245l186.631942 189.134335-150.31815-1.163904a37.943257 37.943257 0 0 0-39.339942 37.652281v28.573834c0.465561 21.299436 13.268501 38.816185 34.567937 39.281746l276.427105 1.105709c0.407366 0 0.698342-1.454879 1.047513-1.45488l19.378995 2.036831a35.673645 35.673645 0 0 0 27.118954-9.136643 36.197402 36.197402 0 0 0 10.824303-26.886173zM917.156036 182.302162l0.581951 143.742095a43.180824 43.180824 0 0 0 39.339942 44.111946h27.177149a40.736626 40.736626 0 0 0 39.456332-39.281746l-1.396684-275.437787 0.407366-19.262604a36.197402 36.197402 0 0 0-10.824303-26.886173A35.673645 35.673645 0 0 0 984.72064 0.151249l-19.378995 2.036832c-0.349171 0-0.640147-1.454879-1.047514-1.45488l-276.427104 1.105708c-21.299436 0.465561-34.102375 18.040506-34.567937 39.281747V69.985465a37.943257 37.943257 0 0 0 39.223551 37.361306l150.31815-1.163904-186.631942 189.134335a52.375662 52.375662 0 0 0 0 74.315245 53.074004 53.074004 0 0 0 74.722611 0l186.224576-187.330285z",
            onclick: (e) => {
              const element = chartRef.current;
              if (element.requestFullScreen) {
                // HTML W3C 提议
                element.requestFullScreen();
              } else if (element.msRequestFullscreen) {
                // IE11
                element.msRequestFullScreen();
              } else if (element.webkitRequestFullScreen) {
                // Webkit (works in Safari5.1 and Chrome 15)
                element.webkitRequestFullScreen();
              } else if (element.mozRequestFullScreen) {
                // Firefox (works in nightly)
                element.mozRequestFullScreen();
              }
              // 退出全屏
              if (element.requestFullScreen) {
                document.exitFullscreen();
              } else if (element.msRequestFullScreen) {
                document.msExitFullscreen();
              } else if (element.webkitRequestFullScreen) {
                document.webkitCancelFullScreen();
              } else if (element.mozRequestFullScreen) {
                document.mozCancelFullScreen();
              }
            },
          },
        },
      },

      bmap: {
        center: centerPosition,
        zoom: zoom,
        roam: true,
      },
      series: {
        type: "custom",
        coordinateSystem: "bmap",
        data: filterData,
        smooth: true,
        zlevel: 1,
        encode: {
          value: 2,
        },
        showEffectOn: "render",
        rippleEffect: {
          brushType: "stroke",
        },
        label: {
          formatter: "{b}",
          position: "right",
          show: false,
        },
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "#333",
        },
        emphasis: {
          scale: true,
        },
      },
      renderItem(params, api) {
        const coord = api.coord([
          api.value(0, params.dataIndex),
          api.value(1, params.dataIndex),
        ]);
        const circles = [];
        for (let i = 0; i < 5; i++) {
          circles.push({
            type: "circle",
            shape: {
              cx: 0,
              cy: 0,
              r: 30,
            },
            style: {
              stroke: "#8CC862",
              fill: "none",
              lineWidth: 2,
            },
            // Ripple animation
            keyframeAnimation: {
              duration: 4000,
              loop: true,
              delay: (-i / 4) * 4000,
              keyframes: [
                {
                  percent: 0,
                  scaleX: 0,
                  scaleY: 0,
                  style: {
                    opacity: 1,
                  },
                },
                {
                  percent: 1,
                  scaleX: 1,
                  scaleY: 0.4,
                  style: {
                    opacity: 0,
                  },
                },
              ],
            },
          });
        }

        return {
          type: "group",
          x: coord[0],
          y: coord[1],
          children: [
            ...circles,
            {
              type: "path",
              shape: {
                d: "M16 0c-5.523 0-10 4.477-10 10 0 10 10 22 10 22s10-12 10-22c0-5.523-4.477-10-10-10zM16 16c-3.314 0-6-2.686-6-6s2.686-6 6-6 6 2.686 6 6-2.686 6-6 6z",
                x: -10,
                y: -35,
                width: 20,
                height: 30,
              },
              style: {
                fill: "#61A030",
              },
            },
          ],
        };
      },
    };

    myChart.current.setOption(option);
    // const bmap = myChart.current.getModel().getComponent("bmap").getBMap();

    // // 用户在拖拽过程中触发了 zoomend 事件，并返回了 true
    // bmap.addEventListener("zoomend", onZoomEnd);

    // // 监听地图缩放结束的事件

    // // 缩放结束时的回调函数
    // function onZoomEnd() {
    //   setIsDragging(true);
    //   const zoom = bmap.getZoom();
    //   zoomDataRef.current = zoom;
    //   setZoomData(zoom);
    // }
    // // 组件卸载时移除事件监听
    // return () => {
    //   bmap.removeEventListener("zoomend", onZoomEnd);
    //   setIsDragging(false);
    // };
  }, [data, centerPosition]);

  return (
    <>
      <Grid
        item
        ref={chartRef}
        style={{ width: "100%", height: "600px" }}
      ></Grid>
      <StoreDetail ref={storeDetailRef} />
    </>
  );
});

export default MapChart;

// mapStyle: {
//   styleJson: [
//     {
//       featureType: "water",
//       elementType: "all",
//       stylers: {
//         color: "#d1d1d1",
//       },
//     },
//     {
//       featureType: "land",
//       elementType: "all",
//       stylers: {
//         color: "#DDE6ED",
//       },
//     },
//     {
//       featureType: "railway",
//       elementType: "all",
//       stylers: {
//         visibility: "off",
//       },
//     },
//     {
//       featureType: "highway",
//       elementType: "all",
//       stylers: {
//         color: "#fdfdfd",
//       },
//     },
//     {
//       featureType: "highway",
//       elementType: "labels",
//       stylers: {
//         visibility: "off",
//       },
//     },
//     {
//       featureType: "arterial",
//       elementType: "geometry",
//       stylers: {
//         color: "#fefefe",
//       },
//     },
//     {
//       featureType: "arterial",
//       elementType: "geometry.fill",
//       stylers: {
//         color: "#fefefe",
//       },
//     },
//     // {
//     //   featureType: "poi",
//     //   elementType: "all",
//     //   stylers: {
//     //     visibility: "off",
//     //   },
//     // },
//     // {
//     //   featureType: "green",
//     //   elementType: "all",
//     //   stylers: {
//     //     visibility: "off",
//     //   },
//     // },
//     // {
//     //   featureType: "subway",
//     //   elementType: "all",
//     //   stylers: {
//     //     visibility: "off",
//     //   },
//     // },
//     {
//       featureType: "manmade",
//       elementType: "all",
//       stylers: {
//         color: "#d1d1d1",
//       },
//     },
//     {
//       featureType: "local",
//       elementType: "all",
//       stylers: {
//         color: "#d1d1d1",
//       },
//     },
//     // {
//     //   featureType: "arterial",
//     //   elementType: "labels",
//     //   stylers: {
//     //     visibility: "off",
//     //   },
//     // },
//     {
//       featureType: "boundary",
//       elementType: "all",
//       stylers: {
//         color: "#7AC143",
//       },
//     },
//     {
//       featureType: "building",
//       elementType: "all",
//       stylers: {
//         color: "#d1d1d1",
//       },
//     },
//     {
//       featureType: "label",
//       elementType: "labels.text.fill",
//       stylers: {
//         color: "#999999",
//       },
//     },
//     {
//       featureType: "transportation",
//       elementType: "labels.text.stroke",
//       stylers: {
//         color: "#ffffff",
//       },
//     },
//     {
//       featureType: "district",
//       elementType: "labels.text.fill",
//       stylers: {
//         color: "#ffffff",
//       },
//     },
//     {
//       featureType: "district",
//       elementType: "labels.text.stroke",
//       stylers: {
//         color: "#7ac143ff",
//       },
//     },
//   ],
// },
