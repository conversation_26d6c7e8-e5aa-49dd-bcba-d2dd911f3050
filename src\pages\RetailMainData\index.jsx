import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { pxToRem } from "@u/zkUtils";
import LayoutList from "@l/components/LayoutList";
import OutletTable from "./Outlet/TableList";
import ProductTable from "./Product/TableList";
import { getProductList } from "@s/api/product";
import { getOutletList } from "@s/api/outlet";
import { getDepartmentId, getDepartmentCode } from "@/pages/Organization/utils";

function index() {
  const { t } = useTranslation();
  const [selectedValue, setSelectedValue] = useState("1");

  const [data, setData] = useState([]);
  const [rowCount, setRowCount] = useState(0);
  const [serchName, setSeachName] = useState(null);
  const departmentId = getDepartmentId();
  const departmentCode = getDepartmentCode();

  useEffect(() => {
    setSelectedValue(sessionStorage.getItem("OUTLET_PRODUCT"));
  }, [selectedValue]);

  const renderHeader = () => (
    <Grid
      sx={{
        width: "100%",
        height: "100px",
        ml: 2,
      }}
      spacing={4}
      container>
      {["outlets.outlet", "product.product"].map((key, index) => (
        <Grid item key={index}>
          <TabButton
            title={t(key)}
            value={(index + 1).toString()}
            isSelected={selectedValue === (index + 1).toString()}
            onClick={(value) => {
              sessionStorage.setItem("OUTLET_PRODUCT", value);
              setSelectedValue(value);
              setSeachName(""); // 切tab清空搜索词
            }}
          />
        </Grid>
      ))}
    </Grid>
  );

  const renderContent = () => (
    <Grid>
      {selectedValue === "1" ? (
        <OutletTable
          setRowCount={setRowCount}
          rowCount={rowCount}
          data={data}
          setData={setData}
        />
      ) : (
        <ProductTable
          setRowCount={setRowCount}
          rowCount={rowCount}
          data={data}
          setData={setData}
        />
      )}
    </Grid>
  );

  const handlerSeacher = async () => {
    const params = {
      page: 1,
      pageSize: 5,
      name: serchName,
      ...(selectedValue == "1"
        ? { parentId: departmentId }
        : { principalCode: departmentCode }),
    };

    try {
      const res = await (selectedValue == "1"
        ? getOutletList(params)
        : getProductList(params));
      setData(res.data.data);
      setRowCount(res.data.total);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };
  return (
    <React.Fragment>
      <LayoutList
        title={t("outlets.main_title")}
        isSearch={true}
        serchName={serchName}
        onClick={handlerSeacher}
        setSeachName={setSeachName}
        onClear={async () => {
          setSeachName(null);
          const params = {
            page: 1,
            pageSize: 5,
            ...(selectedValue == "1"
              ? { parentId: departmentId }
              : { principalCode: departmentCode }),
          };

          const res = await (selectedValue == "1"
            ? getOutletList(params)
            : getProductList(params));
          setData(res.data.data);
          setRowCount(res.data.total);
        }}
        header={renderHeader()}
        content={renderContent()}></LayoutList>
    </React.Fragment>
  );
}

export default index;

const TabButton = ({ title, value, isSelected, onClick }) => (
  <Grid
    sx={{
      width: pxToRem(200),
      height: pxToRem(60),
      border: isSelected ? "1px solid #78BC27" : "1px solid #E3E3E3",
      borderRadius: "10px",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      cursor: "pointer",
      "&:hover": {
        border: isSelected ? "1px solid #78BC27" : "1px solid #E3E3E3",
      },
    }}
    onClick={() => onClick(value)}>
    <Typography
      sx={{
        font: "normal normal bold 20px/24px Proxima Nova",
        color: "#474B4F",
      }}>
      {title}
    </Typography>
  </Grid>
);
