import AuthWrapper from "./AuthWrapper";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useTranslation } from "react-i18next";
import ZK_DIGIMAX from "@/assets/Images/Logo/ZKDIGIMAX.png";
import { pxToRem } from "@/utils/zkUtils";
import { getFormConfig } from "./Config";
import ZkFormik from "@c/Config/ZkFormik";
import { createValidation } from "@c/Config/validationUtils.js";
import { forgetPassword } from "@/service/api/user";
import { toast } from "react-toastify";

const ForgotPassword = (props) => {
  const { setLoginOrForgot } = props;
  const { t } = useTranslation();

  const [formConfig, setFormConfig] = useState([]);

  const formik = useFormik({
    initialValues: {
      email: "",
      code: "",
      password: "",
      confirmPassword: "",
    },
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        forgetPassword(values).then((res) => {
          setLoginOrForgot("0");
          toast.success(res?.message);
        });

        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });

  useEffect(() => {
    const formConfig = getFormConfig(t, formik);
    setFormConfig(formConfig);
  }, [formik.values]);

  return (
    <AuthWrapper width={"550px"}>
      <img
        style={{
          height: pxToRem(35),
        }}
        src={ZK_DIGIMAX}></img>

      <Grid
        sx={{
          font: "normal normal bold 18px/22px Proxima Nova",
          color: "#222222",
          textAlign: "center",
        }}
        mt={2}
        mb={4}>
        {t("common.common_forgort_title")}
      </Grid>

      <ZkFormik sx={12} formik={formik} formConfig={formConfig}></ZkFormik>

      <Grid container xs={12} mt={2}>
        <Button
          disableElevation
          onClick={() => formik.handleSubmit()}
          variant="contained"
          size="medium"
          style={{
            height: pxToRem(50),
            width: "100%",
            borderRadius: "8px",
            border: "1px solid #E3E3E3",
            background:
              "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
          }}>
          {t("common.common_submit")}
        </Button>
      </Grid>

      <Grid container xs={12} mt={2}>
        <Button
          disableElevation
          type="submit"
          variant="outlined"
          size="medium"
          onClick={() => {
            setLoginOrForgot("0");
          }}
          style={{
            height: pxToRem(50),
            width: "100%",
            borderRadius: "8px",
            border: "1px solid #E3E3E3",
          }}>
          {t("common.common_op_return")}
        </Button>
      </Grid>
    </AuthWrapper>
  );
};
export default ForgotPassword;
