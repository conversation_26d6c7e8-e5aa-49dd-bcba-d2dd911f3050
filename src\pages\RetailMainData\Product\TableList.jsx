import React from "react";
import ZktecoTable from "@c/ZktecoTable";
import CustomDelete from "@c/Toast/CustomDelete";
import { getProductList, deleteProduct } from "@s/api/product";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { getDepartmentId, getDepartmentCode } from "@/pages/Organization/utils";
import ZkTooltip from "@c/ZkTooltip";
import { toast } from "react-toastify";
function TableList(props) {
  const { data, setData, rowCount, setRowCount } = props;
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [isError, setIsError] = useState(false);
  const [tenantId, setTenantId] = useState(null);
  const [open, setOpen] = useState(false); // 删除Branch 弹窗
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);

  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const departmentId = getDepartmentId();
  const departmentCode = getDepartmentCode();

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("product.partner_company_name"),
        enableClickToCopy: true, // 启用点击复制
        Cell: (e) => {
          return (
            <ZkTooltip title={e.row.original.name} arrow placement="bottom">
              <span>{e.row.original.name}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "tenantCode",
        header: t("product.size"),
        enableClickToCopy: true, // 启用点击复制
        Cell: (e) => {
          return (
            <ZkTooltip
              title={e.row.original.tenantCode}
              arrow
              placement="bottom">
              <span>{e.row.original.tenantCode}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "departmentName",
        header: t("product.bar_code"),
        Cell: (e) => {
          return (
            <ZkTooltip
              title={e.row.original.departmentName}
              arrow
              placement="bottom">
              <span>{e.row.original.departmentName}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "categoryLevel1",
        header: t("product.category_level_1"),
        Cell: (e) => {
          return (
            <ZkTooltip
              title={e.row.original.categoryLevel1}
              arrow
              placement="bottom">
              <span>{e.row.original.categoryLevel1}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "categoryLevel2",
        header: t("product.category_level_2"),
        Cell: (e) => {
          return (
            <ZkTooltip
              title={e.row.original.categoryLevel2}
              arrow
              placement="bottom">
              <span>{e.row.original.categoryLevel2}</span>
            </ZkTooltip>
          );
        },
      },
    ],
    []
  );

  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      principalCode: departmentCode,
    };

    return params;
  };

  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    await getProductList(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  const handlerDetele = async () => {
    const res = await deleteProduct(tenantId);
    setOpen(false);
    toast.success(res?.message);
    getTableData();
  };

  const isShowAction = {
    isShowView: "system:product:query",
    isShowEditor: "system:product:update",
    isShowDetele: "system:product:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/view/retail/product", {
          state: { id: data?.id, type: "view" },
        }),
      handlerEditor: (data) =>
        navigate("/add/retail/product", {
          state: { id: data?.id, type: "editor", retailerId: departmentId },
        }),

      onAdd: (data) =>
        navigate("/add/retail/product", {
          state: { retailerId: departmentId },
        }),

      Detele: (data) => {
        setOpen(true);
        setTenantId(data?.id);
      },
    }),
    [setOpen, setTenantId]
  );

  return (
    <React.Fragment>
      <ZktecoTable
        headerTitle={t("product.list")}
        columns={columns}
        data={data}
        rowCount={rowCount}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        loadDada={getTableData}
        paginationProps={{
          currentPage: pagination.pageIndex,
          rowsPerPage: pagination.pageSize,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
        }}
        topActions={{
          showAdd: "system:product:save",
          onAdd: actionHandlers.onAdd,
        }}
        actionHandlers={actionHandlers}
        isShowAction={isShowAction}
      />

      <CustomDelete
        open={open}
        setOpen={setOpen}
        handlerDetele={handlerDetele}></CustomDelete>
    </React.Fragment>
  );
}

export default TableList;
