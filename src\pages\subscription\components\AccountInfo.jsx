import React, { useState } from "react";
import { Grid, Typography } from "@mui/material";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import { useTranslation } from "react-i18next";
import { computerTime } from "../js/ulits";
import { getAccountConfig } from "../js/Config";
import { useLocation } from "react-router-dom";
import CustomCard from "./CustomCard";
import Select from "./Select";
import dayjs from "dayjs";
function AccountInfo(props) {
  const { formik, accountInfoConfig, setAccountInfoConfig } = props;
  const { t } = useTranslation();
  const { state } = useLocation();
  const [isSelected, setIsSelected] = useState(0);
  const [open, setOpen] = useState(false);
  const [selectedId, setSelectedId] = useState(null); // 选择的零售商
  const buttonData = [
    { value: "0", label: t("subscription.create_principal") },
    { value: "1", label: t("subscription.select_principal") },
    // { value: "2", label: "Create a Partner" },
    // { value: "3", label: "Select a Partner" },
  ];

  useEffect(() => {
    setAccountInfoConfig(
      getAccountConfig(t, state, isSelected, setOpen, formik)
    );
  }, [isSelected]);

  return (
    <>
      <CustomCard title={t("subscription.account_information")}>
        {state?.type == "add" && (
          <Grid mt={4} display={"flex"} item xs={4}>
            {buttonData.map(({ value, label }) => (
              <ButtomTag
                key={value}
                selected={isSelected == value}
                title={t(label)}
                onClick={() => {
                  formik.setFieldValue("contactEmail", undefined);
                  formik.setFieldValue("departmentId", undefined);
                  setIsSelected(value);
                }}
              />
            ))}
          </Grid>
        )}

        <Grid mt={4}>
          <ZkFormik
            sx={6}
            formik={formik}
            formConfig={accountInfoConfig}></ZkFormik>
        </Grid>
      </CustomCard>
      <Select
        setOpen={setOpen}
        open={open}
        selectedId={selectedId}
        setSelectedId={setSelectedId}
        formik={formik}></Select>
    </>
  );
}

export default AccountInfo;

const ButtomTag = (props) => {
  const { selected, onClick, title } = props;

  const style = {
    width: "170px",
    height: "35px",
    borderBottom: selected && "5px solid #78BC27",
    font: `normal normal  16px/18px Proxima Nova`,
    color: "#474B4F",
    textAlign: "center",
    ml: 2,
  };

  return (
    <Grid container>
      <Grid item sx={style} onClick={onClick}>
        {title}
      </Grid>
    </Grid>
  );
};
