/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable jsx-a11y/alt-text */
import React from "react";
import { Card, Stack, IconButton } from "@mui/material";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import DeleteIcon from "@mui/icons-material/Delete";
import { useTranslation } from "react-i18next";
import "./mediaCard.less";
import { useTheme } from "@mui/material/styles";
const defaultSrc = require("@/assets/images/bg/img_bg.png");
import { PhotoProvider, PhotoView } from "react-photo-view";
import "react-photo-view/dist/react-photo-view.css";
import Divider from "@mui/material/Divider";

const MediaCard = ({
  name = "默认名称",
  maxWidth = 345,
  src = defaultSrc,
  resolution = "1920*1080",
  previewChange = () => {},
  deleteChange = () => {},
}) => {
  const [preview, setPreview] = React.useState(false);
  const theme = useTheme();
  const previewClick = (evnet) => {
    setPreview(true);
    // previewChange(evnet);
  };
  const { t } = useTranslation();
  const deleteClick = (event) => {
    deleteChange(evnet);
  };
  return (
    <>
      <Card
        sx={{
          maxWidth: maxWidth,
          position: "relative",
        }}
      >
        <CardMedia
          className="img_div"
          alt={t("common.common_Unable_load")}
          height="200"
        >
          <PhotoProvider>
            <PhotoView src={src} visible={preview}>
              <img
                className="imgcss"
                width={250}
                style={{ objectFit: "cover" }}
                src={src}
                height="200"
              />
            </PhotoView>
          </PhotoProvider>

          <div className="mask">
            <Stack
              sx={{
                width: "100%",
                height: "100%",
                paddingBottom: "10px",
                paddingRight: "10px",
              }}
              direction="row"
              justifyContent="flex-end"
              alignItems="flex-end"
              spacing={1}
            >
              <IconButton
                sx={{ background: "#f46d43", borderRadius: "50%" }}
                size="small"
                aria-label="preview"
                color="white"
                onClick={() => previewClick()}
              >
                <VisibilityOutlinedIcon
                  fontSize="small"
                  color="white"
                  sx={{ color: "white" }}
                />
              </IconButton>

              <IconButton
                sx={{
                  background: theme.palette.primary.main,
                  borderRadius: "50%",
                }}
                color="white"
                aria-label="delete"
                size="small"
              >
                <DeleteIcon fontSize="small" sx={{ color: "white" }} />
              </IconButton>
            </Stack>
          </div>
        </CardMedia>
        <Divider />
        <CardActions>
          <Grid
            container
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography>{name}</Typography>
            <Typography>{resolution}</Typography>
          </Grid>
        </CardActions>
      </Card>
    </>
  );
};
export default MediaCard;
