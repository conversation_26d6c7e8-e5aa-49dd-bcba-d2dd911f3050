import React, { useCallback } from 'react';
import {
  TextField,
  Box,
  Typography,
  Chip
} from '@mui/material';
import { useTranslation } from 'react-i18next';

/**
 * Schema驱动的文本区域组件
 */
const SchemaTextarea = ({
  name,
  path,
  schema,
  uiSchema = {},
  value = '',
  error,
  touched,
  disabled = false,
  readonly = false,
  required = false,
  onChange,
  onBlur,
  onValidate,
  formData,
  registry,
  ...props
}) => {
  const { t } = useTranslation();

  // 从schema中提取配置
  const {
    title,
    description,
    placeholder,
    minLength,
    maxLength,
    pattern
  } = schema;

  // 从uiSchema中提取UI配置
  const {
    label = title,
    help = description,
    placeholder: uiPlaceholder = placeholder,
    rows = 4,
    minRows = 2,
    maxRows = 10,
    variant = 'outlined',
    size = 'medium',
    fullWidth = true,
    autoFocus = false,
    showCharacterCount = false,
    resizable = true,
    ...uiProps
  } = uiSchema;

  // 处理值变化
  const handleChange = useCallback((event) => {
    const newValue = event.target.value;
    onChange?.(newValue);
  }, [onChange]);

  // 处理失焦
  const handleBlur = useCallback((event) => {
    onBlur?.(event);
    
    // 执行验证
    if (onValidate) {
      const errors = [];
      
      // 必填验证
      if (required && (!value || value.trim() === '')) {
        errors.push(t('validation.required', { field: label || name }));
      }
      
      // 长度验证
      if (value && typeof value === 'string') {
        if (minLength && value.length < minLength) {
          errors.push(t('validation.minLength', { field: label || name, min: minLength }));
        }
        if (maxLength && value.length > maxLength) {
          errors.push(t('validation.maxLength', { field: label || name, max: maxLength }));
        }
      }
      
      // 模式验证
      if (value && pattern) {
        const regex = new RegExp(pattern);
        if (!regex.test(value)) {
          errors.push(t('validation.pattern', { field: label || name }));
        }
      }
      
      onValidate(errors.length > 0 ? errors[0] : null);
    }
  }, [onBlur, onValidate, value, required, minLength, maxLength, pattern, label, name, t]);

  // 计算当前字符数
  const currentLength = value ? value.length : 0;
  
  // 判断字符数是否超限
  const isOverLimit = maxLength && currentLength > maxLength;
  const isNearLimit = maxLength && currentLength > maxLength * 0.8;

  return (
    <Box className="schema-form-field">
      <TextField
        name={name}
        label={label}
        placeholder={uiPlaceholder || t('common.pleaseEnter', { field: label || name })}
        value={value || ''}
        variant={variant}
        size={size}
        fullWidth={fullWidth}
        multiline
        rows={rows}
        minRows={minRows}
        maxRows={maxRows}
        required={required}
        disabled={disabled}
        InputProps={{
          readOnly: readonly,
          style: resizable ? {} : { resize: 'none' }
        }}
        error={Boolean(error && touched) || isOverLimit}
        helperText={
          (error && touched) ? error : (
            help ? (
              <Typography variant="caption" color="text.secondary">
                {help}
              </Typography>
            ) : null
          )
        }
        autoFocus={autoFocus}
        onChange={handleChange}
        onBlur={handleBlur}
        {...uiProps}
        {...props}
      />
      
      {/* 字符计数器 */}
      {showCharacterCount && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mt: 1
          }}
        >
          <Box sx={{ flexGrow: 1 }} />
          <Chip
            label={
              maxLength 
                ? `${currentLength}/${maxLength}`
                : currentLength.toString()
            }
            size="small"
            variant="outlined"
            color={
              isOverLimit ? 'error' : 
              isNearLimit ? 'warning' : 
              'default'
            }
            sx={{
              fontSize: '0.75rem',
              height: 20
            }}
          />
        </Box>
      )}
      
      {/* 长度警告 */}
      {isOverLimit && (
        <Typography
          variant="caption"
          color="error"
          sx={{ mt: 0.5, display: 'block' }}
        >
          {t('validation.maxLength', { field: label || name, max: maxLength })}
        </Typography>
      )}
      
      {/* 最小长度提示 */}
      {minLength && currentLength > 0 && currentLength < minLength && (
        <Typography
          variant="caption"
          color="warning.main"
          sx={{ mt: 0.5, display: 'block' }}
        >
          {t('validation.minLengthHint', { 
            field: label || name, 
            min: minLength,
            remaining: minLength - currentLength
          })}
        </Typography>
      )}
    </Box>
  );
};

export default SchemaTextarea;
