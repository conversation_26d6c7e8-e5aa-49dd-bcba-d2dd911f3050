import React, { useState, useEffect, memo } from "react";
import NavItem from "./NavItem";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import SystemLog from "@/assets/Icons/UserSetting.svg?react";
import DataPermissionIcon from "@/assets/Icons/DataPermission.svg?react";
import Manager from "@/assets/menuIcon/manager.svg?react";
import SystemSetting from "@/assets/menuIcon/SystemIcon.svg?react";
import Home from "@/assets/menuIcon/Home.svg?react";
import ApplicationCenter from "@/assets/menuIcon/ApplicationCenter.svg?react";
import Subscription from "@/assets/menuIcon/Subscription.svg?react";
import DeviceIcon from "@/assets/menuIcon/DeviceIcon.svg?react";
import DictManager from "@/assets/menuIcon/DictManage.svg?react";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { activeItem } from "@/store/reducers/menu";
import { useTheme } from "@mui/material/styles";
import {
  Grid,
  ListItemButton,
  ListItemIcon,
  Collapse,
  List,
  Typography,
} from "@mui/material";

const iconMap = {
  organizationManage: <Manager />,
  systemLog: <SystemLog />,
  systemSetting: <SystemSetting />,
  home: <Home />,
  subscription: <Subscription />,
  applicationCenter: <ApplicationCenter />,
  DataScope: <DataPermissionIcon />,
  DeviceManage: <DeviceIcon />,
  DictManage: <DictManager />,
};

const NavCollapse = ({ item }) => {
  const theme = useTheme();
  const { openItem } = useSelector((state) => state.menu);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const { pathname } = useLocation();

  const handleClick = () => {
    if (item?.children) {
      setOpen(!open);
    } else {
      navigate(item.path);
    }
  };

  const getIcons = (key) => iconMap[key] || null;

  const isSelected = Boolean(openItem?.find((code) => code === item.code));

  useEffect(() => {
    if (pathname?.includes(item.path)) {
      dispatch(activeItem({ openItem: [item.code] }));
    }
  }, [pathname, item.path, dispatch]);

  const listItemButtonStyle = {
    zIndex: 1201,
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    "&:hover": {
      bgcolor: "rgba(122, 193, 67, 0.26)",
    },
    "&.Mui-selected": {
      bgcolor: "rgba(122, 193, 67,0.2)",
      color: "primary.main",
      "&::before": {
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        bottom: 0,
        width: "4px",
        backgroundColor: theme.palette.primary.main,
      },
    },
  };

  const iconContainerStyle = {
    width: 36,
    height: 36,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  };

  return (
    <Grid
      key={item?.id}
      sx={{
        // px:1,
        mb: 1,
      }}>
      <ListItemButton
        onClick={handleClick}
        selected={isSelected}
        sx={{
          ...listItemButtonStyle,
          justifyContent: openItem ? "space-between" : "center",
          px: openItem ? 2 : 1,
        }}>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <ListItemIcon sx={iconContainerStyle}>
            {getIcons(item?.icon)}
          </ListItemIcon>

          {openItem && (
            <Typography
              sx={{
                color: "rgba(71, 75, 79, 0.6)",
                fontFamily: "Proxima Nova",
                fontSize: "14px",
                fontWeight: 500,
                lineHeight: "18px",
                ml: 1,
              }}>
              {item.name}
            </Typography>
          )}
        </Box>
        {openItem && item?.children && (
          <>
            {open ? (
              <ExpandLess sx={{ color: "rgba(71, 75, 79, 0.6)" }} />
            ) : (
              <ExpandMore sx={{ color: "rgba(71, 75, 79, 0.6)" }} />
            )}
          </>
        )}
      </ListItemButton>

      {/*  以下 为 二级菜单 */}
      {openItem &&
        item?.children?.map((childItem) => (
          <Collapse in={open} timeout="auto" unmountOnExit key={childItem.code}>
            <List component="div" sx={{ p: 0 }}>
              <NavItem item={childItem} />
            </List>
          </Collapse>
        ))}
    </Grid>
  );
};

export default memo(NavCollapse);
