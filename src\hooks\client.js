import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setClientCode, setClientId } from "@/store/reducers/client.js";

export const getClientId = () => useSelector((store) => store.client.clientId);

export const getClientCode = () =>
  useSelector((store) => store.client.clientCode);
export function useDispatchClient() {
  const dispatch = useDispatch();
  const stateSetClientId = useCallback(
    (id) => dispatch(setClientId(id)),
    [dispatch]
  );
  const stateSetClientCode = useCallback(
    (code) => dispatch(setClientCode(code)),
    [dispatch]
  );
  return { stateSetClientId, stateSetClientCode };
}
