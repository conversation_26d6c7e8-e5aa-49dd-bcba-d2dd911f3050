import { createSlice } from "@reduxjs/toolkit";
import { getLoginInfor } from "@/service/api/user";
// 从 localStorage 获取初始状态
const getInitialState = () => {
  return {
    userInfor: "",
    permission: [],
    isInfoLoaded: false,
  };
};
// 创建分片
const user = createSlice({
  name: "user",
  initialState: {
    userInfor: "",
    permission: [],
    isInfoLoaded: false,
  },
  reducers: {
    setUserInfor(state, action) {
      state.userInfor = action.payload;
    },
    clearUser(state) {
      state.userInfor = {};
      state.permission = [];
      state.isInfoLoaded = false;
    },

    setInfoLoaded(state, action) {
      state.isInfoLoaded = action.payload;
    },
    // 获取用户信息
    requestUserInfor(state, action) {
      getLoginInfor().then((res) => {
        const data = res.data;
        state.userInfor = data;
      });
    },
    // 权限字符串
    setPermission(state, action) {
      state.permission = action.payload;
    },
  },
});

// 导出
export default user.reducer;

export const {
  setUserInfor,
  clearUser,
  requestUserInfor,
  setPermission,
  setInfoLoaded,
} = user.actions;


