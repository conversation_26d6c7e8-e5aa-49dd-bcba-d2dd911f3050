import React from "react";
import { useTranslation } from "react-i18next";
import CMS<PERSON>ogo from "@/assets/Images/Logo/CmsLogo.png";
import NugtagLogo from "@/assets/Images/Logo/NutagLogo.png";
import ZataLogo from "@/assets/Images/Logo/zataLogo.png";
import { Stack, Skeleton, Card, Grid, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";
import { useTheme } from "@mui/material/styles";
import LinearProgress, {
  linearProgressClasses,
} from "@mui/material/LinearProgress";

function MySubscription() {
  const { t } = useTranslation();

  const [subLoading, setSubLoading] = useState(false);

  const [CmsData, setCmsData] = useState({
    usedDeviceCount: 90,
    usedExpireDay: 56,
    maxDeviceCount: 100,
    maxExpiredDay: 730,
  });
  const [NutagData, setNutagData] = useState({
    usedDeviceCount: 180,
    usedExpireDay: 230,
    maxDeviceCount: 300,
    maxExpiredDay: 250,
  });

  const [ZataData, seZataData] = useState({
    usedDeviceCount: 86,
    usedExpireDay: 160,
    maxDeviceCount: 200,
    maxExpiredDay: 280,
  });

  return (
    <React.Fragment>
      <Grid
        sx={{
          height: "100%",
          width: "100%",
          backgroundColor: "#fff",
          p: 2,
        }}>
        <Box
          sx={{
            fontSize: 24,
            fontWeight: 600,
            color: "#000",
            borderBottom: "1px solid #f0f0f0",
            height: "70px",
            width: "100%",
          }}>
          {t("common.my_subscription")}
        </Box>

        <Grid
          container
          xs={12}
          spacing={2}
          sx={{
            height: "406px",
            mt: 2,
          }}>
          <Grid item xs={12} sm={6} md={4}>
            <SubscriptionCard
              imageLogo={CMSLogo}
              title={t("Screen Direct")}
              subData={CmsData}
              subLoading={subLoading}></SubscriptionCard>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <SubscriptionCard
              imageLogo={NugtagLogo}
              title={t("Nu Tag")}
              subData={NutagData}
              subLoading={subLoading}></SubscriptionCard>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <SubscriptionCard
              imageLogo={ZataLogo}
              title={t("Zata")}
              subData={ZataData}
              subLoading={subLoading}></SubscriptionCard>
          </Grid>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default MySubscription;

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 10,
  borderRadius: 5,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor:
      theme.palette.grey[theme.palette.mode === "light" ? 200 : 800],
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 5,
    backgroundColor: theme.palette.primary.main,
    // backgroundColor: theme.palette.mode === "light" ? "#1a90ff" : "#308fe8",
  },
}));

const SubscriptionCard = (props) => {
  const { imageLogo, title, subData, subLoading } = props;
  const { t } = useTranslation();
  const theme = useTheme();

  const BoxStyle = {
    width: "100%",
    height: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    background: `linear-gradient(
          230deg,
          rgba(80, 182, 241, 0.2) 0%,
          rgba(176, 235, 235, 0.2) 20%,
          rgba(235, 255, 237, 0.2) 57%,
          rgba(216, 246, 188, 0.2) 84%,
          rgba(170, 235, 92, 0.2) 100%
        )`,
    border: `1px solid #1487CA`,
    borderRadius: "5px",
  };

  const LogoIconStyle = {
    display: "flex",
    width: "80%",
    height: "20%",
    background: "#fff",
    marginTop: "30px",
    borderRadius: "2px",
    alignItems: "center",
    justifyContent: "center",
  };

  const getEffectexpireDayBarColor = () => {
    const value =
      subData.usedExpireDay === -1
        ? 0
        : subData.usedExpireDay == subData.maxExpiredDay
        ? 100
        : (subData.usedExpireDay / subData.maxExpiredDay) * 100;

    let color = theme.palette.primary.main; // 默认为绿色
    if (value > 50) {
      color = "yellow"; // 如果超过 50%，则将颜色设置为黄色
    }
    if (value > 80) {
      color = "red"; // 如果超过 80%，则将颜色设置为红色
    }
    return color;
  };

  const getEffectdeviceCountBarColor = () => {
    const value =
      subData.usedDeviceCount === -1
        ? 0
        : subData.usedDeviceCount >= subData.maxDeviceCount
        ? 100
        : (subData.usedDeviceCount / subData.maxDeviceCount) * 100;
    let color = theme.palette.primary.main; // 默认为绿色
    if (value > 50) {
      color = "yellow"; // 如果超过 50%，则将颜色设置为黄色
    }
    if (value > 80) {
      color = "red"; // 如果超过 80%，则将颜色设置为红色
    }
    return color;
  };

  return (
    <React.Fragment>
      <Box sx={BoxStyle}>
        <Box sx={LogoIconStyle}>
          <img
            src={imageLogo}
            style={{
              width: "40px",
              height: "40px",
              marginRight: "15px",
            }}
            alt="加载失败"></img>
          <div className="text-nowrap text-xl  font-bold">{title}</div>
        </Box>

        <Grid
          container
          xs={12}
          sx={{
            display: "flex",
            justifyContent: "center",
            mt: 4,
          }}>
          <Grid item xs={10}>
            {subLoading ? (
              <Stack spacing={0}>
                <Skeleton />
                <Skeleton />
                <Skeleton />
              </Stack>
            ) : (
              <Stack spacing={1}>
                <Grid
                  container
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}>
                  <InputLabel sx={{ fontWeight: 700, fontSize: "16px" }}>
                    {t("common.common_remaining_day")}
                  </InputLabel>

                  <InputLabel>{t("In Days")}</InputLabel>
                </Grid>
                {/* "usedDeviceCount": 0, "usedExpireDay": 1, "maxDeviceCount":
                8888, "maxExpiredDay": 730 */}
                <BorderLinearProgress
                  variant="determinate"
                  value={
                    subData.usedExpireDay == -1
                      ? 0
                      : subData.usedExpireDay >= subData.maxExpiredDay
                      ? 100
                      : (subData.usedExpireDay / subData.maxExpiredDay) * 100
                  }
                  sx={{
                    marginBottom: 0.3,
                    "& .MuiLinearProgress-bar": {
                      backgroundColor: getEffectexpireDayBarColor(),
                    },
                  }}
                />
                <Stack justifyContent={"space-between"} flexDirection={"row"}>
                  <Typography
                    sx={{
                      fontWeight: 700,
                    }}>
                    {subData.maxExpiredDay === -1
                      ? t("common.common_no_limit")
                      : t("common.common_used_day", {
                          day: subData.usedExpireDay,
                        })}
                  </Typography>

                  <Typography
                    sx={{
                      fontWeight: 700,
                    }}>
                    {t("common.common_gross")}
                    {subData.maxExpiredDay === -1
                      ? t("common.common_no_limit")
                      : ` ${subData.maxExpiredDay} ${t("common.common_day")}`}
                  </Typography>
                </Stack>
              </Stack>
            )}
          </Grid>

          <Grid item xs={10}>
            {subLoading ? (
              <Stack spacing={0}>
                <Skeleton />
                <Skeleton />
                <Skeleton />
              </Stack>
            ) : (
              <Stack spacing={1}>
                <Grid
                  container
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}>
                  <InputLabel sx={{ fontWeight: 700, fontSize: "16px" }}>
                    {t("common.common_remaining_device_count")}
                  </InputLabel>

                  <InputLabel>{t("In Days")}</InputLabel>
                </Grid>
                <BorderLinearProgress
                  variant="determinate"
                  value={
                    subData.usedDeviceCount === -1
                      ? 0
                      : subData.usedDeviceCount >= subData.maxDeviceCount
                      ? 100
                      : (subData.usedDeviceCount / subData.maxDeviceCount) * 100
                  }
                  sx={{
                    marginBottom: 0.3,
                    "& .MuiLinearProgress-bar": {
                      backgroundColor: getEffectdeviceCountBarColor(),
                    },
                  }}
                />
                <Stack justifyContent={"space-between"} flexDirection={"row"}>
                  <Typography
                    sx={{
                      fontWeight: 700,
                    }}>
                    {subData.usedDeviceCount === -1
                      ? t("common.common_no_limit")
                      : t("common.common_add_device_num", {
                          count: subData.usedDeviceCount,
                        })}
                  </Typography>
                  <Typography
                    sx={{
                      fontWeight: 700,
                    }}>
                    {t("common.common_gross")}
                    {subData.maxDeviceCount === -1
                      ? t("common.common_no_limit")
                      : ` ${subData.maxDeviceCount} ${t(
                          "common.common_add_device_dot"
                        )}`}
                  </Typography>
                </Stack>
              </Stack>
            )}
          </Grid>
        </Grid>
      </Box>
    </React.Fragment>
  );
};
