import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Alert,
  Divider
} from '@mui/material';
import { SchemaFormRenderer } from '../index';

/**
 * Schema表单演示页面
 */
const SchemaFormDemo = () => {
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});
  const [submitted, setSubmitted] = useState(false);

  // 演示Schema配置
  const demoSchema = {
    type: 'object',
    title: 'Schema表单演示',
    properties: {
      // 基础信息
      basicInfo: {
        layout: {
          type: 'card',
          title: '基础信息',
          description: '请填写您的基础信息'
        },
        fields: [
          {
            name: 'username',
            type: 'string',
            title: '用户名',
            required: true,
            minLength: 3,
            maxLength: 20,
            pattern: '^[a-zA-Z0-9_]+$'
          },
          {
            name: 'email',
            type: 'string',
            format: 'email',
            title: '邮箱地址',
            required: true
          }
        ]
      },

      // 个人信息
      personalInfo: {
        layout: {
          type: 'grid',
          columns: 2,
          title: '个人信息'
        },
        fields: [
          {
            name: 'firstName',
            type: 'string',
            title: '名字',
            required: true
          },
          {
            name: 'lastName',
            type: 'string',
            title: '姓氏',
            required: true
          },
          {
            name: 'age',
            type: 'integer',
            title: '年龄',
            minimum: 18,
            maximum: 100
          },
          {
            name: 'gender',
            type: 'string',
            title: '性别',
            widget: 'radio',
            enum: ['male', 'female', 'other'],
            enumNames: ['男', '女', '其他']
          }
        ]
      },

      // 联系方式
      contactInfo: {
        layout: {
          type: 'group',
          title: '联系方式',
          collapsible: true
        },
        fields: [
          {
            name: 'phone',
            type: 'string',
            title: '手机号码',
            pattern: '^1[3-9]\\d{9}$'
          },
          {
            name: 'address',
            type: 'string',
            widget: 'textarea',
            title: '地址',
            rows: 3
          },
          {
            name: 'country',
            type: 'string',
            title: '国家',
            widget: 'select',
            enum: ['CN', 'US', 'UK', 'JP'],
            enumNames: ['中国', '美国', '英国', '日本'],
            default: 'CN'
          }
        ]
      },

      // 偏好设置
      preferences: {
        layout: {
          type: 'group',
          title: '偏好设置'
        },
        fields: [
          {
            name: 'newsletter',
            type: 'boolean',
            title: '订阅邮件通知',
            default: false
          },
          {
            name: 'language',
            type: 'string',
            title: '首选语言',
            widget: 'select',
            enum: ['zh-CN', 'en-US', 'ja-JP'],
            enumNames: ['中文', 'English', '日本語'],
            default: 'zh-CN'
          },
          {
            name: 'interests',
            type: 'array',
            title: '兴趣爱好',
            widget: 'checkboxes',
            enum: ['technology', 'sports', 'music', 'travel', 'reading'],
            enumNames: ['科技', '运动', '音乐', '旅行', '阅读']
          }
        ]
      }
    }
  };

  // UI Schema配置
  const uiSchema = {
    username: {
      placeholder: '请输入用户名',
      help: '用户名只能包含字母、数字和下划线'
    },
    email: {
      placeholder: '请输入邮箱地址'
    },
    firstName: {
      placeholder: '请输入名字'
    },
    lastName: {
      placeholder: '请输入姓氏'
    },
    phone: {
      placeholder: '请输入手机号码',
      help: '请输入有效的中国大陆手机号码'
    },
    address: {
      placeholder: '请输入详细地址'
    },
    interests: {
      inline: true
    }
  };

  const handleChange = (newData) => {
    setFormData(newData);
    setSubmitted(false);
  };

  const handleValidate = (newErrors) => {
    setErrors(newErrors);
  };

  const handleSubmit = () => {
    if (Object.keys(errors).length === 0) {
      setSubmitted(true);
      console.log('表单提交成功:', formData);
    }
  };

  const handleReset = () => {
    setFormData({});
    setErrors({});
    setSubmitted(false);
  };

  return (
    <Box sx={{ maxWidth: 800, margin: '0 auto', padding: 3 }}>
      <Typography variant="h4" gutterBottom>
        Schema表单系统演示
      </Typography>
      
      <Typography variant="body1" paragraph>
        这是一个基于JSON Schema的配置化表单演示，展示了新的Schema驱动配置风格的特点。
      </Typography>

      {submitted && (
        <Alert severity="success" sx={{ mb: 3 }}>
          表单提交成功！请查看控制台输出。
        </Alert>
      )}

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <SchemaFormRenderer
          schema={demoSchema}
          uiSchema={uiSchema}
          formData={formData}
          onChange={handleChange}
          onValidate={handleValidate}
        />
        
        <Divider sx={{ my: 3 }} />
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={Object.keys(errors).length > 0}
          >
            提交表单
          </Button>
          <Button
            variant="outlined"
            onClick={handleReset}
          >
            重置表单
          </Button>
        </Box>
      </Paper>

      {/* 数据预览 */}
      <Paper elevation={1} sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          实时数据预览：
        </Typography>
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px',
          overflow: 'auto',
          fontSize: '12px',
          maxHeight: '300px'
        }}>
          {JSON.stringify(formData, null, 2)}
        </pre>
        
        {Object.keys(errors).length > 0 && (
          <>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              验证错误：
            </Typography>
            <pre style={{ 
              background: '#ffebee', 
              padding: '10px', 
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '12px',
              color: '#c62828'
            }}>
              {JSON.stringify(errors, null, 2)}
            </pre>
          </>
        )}
      </Paper>

      {/* 特性说明 */}
      <Paper elevation={1} sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          演示特性：
        </Typography>
        <Box component="ul" sx={{ pl: 2 }}>
          <li>声明式Schema配置</li>
          <li>多种布局组件（Card、Grid、Group）</li>
          <li>丰富的字段类型（Input、Select、Radio、Checkbox、Textarea）</li>
          <li>实时验证和错误提示</li>
          <li>可折叠的分组布局</li>
          <li>响应式网格布局</li>
          <li>自定义UI配置</li>
          <li>类型安全的数据处理</li>
        </Box>
      </Paper>
    </Box>
  );
};

export default SchemaFormDemo;
