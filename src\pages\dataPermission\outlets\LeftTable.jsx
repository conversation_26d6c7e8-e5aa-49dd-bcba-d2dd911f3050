import React from "react";
import DataTable from "../components/DataTable";
import { getOutletList } from "@s/api/outlet.js";
import { OutletsColumns } from "../components/Colums";
import { useTranslation } from "react-i18next";
const LeftTable = forwardRef((props, ref) => {
  const { tableRef, rowSelection, setRowSelection, rowId } = props;
  const { t } = useTranslation();
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  // 构建参数
  const buildParams = (name) => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      parentId: rowId?.id,
      name: name,
    };

    return params;
  };

  const loadData = async (name = null) => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    await getOutletList(buildParams(name))
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };

  useImperativeHandle(ref, () => ({
    loadData,
  }));

  useEffect(() => {
    loadData();
  }, [pagination.pageIndex, pagination.pageSize]);

  return (
    <Grid item xs={5.6}>
      <DataTable
        title={t("outlets.outlet_list")}
        columns={OutletsColumns()}
        enableRowActions={false}
        data={data}
        rowCount={rowCount}
        isRefetching={isRefetching}
        isError={isError}
        state={{
          // 加载状态
          isLoading: isLoading,
          rowSelection,
        }}
        tableInstanceRef={tableRef} // 绑定 ref
        rowsPerPage={pagination.pageSize}
        currentPage={pagination.pageIndex}
        getRowId={(originalRow) => originalRow.id} // 确保 ID 唯一
        enableRowSelection={true}
        //行选中
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        onRowSelectionChange={setRowSelection}
        onPageChange={(pageIndex) => {
          setPagination((prev) => ({
            ...prev,
            pageIndex, // 更新页码
          }));
        }}
        onPageSizeChange={(pageSize) => {
          setPagination({
            pageIndex: 0, // 重置页码
            pageSize, // 更新每页行数
          });
        }}></DataTable>
    </Grid>
  );
});

export default LeftTable;
