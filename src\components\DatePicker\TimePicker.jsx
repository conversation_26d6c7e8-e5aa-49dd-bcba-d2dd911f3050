import React, { useState, useCallback, useRef } from "react";
import {
  TextField,
  Popover,
  Box,
  InputAdornment,
  IconButton,
  FormHelperText,
  Button,
  Typography,
  useTheme,
  alpha,
} from "@mui/material";
import {
  LocalizationProvider,
} from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import {
  AccessTime as TimeIcon,
  Clear as ClearIcon,
} from "@mui/icons-material";
import { styled } from '@mui/material/styles';
import dayjs from 'dayjs';

// Material-UI 风格的样式组件
const StyledPopover = styled(Popover)(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: theme.spacing(1),
    boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
    border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
    overflow: 'hidden',
    minWidth: 'auto',
    background: theme.palette.background.paper,
  },
}));

const StyledTimePanel = styled(Box)(({ theme }) => ({
  display: 'flex',
  minWidth: 240,
  '& .time-column': {
    flex: 1,
    borderRight: `1px solid ${theme.palette.divider}`,
    '&:last-child': {
      borderRight: 'none',
    },
  },
  '& .time-column-header': {
    padding: theme.spacing(1),
    textAlign: 'center',
    fontSize: '0.75rem',
    fontWeight: 500,
    color: theme.palette.text.secondary,
    borderBottom: `1px solid ${theme.palette.divider}`,
    backgroundColor: alpha(theme.palette.background.default, 0.5),
  },
  '& .time-list': {
    height: 200,
    overflowY: 'auto',
    '&::-webkit-scrollbar': {
      width: 6,
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: alpha(theme.palette.text.secondary, 0.3),
      borderRadius: 3,
    },
  },
  '& .time-item': {
    padding: theme.spacing(0.5, 1),
    textAlign: 'center',
    fontSize: '0.875rem',
    cursor: 'pointer',
    transition: theme.transitions.create(['background-color'], {
      duration: theme.transitions.duration.shortest,
    }),
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.08),
    },
    '&.selected': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
    },
    '&.disabled': {
      color: theme.palette.text.disabled,
      cursor: 'not-allowed',
      '&:hover': {
        backgroundColor: 'transparent',
      },
    },
  },
}));

/**
 * TimePicker 组件 - 兼容 Ant Design TimePicker API
 */
const TimePicker = ({
  // 基础属性
  value,
  defaultValue,
  onChange,
  onOk,
  onOpenChange,

  // 显示相关
  placeholder = "请选择时间",
  size = "middle",
  variant = "outlined",
  disabled = false,
  allowClear = true,
  autoFocus = false,

  // 格式化
  format = "HH:mm:ss",

  // 下拉框相关
  open,
  defaultOpen = false,
  popupClassName,
  popupStyle,
  placement = "bottomLeft",

  // 时间配置
  hourStep = 1,
  minuteStep = 1,
  secondStep = 1,
  use12Hours = false,
  hideDisabledOptions = false,

  // 禁用时间
  disabledHours,
  disabledMinutes,
  disabledSeconds,

  // 面板相关
  renderExtraFooter,

  // 图标
  suffixIcon,

  // 状态
  status,

  // 其他
  className,
  style,
  needConfirm = false,

  ...restProps
}) => {
  // 状态管理
  const [internalOpen, setInternalOpen] = useState(defaultOpen);
  const [internalValue, setInternalValue] = useState(defaultValue);
  const [anchorEl, setAnchorEl] = useState(null);
  const [tempValue, setTempValue] = useState(null);

  const inputRef = useRef(null);
  const theme = useTheme();

  // 受控状态处理
  const isControlledOpen = open !== undefined;
  const isControlledValue = value !== undefined;

  const currentOpen = isControlledOpen ? open : internalOpen;
  const currentValue = isControlledValue ? value : internalValue;

  // 处理打开状态变化
  const handleOpenChange = useCallback((newOpen) => {
    if (!isControlledOpen) {
      setInternalOpen(newOpen);
    }

    if (onOpenChange) {
      onOpenChange(newOpen);
    }
  }, [isControlledOpen, onOpenChange]);

  // 处理值变化
  const handleValueChange = useCallback((newValue, triggerEvent = 'change') => {
    const formattedValue = newValue ? dayjs(newValue) : null;
    const timeString = formattedValue ? formattedValue.format(format) : '';

    if (!isControlledValue) {
      setInternalValue(formattedValue);
    }

    if (onChange) {
      onChange(formattedValue, timeString);
    }

    // 时间选择器总是需要确认或者等待完整选择
    if (!needConfirm && triggerEvent === 'ok') {
      handleOpenChange(false);
    }
  }, [isControlledValue, onChange, format, needConfirm, handleOpenChange]);

  // 处理确认
  const handleOk = useCallback(() => {
    if (tempValue !== null) {
      handleValueChange(tempValue, 'ok');
      setTempValue(null);
    }
    handleOpenChange(false);

    if (onOk) {
      onOk();
    }
  }, [tempValue, handleValueChange, handleOpenChange, onOk]);

  // 处理取消
  const handleCancel = useCallback(() => {
    setTempValue(null);
    handleOpenChange(false);
  }, [handleOpenChange]);

  // 处理清除
  const handleClear = useCallback((e) => {
    e.stopPropagation();
    handleValueChange(null, 'clear');
  }, [handleValueChange]);

  // 处理输入框点击
  const handleInputClick = useCallback((e) => {
    if (!disabled) {
      setAnchorEl(e.currentTarget);
      handleOpenChange(true);
    }
  }, [disabled, handleOpenChange]);

  // 获取显示文本
  const getDisplayText = useCallback(() => {
    if (!currentValue) return "";
    return dayjs(currentValue).format(format);
  }, [currentValue, format]);

  // 获取输入框尺寸
  const getInputSize = () => {
    switch (size) {
      case 'large': return 'medium';
      case 'small': return 'small';
      default: return 'medium';
    }
  };

  // 生成时间选项
  const generateTimeOptions = useCallback((type) => {
    const options = [];
    let max = 24;
    let step = 1;

    if (type === 'hour') {
      max = use12Hours ? 12 : 24;
      step = hourStep;
    } else if (type === 'minute') {
      max = 60;
      step = minuteStep;
    } else if (type === 'second') {
      max = 60;
      step = secondStep;
    }

    for (let i = 0; i < max; i += step) {
      const value = use12Hours && type === 'hour' && i === 0 ? 12 : i;
      const label = value.toString().padStart(2, '0');
      const disabled = false; // 可以在这里添加禁用逻辑
      options.push({ value, label, disabled });
    }

    return options;
  }, [hourStep, minuteStep, secondStep, use12Hours]);

  // 渲染时间选择面板
  const renderTimePanel = () => {
    const timeValue = tempValue || currentValue;
    const currentTime = timeValue ? dayjs(timeValue) : dayjs();

    const hours = generateTimeOptions('hour');
    const minutes = generateTimeOptions('minute');
    const seconds = generateTimeOptions('second');

    const handleTimeChange = (type, value) => {
      let newTime = currentTime.clone();

      if (type === 'hour') {
        newTime = newTime.hour(value);
      } else if (type === 'minute') {
        newTime = newTime.minute(value);
      } else if (type === 'second') {
        newTime = newTime.second(value);
      }

      // 总是先设置临时值，不立即回填
      setTempValue(newTime);

      // 如果不需要确认，延迟一点时间后自动确认
      if (!needConfirm) {
        setTimeout(() => {
          handleValueChange(newTime, 'ok');
        }, 300);
      }
    };

    return (
      <Box>
        <StyledTimePanel>
          {/* 小时列 */}
          <Box className="time-column">
            <Box className="time-column-header">时</Box>
            <Box className="time-list">
              {hours.map((hour) => (
                <Box
                  key={hour.value}
                  className={`time-item ${currentTime.hour() === hour.value ? 'selected' : ''} ${hour.disabled ? 'disabled' : ''}`}
                  onClick={() => !hour.disabled && handleTimeChange('hour', hour.value)}
                >
                  {hour.label}
                </Box>
              ))}
            </Box>
          </Box>

          {/* 分钟列 */}
          <Box className="time-column">
            <Box className="time-column-header">分</Box>
            <Box className="time-list">
              {minutes.map((minute) => (
                <Box
                  key={minute.value}
                  className={`time-item ${currentTime.minute() === minute.value ? 'selected' : ''} ${minute.disabled ? 'disabled' : ''}`}
                  onClick={() => !minute.disabled && handleTimeChange('minute', minute.value)}
                >
                  {minute.label}
                </Box>
              ))}
            </Box>
          </Box>

          {/* 秒列 */}
          <Box className="time-column">
            <Box className="time-column-header">秒</Box>
            <Box className="time-list">
              {seconds.map((second) => (
                <Box
                  key={second.value}
                  className={`time-item ${currentTime.second() === second.value ? 'selected' : ''} ${second.disabled ? 'disabled' : ''}`}
                  onClick={() => !second.disabled && handleTimeChange('second', second.value)}
                >
                  {second.label}
                </Box>
              ))}
            </Box>
          </Box>
        </StyledTimePanel>

        {/* 额外的页脚 */}
        {renderExtraFooter && (
          <Box sx={{ p: 1, borderTop: `1px solid ${theme.palette.divider}` }}>
            {renderExtraFooter()}
          </Box>
        )}

        {/* 确认按钮 */}
        {needConfirm && (
          <Box sx={{ p: 1, borderTop: `1px solid ${theme.palette.divider}`, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button size="small" onClick={handleCancel}>
              取消
            </Button>
            <Button size="small" variant="contained" onClick={handleOk}>
              确定
            </Button>
          </Box>
        )}
      </Box>
    );
  };

  return (
    <Box className={className} style={style}>
      {/* 输入框 */}
      <TextField
        ref={inputRef}
        value={getDisplayText()}
        placeholder={placeholder}
        size={getInputSize()}
        variant={variant}
        disabled={disabled}
        error={status === 'error'}
        autoFocus={autoFocus}
        fullWidth
        onClick={handleInputClick}
        InputProps={{
          readOnly: true,
          style: { cursor: disabled ? 'default' : 'pointer' },
          endAdornment: (
            <InputAdornment position="end">
              {allowClear && currentValue && !disabled && (
                <IconButton
                  size="small"
                  onClick={handleClear}
                  sx={{ mr: 0.5 }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              )}
              {suffixIcon || <TimeIcon />}
            </InputAdornment>
          ),
        }}
        {...restProps}
      />

      {/* 下拉时间面板 */}
      <StyledPopover
        open={currentOpen}
        anchorEl={anchorEl}
        onClose={() => {
          setAnchorEl(null);
          handleOpenChange(false);
        }}
        anchorOrigin={{
          vertical: placement.includes('top') ? 'top' : 'bottom',
          horizontal: placement.includes('Right') ? 'right' : 'left',
        }}
        transformOrigin={{
          vertical: placement.includes('top') ? 'bottom' : 'top',
          horizontal: placement.includes('Right') ? 'right' : 'left',
        }}
        className={popupClassName}
        slotProps={{
          paper: {
            style: popupStyle,
          },
        }}
      >
        {renderTimePanel()}
      </StyledPopover>

      {/* 状态提示 */}
      {status === 'warning' && (
        <FormHelperText sx={{ color: 'warning.main' }}>
          警告状态
        </FormHelperText>
      )}
    </Box>
  );
};

export default TimePicker;
