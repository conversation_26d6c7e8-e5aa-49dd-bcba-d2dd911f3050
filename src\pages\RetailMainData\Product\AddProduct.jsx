import React, { useEffect, useState } from "react";
import { editProduct, addProduct } from "@s/api/product";
import { toast } from "react-toastify";
import { getFormConfig } from "./FormConfig";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import { createValidation } from "@c/Config/validationUtils.js";
import UploadImage from "@c/UploadImage";
import CustomInput from "./ProductInput";

import { useFormik } from "formik";
import { useNavigate, useLocation } from "react-router-dom";
import { getlabelList, getOutletsList, getDetail, handleUpload } from "./utils";
function AddProduct(props) {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { state } = useLocation();
  const [imageUrl, setImageUrl] = useState("");
  const [fileUrl, setFileUrl] = useState("");
  const [formConfig, setFormConfig] = useState([]);
  const [loading, setLoading] = React.useState(false);
  const [outletList, setOutletList] = useState([]);
  const [newLabels, setNewLabels] = useState([]);
  const [customePayload, setCustomePayload] = useState({});

  useEffect(() => {
    /**
     *     获取商品标签
     */
    getlabelList(state, setNewLabels);
    /**
     *    获取门店 列表
     */
    getOutletsList(state, setOutletList);
  }, []);

  useEffect(() => {
    if (state?.type == "editor") {
      /**
       *    获取 商品详情
       */
      getDetail(state, formik, setImageUrl, setCustomePayload);
    }
  }, [state]);

  useEffect(() => {
    const formConfig = getFormConfig(t, outletList);
    setFormConfig(formConfig);
  }, [outletList]);

  const handleCustomChange = (name, value) => {
    setCustomePayload((prevPayload) => ({
      ...prevPayload,
      [name]: value,
    }));
  };

  const formik = useFormik({
    initialValues: {
      id: state?.id,
      departmentId: "",
      isEmpty: "",
      name: "",
    },
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handerSubmit(values, fileUrl, setLoading);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });

  const handerSubmit = (values) => {
    let formData = new FormData();

    if (fileUrl) {
      formData.append("imageFiles", fileUrl);
    }

    if (values?.id) {
      formData.append("id", state?.id);
    }

    formData.append("name", customePayload?.productName || values?.name);
    formData.append("params", JSON.stringify(customePayload));
    formData.append("departmentId", values.departmentId);
    formData.append("isEmpty", values.isEmpty);

    setLoading(true);
    try {
      if (state?.type === "editor") {
        editProduct(formData, state?.id).then((res) => {
          toast.success(res?.message);
          navigate("/retail/list");
          setLoading(false);
        });
      } else {
        addProduct(formData).then((res) => {
          toast.success(res?.message);
          navigate("/retail/list");
          setLoading(false);
        });
      }
    } catch (error) {
      toast.error(error);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <React.Fragment>
      <RightViewLayout
        navigateBack={"/retail/list"}
        title={state?.type == "editor" ? t("product.edit") : t("product.add")}
        handleSubmit={formik.handleSubmit}
        handleCancle={() => {
          navigate("/retail/list");
        }}
        loading={loading}>
        <Grid container mt={2} mb={2}>
          <UploadImage
            label={t("common.common_company_logo")}
            imageUrl={imageUrl}
            setImageUrl={setImageUrl}
            required
            handleUpload={(file) => {
              handleUpload(file, setImageUrl, setFileUrl);
            }}></UploadImage>
        </Grid>
        <Grid container xs={12} md={12} item spacing={2} mb={1}>
          {newLabels.map((field, index) => (
            <Grid key={index} item md={6} xs={6}>
              <CustomInput
                id="AddProduct12"
                size="small"
                label={field.label}
                value={customePayload[field.name]}
                handleChange={(event) =>
                  handleCustomChange(field.name, event.target.value)
                }
                required={field.name == "productName"}
                name={field.name}
                inputProps={{
                  maxLength: 50,
                }}
                resetError={() => console.log()}
                fullWidth
                placeholder={t("product.please_enter") + field.label}
              />
            </Grid>
          ))}
        </Grid>

        <ZkFormik sx={6} formik={formik} formConfig={formConfig}></ZkFormik>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddProduct;
