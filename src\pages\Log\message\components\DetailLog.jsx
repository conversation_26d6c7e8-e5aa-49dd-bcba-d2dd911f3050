/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { forwardRef, useEffect, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@mui/material";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { getMessagelogDetail } from "@s/messagelog";
const DetailMessageLog = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const [open, setOpen] = React.useState(false);
  const [data, setData] = useState([]);
  React.useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
  }));

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpen = async (cell) => {
    setData(cell.row.original);
    setOpen(true);
    await getDetail(cell.row.original.id);
  };

  const getDetail = (id) => {
    getMessagelogDetail(id).then((res) => {});
  };
  return (
    <BootstrapDialog
      open={open}
      maxWidth="xs"
      onClose={handleClose}
      aria-describedby="alert-dialog-slide-description">
      <BootstrapDialogTitle onClose={handleClose}>
        <Typography variant="h4" component="p">
          {t("消息日志详情")}
        </Typography>
      </BootstrapDialogTitle>
      <BootstrapContent>
        <Typography>{data?.countryCode}</Typography>
        <Typography>{data?.receiver}</Typography>
        <Typography>{data?.sendTime}</Typography>
        <Typography>{data?.templateCode}</Typography>
      </BootstrapContent>
    </BootstrapDialog>
  );
});

export default DetailMessageLog;
