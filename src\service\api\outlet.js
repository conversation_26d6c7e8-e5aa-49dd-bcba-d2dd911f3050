import request from "@u/request";
const baseUrl = `${import.meta.env.VITE_APICODE}/outlet`;

export const getOutletList = (params) => {
  return request({
    url: `${baseUrl}/query/page`,
    method: "get",
    params: params,
  });
};

export const addOutlet = (params) => {
  return request({
    url: `${baseUrl}`,
    method: "POST",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const editOutlet = (params) => {
  return request({
    url: `${baseUrl}`,
    method: "PUT",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const deleteOutlet = (ids) => {
  return request({
    url: `${baseUrl}/${ids}`,
    method: "DELETE",
  });
};

export const getOutletDetail = (id) => {
  return request({
    url: `${baseUrl}/query/${id}`,
    method: "get",
  });
};

/**
 *
 * @returns 门店列表查询
 */

export const queryOutletList = (params) => {
  return request({
    url: `${baseUrl}/query/list`,
    method: "GET",
    params: params,
  });
};



/**
 *  自定义门店类型
 */


export const getCustomOutletTypeList = (id) => {
  return request({
    url: `/global/v1/outlet_type/list/${id}`,
    method: "GET",
  });
};