function BasicsGrid(props) {
  const {
    rowSpacing,
    columnSpacing,
    size,
    container,
    spacing,
    children,
    offset,
    sx = () => {},
    ...other
  } = props;

  /**
   * @props
   * size = "auto"  当断点的值为 时"auto"，列的大小将自动调整以匹配其内容的宽度
   * size = "grow"  自动布局功能为所有项目提供相等的空间。当您设置一个项目的宽度时，其他项目将自动调整大小以匹配它。
   * offset = ""      数字——例如，offset={{ md: 2 }}当视口大小等于或大于断点时，将项目向右推两列md。
                     "auto"— 这会将该项目推到网格容器的最右侧。
   */
  return (
    <Grid
      sx={sx}
      container={container}
      spacing={spacing}
      size={size}
      rowSpacing={rowSpacing}
      columnSpacing={columnSpacing}
      offset={offset}
      {...other}
    >
      {children}
    </Grid>
  );
}

export default BasicsGrid;
