import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardContent,
  CardActions,
  Collapse,
  IconButton,
  Typography,
  Box
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess
} from '@mui/icons-material';

/**
 * Schema卡片布局组件
 */
const SchemaCard = ({
  title,
  subtitle,
  description,
  children,
  actions,
  collapsible = false,
  defaultExpanded = true,
  elevation = 1,
  variant = 'outlined',
  spacing = 2,
  sx,
  ...props
}) => {
  const [expanded, setExpanded] = useState(defaultExpanded);

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  const cardContent = (
    <CardContent>
      {description && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {description}
        </Typography>
      )}
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          gap: spacing 
        }}
      >
        {children}
      </Box>
    </CardContent>
  );

  return (
    <Card
      elevation={elevation}
      variant={variant}
      className="schema-form-card"
      sx={sx}
      {...props}
    >
      {(title || subtitle || collapsible) && (
        <CardHeader
          title={title}
          subheader={subtitle}
          action={
            collapsible ? (
              <IconButton
                onClick={handleToggle}
                aria-expanded={expanded}
                aria-label={expanded ? 'Collapse' : 'Expand'}
              >
                {expanded ? <ExpandLess /> : <ExpandMore />}
              </IconButton>
            ) : null
          }
        />
      )}
      
      {collapsible ? (
        <Collapse in={expanded} timeout="auto" unmountOnExit>
          {cardContent}
        </Collapse>
      ) : (
        cardContent
      )}
      
      {actions && (
        <CardActions>
          {actions}
        </CardActions>
      )}
    </Card>
  );
};

export default SchemaCard;
