/**
 * 验证工具函数集合
 */

/**
 * 常用验证规则
 */
export const validationRules = {
  // 邮箱验证
  email: (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) ? null : 'Please enter a valid email address';
  },

  // 手机号验证（中国）
  phone: (value) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(value) ? null : 'Please enter a valid phone number';
  },

  // URL验证
  url: (value) => {
    try {
      new URL(value);
      return null;
    } catch {
      return 'Please enter a valid URL';
    }
  },

  // 身份证号验证（中国）
  idCard: (value) => {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idCardRegex.test(value) ? null : 'Please enter a valid ID card number';
  },

  // 密码强度验证
  strongPassword: (value) => {
    const hasLower = /[a-z]/.test(value);
    const hasUpper = /[A-Z]/.test(value);
    const hasNumber = /\d/.test(value);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value);
    const isLongEnough = value.length >= 8;

    if (!isLongEnough) return 'Password must be at least 8 characters long';
    if (!hasLower) return 'Password must contain at least one lowercase letter';
    if (!hasUpper) return 'Password must contain at least one uppercase letter';
    if (!hasNumber) return 'Password must contain at least one number';
    if (!hasSpecial) return 'Password must contain at least one special character';

    return null;
  },

  // 确认密码验证
  confirmPassword: (value, allData) => {
    return value === allData.password ? null : 'Passwords do not match';
  },

  // 年龄验证
  age: (value) => {
    const age = parseInt(value, 10);
    if (isNaN(age)) return 'Age must be a number';
    if (age < 0 || age > 150) return 'Please enter a valid age';
    return null;
  },

  // 用户名验证
  username: (value) => {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(value) 
      ? null 
      : 'Username must be 3-20 characters long and contain only letters, numbers, and underscores';
  }
};

/**
 * 创建必填验证器
 * @param {string} message 错误消息
 * @returns {Function}
 */
export const required = (message = 'This field is required') => {
  return (value) => {
    if (value === undefined || value === null || value === '' || 
        (Array.isArray(value) && value.length === 0)) {
      return message;
    }
    return null;
  };
};

/**
 * 创建长度验证器
 * @param {number} min 最小长度
 * @param {number} max 最大长度
 * @returns {Function}
 */
export const length = (min, max) => {
  return (value) => {
    if (typeof value !== 'string') return null;
    
    if (min !== undefined && value.length < min) {
      return `Must be at least ${min} characters long`;
    }
    if (max !== undefined && value.length > max) {
      return `Must be no more than ${max} characters long`;
    }
    return null;
  };
};

/**
 * 创建数值范围验证器
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {Function}
 */
export const range = (min, max) => {
  return (value) => {
    const num = Number(value);
    if (isNaN(num)) return 'Must be a valid number';
    
    if (min !== undefined && num < min) {
      return `Must be at least ${min}`;
    }
    if (max !== undefined && num > max) {
      return `Must be no more than ${max}`;
    }
    return null;
  };
};

/**
 * 创建正则表达式验证器
 * @param {RegExp} pattern 正则表达式
 * @param {string} message 错误消息
 * @returns {Function}
 */
export const pattern = (pattern, message = 'Invalid format') => {
  return (value) => {
    if (typeof value !== 'string') return null;
    return pattern.test(value) ? null : message;
  };
};

/**
 * 创建自定义验证器
 * @param {Function} validator 验证函数
 * @returns {Function}
 */
export const custom = (validator) => {
  return (value, allData, schema) => {
    try {
      return validator(value, allData, schema);
    } catch (error) {
      return error.message || 'Validation error';
    }
  };
};

/**
 * 组合多个验证器
 * @param {...Function} validators 验证器函数
 * @returns {Function}
 */
export const compose = (...validators) => {
  return (value, allData, schema) => {
    for (const validator of validators) {
      const error = validator(value, allData, schema);
      if (error) return error;
    }
    return null;
  };
};

/**
 * 条件验证器
 * @param {Function} condition 条件函数
 * @param {Function} validator 验证器
 * @returns {Function}
 */
export const when = (condition, validator) => {
  return (value, allData, schema) => {
    if (condition(allData, schema)) {
      return validator(value, allData, schema);
    }
    return null;
  };
};

/**
 * 异步验证器包装器
 * @param {Function} asyncValidator 异步验证函数
 * @returns {Function}
 */
export const async = (asyncValidator) => {
  return async (value, allData, schema) => {
    try {
      const result = await asyncValidator(value, allData, schema);
      return result;
    } catch (error) {
      return error.message || 'Validation error';
    }
  };
};

/**
 * 数组验证器
 * @param {Function} itemValidator 数组项验证器
 * @returns {Function}
 */
export const arrayOf = (itemValidator) => {
  return (value, allData, schema) => {
    if (!Array.isArray(value)) return 'Must be an array';
    
    for (let i = 0; i < value.length; i++) {
      const error = itemValidator(value[i], allData, schema);
      if (error) return `Item ${i + 1}: ${error}`;
    }
    return null;
  };
};

/**
 * 对象验证器
 * @param {Object} shape 对象形状定义
 * @returns {Function}
 */
export const shape = (shape) => {
  return (value, allData, schema) => {
    if (typeof value !== 'object' || value === null) {
      return 'Must be an object';
    }
    
    for (const [key, validator] of Object.entries(shape)) {
      const error = validator(value[key], allData, schema);
      if (error) return `${key}: ${error}`;
    }
    return null;
  };
};

/**
 * 预定义的验证器组合
 */
export const validators = {
  // 必填邮箱
  requiredEmail: compose(required(), validationRules.email),
  
  // 必填手机号
  requiredPhone: compose(required(), validationRules.phone),
  
  // 必填强密码
  requiredStrongPassword: compose(required(), validationRules.strongPassword),
  
  // 必填用户名
  requiredUsername: compose(required(), validationRules.username),
  
  // 可选邮箱
  optionalEmail: validationRules.email,
  
  // 可选手机号
  optionalPhone: validationRules.phone,
  
  // 年龄范围
  ageRange: compose(required(), range(0, 150)),
  
  // 用户名长度
  usernameLength: compose(required(), length(3, 20)),
  
  // 密码长度
  passwordLength: compose(required(), length(6, 50))
};

/**
 * 根据Schema生成验证器
 * @param {Object} schema 字段Schema
 * @returns {Function}
 */
export const createValidatorFromSchema = (schema) => {
  const validators = [];
  
  // 必填验证
  if (schema.required) {
    validators.push(required());
  }
  
  // 类型验证
  if (schema.type === 'string') {
    // 长度验证
    if (schema.minLength || schema.maxLength) {
      validators.push(length(schema.minLength, schema.maxLength));
    }
    
    // 模式验证
    if (schema.pattern) {
      validators.push(pattern(new RegExp(schema.pattern)));
    }
    
    // 格式验证
    if (schema.format && validationRules[schema.format]) {
      validators.push(validationRules[schema.format]);
    }
  }
  
  // 数值验证
  if (schema.type === 'number' || schema.type === 'integer') {
    if (schema.minimum !== undefined || schema.maximum !== undefined) {
      validators.push(range(schema.minimum, schema.maximum));
    }
  }
  
  return validators.length > 0 ? compose(...validators) : null;
};
