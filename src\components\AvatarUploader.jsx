import React, { useRef } from "react";
import { useTranslation } from "react-i18next";
import { Box, Button, Typography, Avatar, IconButton } from "@mui/material";
import { toast } from "react-toastify";
// import UploadIcon from "@/assets/Icons/CameraAdd.svg"; // 自定义图标
// import DeleteIcon from "@/assets/Icons/Delete.svg";
import UploadProfile from "@/assets/Icons/UploadProfile.svg";
import { pxToRem } from "@/utils/zkUtils.js";
import SvgIcon from "@c/SvgIcon.jsx";
const MAX_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = ["image/jpeg", "image/png", "image/jpg"];

export default function AvatarUploader({
  imageUrl,
  handleUpload,
  setImageUrl,
  onRemove,
}) {
  const { t } = useTranslation();
  const fileInputRef = useRef(null);

  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (!ALLOWED_TYPES.includes(file.type)) {
      toast.error(t("common.file_type_error"));
      return;
    }

    if (file.size > MAX_SIZE) {
      toast.error(t("File size exceeds 5MB limit"));
      return;
    }

    // const reader = new FileReader();
    // reader.onload = (event) => {
    //   handleUpload(file, event.target.result);
    // };
    // reader.readAsDataURL(file);
    handleUpload(file);
  };

  const handleRemove = () => {
    onRemove();
    setImageUrl(null); // Clear the image URL
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <React.Fragment>
      <div className="flex flex-row items-center h-full gap-5">
        <div>
          <Avatar
            alt="加载失败"
            src={imageUrl || UploadProfile}
            className="shadow-sm"
            sx={{
              width: pxToRem(140),
              "&:hover": { transform: "scale(1.05)" },
              transition: "transform 0.2s",
              height: pxToRem(140),
              border: "1px solid #E0E0E0",
            }}
            onClick={handleAvatarClick}></Avatar>

          <input
            type="file"
            accept=".jpg,.png,.jpeg"
            ref={fileInputRef}
            onChange={handleFileChange}
            style={{ display: "none" }}
          />
        </div>

        <div className="flex h-full flex-col  justify-between gap-2">
          {/* Info Text */}
          <div className="flex   flex-col gap-2 ">
            <Typography variant="body2" color="textSecondary">
              {t("common.common_allowed")}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {t("common.common_maximum")}
            </Typography>
          </div>
          {/* Action Buttons */}
          <div className="flex flex-row mt-4 gap-4">
            <Button
              variant="contained"
              className="rounded-md"
              sx={{
                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
              }}
              startIcon={<SvgIcon icon="solar:upload-outline" />}
              onClick={handleAvatarClick}>
              {t("common.common_upload")}
            </Button>
            {imageUrl && (
              <Button
                variant="outlined"
                color="error"
                startIcon={<SvgIcon icon={"mingcute:delete-3-line"} />}
                onClick={handleRemove}>
                {t("common.common_remove")}
              </Button>
            )}
          </div>
        </div>
      </div>
    </React.Fragment>
  );
}
