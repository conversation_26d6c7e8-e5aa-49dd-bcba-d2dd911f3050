import VerifyCode from "./VerifyCode";
export const getFormConfig = (t, formik) => {
  let formConfig = [
    {
      name: "email",
      label: t("branch_user.email"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch.branch_email_required"),
        },
        {
          type: "email",
          message: t("subscription.email_format"),
        },
      ],
    },

    {
      name: "code",
      label: t("system.base_system_verification_code"),
      type: "input",
      required: true,
      custom: true,
      renderingCustomItem: (item) => {
        return <VerifyCode item={item} formik={formik}></VerifyCode>;
      },

      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_form_username_placeholder"),
        },
      ],
    },

    {
      name: "password",
      label: t("common.common_new_password"),
      type: "password",
      viewPwd: true,
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_required_password"),
        },
        {
          type: "matches",
          matches:
            /^(?=(?:.*[A-Z]){1})(?=(?:.*[a-z]){1})(?=(?:.*\d){1})(?=(?:.*[^\w\s]){1})(?!.*\s).{8,64}$/,
          message: t("common.common_format"),
        },
      ],
    },

    {
      name: "confirmPassword",
      label: t("common.common_confirm_password"),
      type: "password",
      viewPwd: true,
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_required_confirm_password"),
        },
        {
          type: "secondConfirm",
          ref: "password",
          message: t("common.common_confirm_password_not_match"),
        },
      ],
    },
  ];

  return formConfig;
};
