import React from "react";
import TableList from "./components/TableList";
import LayoutList from "@l/components/LayoutList";
import DeteleBranch from "./components/DeteleBranch";
import { deltenant, getTenantList } from "@s/api/tenant";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import { useTableRequest } from "../../utils.js"; // 引入

const index = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [tenantId, setTenantId] = useState(null);
  const [serchName, setSeachName] = useState("");

  const {
    data,
    isLoading,
    isRefetching,
    isError,
    rowCount,
    pagination,
    setPagination,
    fetchData,
    search,
    reset,
  } = useTableRequest(getTenantList);

  // 删除
  const handlerDetele = async () => {
    try {
      const res = await deltenant(tenantId);
      toast.success(res?.message);
      fetchData();
    } catch {
      toast.error(t("common.deleteFailed"));
    } finally {
      setOpen(false);
    }
  };

  // 搜索
  const handlerSeacher = () => {
    search({ name: serchName });
  };

  // 清空
  const handleClear = () => {
    setSeachName("");
    reset();
  };

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
        setTenantId={setTenantId}
        setOpen={setOpen}
        getTableData={fetchData}></TableList>
    );
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("branch.title")}
        isSearch={true}
        onClick={handlerSeacher}
        serchName={serchName}
        setSeachName={setSeachName}
        onClear={handleClear} // 添加 onClear 属性
        content={rederTable()}></LayoutList>

      <DeteleBranch
        open={open}
        setOpen={setOpen}
        handlerDetele={handlerDetele}></DeteleBranch>
    </React.Fragment>
  );
};

export default index;
