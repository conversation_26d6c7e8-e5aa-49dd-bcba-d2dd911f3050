import { SvgIconStyle } from "./utils.js";
import SvgIcon from "@c/SvgIcon";
const IconsGroup = () => {
  return (
    <Grid
      item
      xs={0.8}
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
      }}>
      <Grid
        sx={{
          ...SvgIconStyle,
          marginTop: "-10px",
        }}>
        <SvgIcon
          width="16px"
          height="16px"
          localIcon="Right_double_arrows"></SvgIcon>
      </Grid>

      <Grid sx={SvgIconStyle}>
        <SvgIcon
          width="20px"
          height="20px"
          localIcon="Right_single_arrows"></SvgIcon>
      </Grid>

      <Grid sx={SvgIconStyle}>
        <SvgIcon
          width="20px"
          height="20px"
          localIcon="Left_single_arrows"></SvgIcon>
      </Grid>

      <Grid sx={SvgIconStyle}>
        <SvgIcon
          width="16px"
          height="16px"
          localIcon="Left_double_arrows"></SvgIcon>
      </Grid>
    </Grid>
  );
};

export default IconsGroup;
