@descriptions-prefix: ~"w-descriptions";

.@{descriptions-prefix} table {
  display: table !important;
  margin-bottom: 0 !important;
  margin: 0;
  border-spacing: 0;
  border-collapse: collapse;
  table-layout: fixed;
  width: 100% !important;
}

.@{descriptions-prefix} {
  background: #fff;
  &-title {
    font-weight: bold;
    text-align: left;
    margin-bottom: 12px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 18px;
    line-height: 1.5;
  }
  &-tbody {
    word-wrap: break-word;
  }
  & &-row:nth-child(2n) {
    background-color: transparent;
  }
  &-item {
    &-label {
      font-size: 14px;
      // white-space: nowrap;
      font-weight: normal;
      &::after {
        position: relative;
        top: -0.5px;
        margin: 0 8px 0 2px;
        content: " ";
      }
    }
    &-colon::after {
      content: ":";
    }
    &-colon {
      color: rgba(0, 0, 0, 0.95);
    }
    &-content {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
  &-small&-bordered tbody tr th,
  &-small&-bordered tbody &-row td {
    padding: 4px 8px;
  }
  &-large&-bordered tbody tr th,
  &-large&-bordered tbody &-row td {
    padding: 14px 16px;
  }
}

.@{descriptions-prefix} {
  &-bordered {
    overflow: hidden;
    border-radius: 3px;
    border: 1px solid #f0f0f0;
  }
  &-bordered tbody {
    tr th {
      padding: 8px 12px;
      background-color: #fafafa;
      font-weight: normal;
    }
    tr th,
    tr td {
      border-top: 1px solid #f0f0f0;
      // border-right: 1px solid #dfe2e5;
      border-right: 1px solid #f0f0f0;
      text-align: left;
      border-left: 0;
      border-bottom: 0;
    }
    &:first-child tr:first-child th,
    &:first-child tr:first-child td {
      border-top: 0;
    }
  }
  &:not(&-bordered) &-row td {
    padding-bottom: 8px;
  }
  &-bordered &-row td {
    padding: 8px 12px;
  }
  &-bordered &-title {
    margin-bottom: 0;
    padding: 7px 12px;
  }
  &-bordered &-item {
    &-label:last-child,
    &-content:last-child {
      border-right: none;
    }
  }
}
