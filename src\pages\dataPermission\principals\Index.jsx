import React, { useRef, forwardRef, useImperativeHandle } from "react";
import BootstrapDialog from "../components/ZkDialog";
import { useTranslation } from "react-i18next";
import Outlets from "../outlets/Index";
import { filterValidItems, comBinationID } from "../components/utils";
import IconsGroup from "../components/IconsGroup";
import { getStoreList } from "@/service/api/dataPermission";
import ZkSearch from "./ZkSearch";
import LeftTable from "./LeftTable.jsx";
import RightTable from "./RightTable.jsx";
const Index = forwardRef((props, ref) => {
  const {
    dataType,
    principalsOpen,
    setPrincipalsOpen,
    isUserAction,
    setIsUserAction,
    setEditorInit,
    list,
    setList,
    setEditorIds,
    setAddDataIds,
  } = props;
  const { t } = useTranslation();
  const rightTableRef = useRef();
  const tableRef = useRef(null);
  const [rowSelection, setRowSelection] = useState([]);
  // 选中的行（零售商ID或代理商ID）
  const [selectedRows, setSelectedRows] = useState([]);
  const [searchName, setSearchName] = useState(null);

  useEffect(() => {
    if (!tableRef.current) return;

    // 获取当前选中的数据
    const selectedData = tableRef.current
      .getSelectedRowModel()
      .rows.map((row) => row.original);

    setSelectedRows((prevMap) => {
      const newMap = new Map(prevMap);

      selectedData.forEach((item) => {
        newMap.set(item.id, item);
      });

      return newMap;
    });
  }, [rowSelection]);

  //  以下是门店弹窗相关参数

  const outletsRef = useRef();
  const [rowId, setRowId] = useState(null);
  const [outletOpen, setOutletOpen] = useState(false);

  useImperativeHandle(ref, () => ({
    setSelectedRows: (rows, isEditInitialization = false) => {
      if (!isEditInitialization) {
        setIsUserAction(true);
      }

      setSelectedRows(new Map(rows.map((row) => [row.id, row])));

      const newRowSelection = {};
      rows.forEach((row) => {
        newRowSelection[row.id] = true;
      });

      setRowSelection(newRowSelection);
    },
  }));

  useEffect(() => {
    if (principalsOpen && isUserAction) {
      setSelectedRows([]);
      setRowSelection({});
      // 重置用户操作标志
      setIsUserAction(false);
    }
  }, [dataType, principalsOpen, isUserAction]);

  const handlerSubmit = async () => {
    if (dataType == "6") {
      const IdsList = comBinationID(
        selectedRows,
        outletsRef.current.selectOutlets
      );

      const response = await getStoreList(IdsList);
      setList(response?.data || []);
      setEditorIds(IdsList);
      setAddDataIds(IdsList);
    } else {
      setList(filterValidItems(selectedRows));
      setAddDataIds(filterValidItems(selectedRows).map((item) => item.id));
      setEditorIds(filterValidItems(selectedRows).map((item) => item.id));
    }
    setPrincipalsOpen(false);
    setEditorInit(false);
  };

  const handleDeleteRetailerAndOutlets = (retailerId) => {
    setSelectedRows((prevMap) => {
      const newMap = new Map(prevMap);
      newMap.delete(retailerId);
      return newMap;
    });

    setRowSelection((prev) => {
      const newSelection = { ...prev };
      delete newSelection[retailerId];
      return newSelection;
    });

    // 删除与该零售商关联的所有门店
    if (outletsRef.current) {
      outletsRef.current.deleteOutletsByRetailerId(retailerId);
    }
  };

  return (
    <React.Fragment>
      <BootstrapDialog
        open={principalsOpen}
        setOpen={setPrincipalsOpen}
        title={
          dataType == "6"
            ? t("datascope.principals_selection")
            : t("datascope.partner_selection")
        }
        handlerSubmit={handlerSubmit}>
        <Grid container xs={12}>
          <ZkSearch
            dataType={dataType}
            searchName={searchName}
            setSearchName={setSearchName}
            rightTableRef={rightTableRef}></ZkSearch>

          <Grid item xs={12} display={"flex"}>
            <LeftTable
              ref={rightTableRef}
              tableRef={tableRef}
              dataType={dataType}
              rowSelection={rowSelection}
              setRowSelection={setRowSelection}></LeftTable>

            <IconsGroup></IconsGroup>

            <RightTable
              dataType={dataType}
              onDeleteRetailer={handleDeleteRetailerAndOutlets}
              selectedRows={selectedRows}
              setRowSelection={setRowSelection}
              setSelectedRows={setSelectedRows}
              setOutletOpen={setOutletOpen}
              setRowId={setRowId}></RightTable>
          </Grid>
        </Grid>
      </BootstrapDialog>

      <Outlets
        ref={outletsRef}
        list={list}
        open={outletOpen}
        setOpen={setOutletOpen}
        deleteOutletsByRetailerId={(retailerId) => {
          // 实现删除特定零售商的所有门店的逻辑
          setList((prevList) =>
            prevList.filter((item) => item.retailerId !== retailerId)
          );
        }}
        rowId={rowId}></Outlets>
    </React.Fragment>
  );
});

export default Index;
