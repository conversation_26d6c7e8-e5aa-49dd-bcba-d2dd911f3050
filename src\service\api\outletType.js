import request from "@/utils/request";

// // 门店列表
// export const getProductList = (params) => {
//   return request({
//     url: `/v1/product_type/query/page`,
//     method: "get",
//     params: params,
//   });
// };

// 头部 查询接口
export const queryProduct = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/outlet_type`,
    method: "get",
    data: params,
  });
};

// 批量删除
export const deleteProduct = (ids) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/outlet_type/${ids}`,
    method: "delete",
  });
};

//   新增
export const saveProduct = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/outlet_type`,
    method: "post",
    data: params,
  });
};

// 修改 商品标签
export const editorProduct = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/outlet_type`,
    method: "put",
    data: params,
  });
};
