import {
  FormHelperText,
  InputLabel,
  OutlinedInput,
  IconButton,
  Stack,
  InputAdornment,
} from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import RequirePoint from "../RequirePoint";
import { useState, useRef } from "react";
import LocationIcon from "@a/Icons/Location.svg?react";
import SelectMapBaidu from "@c/baiduMap/selectMap"; //百度地图
import SelectMapGoogle from "@c/googleMap/selectMapGoogle"; //谷歌地图
import { useTranslation } from "react-i18next";
const ZkInput = (props) => {
  const selectMapRef = useRef(null);
  const { t } = useTranslation();
  const {
    formik = null,
    label,
    placeholder = t("common.common_enter") + `${label}`,
    handleBlur,
    handleChange,
    name,
    error,
    disabled = false,
    labelpostion,
    type = "text",
    ...orther
  } = props;

  const blurFn = (e) => {
    if (formik?.handleBlur) {
      formik?.handleBlur(e);
    }

    if (handleBlur) {
      handleBlur(e);
    }
  };
  const changeFn = (e) => {
    if (formik?.handleChange) {
      formik?.handleChange(e);
    }
    if (handleChange) {
      handleChange(e);
    }
  };

  // 点图选点回调事件
  const getLocationGoogle = (lat, lng, location) => {
    formik.setFieldValue(name, location?.address);
    formik.setFieldValue("lat", lat);
    formik.setFieldValue("lng", lng);
  };

  const getLocation = (value, location) => {
    formik.setFieldValue(name, location);
    formik.setFieldValue("lng", value.lng);
    formik.setFieldValue("lat", value.lat);
  };

  // 切换地图

  const handleSwitchMap = () => {
    const lang = localStorage.getItem("zkBioCloudMediaLang");

    if (lang == "en") {
      return (
        <SelectMapGoogle ref={selectMapRef} getLocation={getLocationGoogle} />
      );
    } else {
      return <SelectMapBaidu ref={selectMapRef} getLocation={getLocation} />;
    }
  };

  return (
    <>
      <Stack spacing={1}>
        <Stack
          direction={labelpostion === "left" ? "row" : "column"}
          sx={{
            alignItems: labelpostion === "left" ? "flex-start" : "",
          }}
          spacing={1}>
          {label && (
            <InputLabel
              style={{
                marginTop: labelpostion === "left" ? "12px" : "",
                color: "#474b4fcc",
                fontSize: "14px",
              }}
              htmlFor={"zkInput_" + name}>
              {label} {props.required && <RequirePoint></RequirePoint>}
            </InputLabel>
          )}

          <Stack
            sx={{
              flexGrow: 1,
              width: "100%",
            }}>
            <OutlinedInput
              id={"zkInput_" + name}
              type={type}
              autoComplete="off"
              value={formik?.values[name]}
              name={name}
              onBlur={blurFn}
              onChange={changeFn}
              placeholder={placeholder}
              fullWidth
              error={Boolean(
                (formik?.touched[name] && formik?.errors[name]) || error
              )}
              endAdornment={
                <IconButton
                  onClick={(e) => {
                    e.stopPropagation();
                    selectMapRef.current.handleClickOpen();
                  }}>
                  <LocationIcon />
                </IconButton>
              }
              disabled={disabled}
              {...orther}
            />
            {((formik?.touched[name] && formik?.errors[name]) || error) && (
              <FormHelperText error id={`standard-weight-helper-text-${name}`}>
                {formik?.errors[name] || error}
              </FormHelperText>
            )}
          </Stack>
        </Stack>
      </Stack>
      {/* 谷歌地图  注意回显方法 */}
      {handleSwitchMap()}
    </>
  );
};

export default ZkInput;
