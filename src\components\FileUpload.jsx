import React from "react";
import { useDropzone } from "react-dropzone";
import { Grid } from "@mui/material";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
const FileUpload = ({
  onUpload,
  accept = {
    "image/jpeg": [".jpeg", ".png"],
    "application/pdf": [".pdf", ".doc"],
  },
  multiple = false,
  children,
  dropCom = null,
}) => {
  const { i18n, t } = useTranslation();
  const onDrop = async (acceptedFiles) => {
    if (multiple && acceptedFiles && acceptedFiles.length > 1) {
      toast.error(t("subscription.singleFile"));
    } else {
      onUpload(acceptedFiles);
    }
  };
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: multiple,
    accept: accept,
    onError: (e) => {
      toast.error(e);
    },
  });
  const InputProps = getInputProps();
  return (
    <Grid
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        cursor: "pointer",
      }}
      {...getRootProps()}>
      <input {...InputProps} />
      {isDragActive ? (
        <Grid
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "column",
          }}>
          {dropCom ? dropCom : <div>{t("subscription.dropTip")}</div>}
        </Grid>
      ) : (
        <Grid
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "column",
          }}>
          {children}
        </Grid>
      )}
    </Grid>
  );
};

export default FileUpload;
