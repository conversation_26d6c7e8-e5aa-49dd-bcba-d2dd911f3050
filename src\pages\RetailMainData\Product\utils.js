import { getProductDetail, getProductLabel } from "@s/api/product";
import { queryOutletList } from "@/service/api/outlet.js";

/**
 *     获取商品标签
 */
export const getlabelList = async (state, setNewLabels) => {
  const reseponse = await getProductLabel({ departmentId: state?.retailerId });
  let data = reseponse?.data;
  let productName;
  data = data?.filter((x) => {
    if (x.name == "productName") {
      productName = x;
      return false;
    } else {
      return true;
    }
  });
  if (productName) {
    data.unshift(productName);
  }
  setNewLabels(data);
};

/**
 *    获取门店 列表
 */

export const getOutletsList = async (state, setOutletList) => {
  const response = await queryOutletList({
    type: 3,
    parentId: state?.retailerId,
  });
  setOutletList(response?.data);
};

/**
 *    获取 商品详情
 */
export const getDetail = async (
  state,
  formik,
  setImageUrl,
  setCustomePayload
) => {
  const reseponse = await getProductDetail(state?.id);
  const {
    name,
    productPhotoList,
    isEmpty,
    departmentId,
    productTypeValueList,
  } = reseponse?.data;

  setImageUrl(productPhotoList[0]?.photoUrl);
  formik.setFieldValue("isEmpty", isEmpty);
  formik.setFieldValue("departmentId", departmentId);
  formik.setFieldValue("name", name);

  let params = {};
  productTypeValueList?.forEach((item) => {
    params[item?.name] = item.value;
  });

  setCustomePayload(params);
};

export const handleUpload = (file, setImageUrl, setFileUrl) => {
  setFileUrl(file);
  const reader = new FileReader();
  reader.onload = (e) => {
    setImageUrl(e.target.result);
  };
  reader.readAsDataURL(file);
};
