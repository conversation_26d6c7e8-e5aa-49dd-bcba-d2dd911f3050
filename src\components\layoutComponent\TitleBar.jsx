import React from "react";
import { useNavigate } from "react-router-dom";
import KeyBoardLeftArrowIcon from "@/assets/Icons/KeyboardArrowLeftIcon.svg?react";
import { useTranslation } from "react-i18next";

const TitleBar = (props) => {
  let navigate = useNavigate();
  const { t } = useTranslation();
  return (
    <>
      <Box
        display={"flex"}
        width="100%"
        alignItems={"center"}
        height={"80px"}
        onClick={() =>
          props?.navigateBack == "-1"
            ? navigate(-1)
            : navigate(props?.navigateBack)
        }>
        {props.navigateBack && (
          <Grid ml={2} mr={2}>
            <Tooltip title={t("返回")}>
              <KeyBoardLeftArrowIcon></KeyBoardLeftArrowIcon>
            </Tooltip>
          </Grid>
        )}

        <Typography
          variant="h5"
          sx={{
            fontSize: "20px",
            marginLeft: props.navigateBack ? "0px" : "30px",
          }}>
          {props.title}
        </Typography>

        {props.children}
      </Box>
    </>
  );
};
export default TitleBar;
