import React from "react";
import CustInput from "@c/CustInput";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import * as Yup from "yup";
function SecuritySettings() {
  // 国际化
  const { t } = useTranslation();
  const areaFormik = useFormik({
    initialValues: {
      oldpassword: "",
      newpassword: "",
      confirmpassword: "",
    },
    onSubmit: (values) => {},

    validationSchema: Yup.object().shape({
      oldpassword: Yup.string().required(t("common.common_area_name_not_null")),
      newpassword: Yup.string()
        .max(20, t("最多不能超过20个字符"))
        .matches(/^[a-zA-Z\u4e00-\u9fa5]+[a-zA-Z\d\u4e00-\u9fa5_\s]*$/, {
          message: t("密码不符合规则"),
          excludeEmptyString: true,
        })
        .required(t("新密码是必传的")),
      confirmpassword: Yup.string()
        .max(20, t("最多不能超过20个字符"))
        .matches(/^[a-zA-Z\u4e00-\u9fa5]+[a-zA-Z\d\u4e00-\u9fa5_\s]*$/, {
          message: t("密码不符合规则"),
          excludeEmptyString: true,
        })
        .required(t("新密码是必传的")),
    }),
  });

  return (
    <React.Fragment>
      <form noValidate onSubmit={areaFormik.handleSubmit}>
        <Grid xs={12} md={6} lg={12} sm={4}>
          <Grid>
            <CustInput
              name={"oldpassword"}
              label={"Old Password*"}
              required={true}
              type={"password"}
              formik={areaFormik}></CustInput>
          </Grid>

          <Grid mt={2}>
            <CustInput
              name={"newpassword"}
              label={"New Password"}
              required={true}
              type={"password"}
              formik={areaFormik}></CustInput>
          </Grid>

          <Grid mt={2}>
            <CustInput
              name={"confirmpassword"}
              label={"Confirm Password"}
              required={true}
              type={"password"}
              formik={areaFormik}></CustInput>
          </Grid>

          <Button
            id="AddOutlet-button-01"
            variant="contained"
            size="medium"
            className="text-transform-none"
            type="submit"
            style={{
              borderRadius: "15px",
              opacity: 1,
              padding: "12px 16px",
              width: "248px",
              height: "65px",
              position: "absolute",
              right: "40px",
              marginTop: "20px",
              font: "normal normal bold 16px/20px Proxima Nova",
              color: "#FFFFFF",
              background:
                "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
            }}>
            Save Changes
          </Button>
        </Grid>
      </form>
    </React.Fragment>
  );
}

export default SecuritySettings;
