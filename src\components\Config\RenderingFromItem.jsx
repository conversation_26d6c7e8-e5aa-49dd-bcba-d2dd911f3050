import ZkSelect from "./ZkSelect.jsx";
import ZkInput from "./ZkInput";
import ZkSwitch from "./ZkSwitch";
import ZkTextarea from "./ZkTextarea";
import ZkRadio from "./ZkRadio";
import ZkMobile from "./ZkMobile";
import { Grid } from "@mui/material";
import ZkAddress from "./ZkAddress";
import ZkAutocomplete from "./ZkAutocomplete";
import ZKTreeSelect from "./ZktreeSelect.jsx";
import ZkTimeDate from "./ZkTimeDate.jsx";
const RenderingFromItem = (props) => {
  const {
    config,
    renderingCustomItem = () => {
      return "没有自定义渲染方法";
    },
    formik = null,
    labelpostion = "top",
    defaultBox = (result, item, index) => {
      return (
        <Grid
          key={`${item.name + index || "field" + index}`}
          sx={{ color: "red" }}
          item
          xs={item.sx ? item.sx : props.sx}>
          {result}
        </Grid>
      );
    },
  } = props;

  let formConfig = config.map((item, index) => {
    const { type, conditionalRendering, display = "true", ...orther } = item;

    orther.labelpostion = labelpostion;

    if (item.custom) {
      if (conditionalRendering) {
        if (conditionalRendering(formik)) {
          return item.renderingCustomItem
            ? item.renderingCustomItem(item, formik)
            : renderingCustomItem(item, formik);
        } else {
          return null;
        }
      } else {
        return item.renderingCustomItem
          ? item.renderingCustomItem(item, formik)
          : renderingCustomItem(item, formik);
      }
    } else {
      if (conditionalRendering) {
        //条件渲染
        if (conditionalRendering(formik)) {
          if (type === "input" || (type === "password" && display)) {
            let result = (
              <ZkInput key={item.name} formik={formik} {...orther}></ZkInput>
            );
            return defaultBox(result, item);
          } else if (type === "select") {
            let result = (
              <ZkSelect key={item.name} formik={formik} {...orther}></ZkSelect>
            );
            return defaultBox(result, item);
          } else if (type === "Radio") {
            let result = (
              <ZkRadio key={item.name} formik={formik} {...orther}></ZkRadio>
            );
            return defaultBox(result, item);
          } else if (type === "switch") {
            let result = (
              <ZkSwitch key={item.name} formik={formik} {...orther}></ZkSwitch>
            );
            return defaultBox(result, item);
          } else if (type === "textArea") {
            let result = (
              <ZkTextarea
                key={item.name}
                formik={formik}
                {...orther}></ZkTextarea>
            );
            return defaultBox(result, item);
          } else if (type === "mobile") {
            let result = (
              <ZkMobile key={item.name} formik={formik} {...orther}></ZkMobile>
            );
            return defaultBox(result, item);
          } else if (type === "address") {
            let result = (
              <ZkAddress
                key={item.name}
                formik={formik}
                {...orther}></ZkAddress>
            );
            return defaultBox(result, item);
          } else if (type === "autoComplate") {
            let result = (
              <ZkAutocomplete
                key={item.name}
                formik={formik}
                {...orther}></ZkAutocomplete>
            );
            return defaultBox(result, item);
          } else if (type === "tree") {
            let result = (
              <ZKTreeSelect
                key={item.name}
                formik={formik}
                {...orther}></ZKTreeSelect>
            );
            return defaultBox(result, item);
          } else if (type === "date") {
            let result = (
              <ZkTimeDate
                key={item.name}
                formik={formik}
                {...orther}></ZkTimeDate>
            );
            return defaultBox(result, item);
          } else {
            return `暂无支持${type}类型表单`;
          }
        } else {
          return null;
        }
      } else {
        if (type === "input" || (type == "password" && display)) {
          let result = (
            <ZkInput
              key={item.name}
              formik={formik}
              type={type}
              {...orther}></ZkInput>
          );
          return defaultBox(result, item);
        } else if (type === "select") {
          let result = (
            <ZkSelect key={item.name} formik={formik} {...orther}></ZkSelect>
          );
          return defaultBox(result, item);
        } else if (type === "Radio") {
          let result = (
            <ZkRadio key={item.name} formik={formik} {...orther}></ZkRadio>
          );
          return defaultBox(result, item);
        } else if (type === "switch") {
          let result = (
            <ZkSwitch key={item.name} formik={formik} {...orther}></ZkSwitch>
          );
          return defaultBox(result, item);
        } else if (type === "textArea") {
          let result = (
            <ZkTextarea
              key={item.name}
              formik={formik}
              {...orther}></ZkTextarea>
          );
          return defaultBox(result, item);
        } else if (type === "mobile") {
          let result = (
            <ZkMobile key={item.name} formik={formik} {...orther}></ZkMobile>
          );
          return defaultBox(result, item);
        } else if (type === "address") {
          let result = (
            <ZkAddress key={item.name} formik={formik} {...orther}></ZkAddress>
          );
          return defaultBox(result, item);
        } else if (type === "autoComplate") {
          let result = (
            <ZkAutocomplete
              key={item.name}
              formik={formik}
              {...orther}></ZkAutocomplete>
          );
          return defaultBox(result, item);
        } else if (type === "tree") {
          let result = (
            <ZKTreeSelect
              key={item.name}
              formik={formik}
              {...orther}></ZKTreeSelect>
          );
          return defaultBox(result, item);
        } else if (type === "date") {
          let result = (
            <ZkTimeDate
              key={item.name}
              formik={formik}
              {...orther}></ZkTimeDate>
          );
          return defaultBox(result, item);
        } else {
          return null;
        }
      }
    }
  });
  return formConfig;
};

export default RenderingFromItem;
