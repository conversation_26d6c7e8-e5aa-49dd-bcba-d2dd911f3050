/* eslint-disable no-debugger */
/* eslint-disable no-undef */
import React, { useEffect, useState, useMemo, useRef } from "react";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import { InputAdornment, IconButton } from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import Grid from "@mui/material/Grid";
import SearchIcon from "@mui/icons-material/Search";
import Typography from "@mui/material/Typography";
import parse from "autosuggest-highlight/parse";
import { debounce } from "@mui/material/utils";
import { useTranslation } from "react-i18next";

const autocompleteService = { current: null };

export default function GoogleMaps(props) {
  const { setZoom, onDetailAddress } = props;
  const { t } = useTranslation();

  const [value, setValue] = React.useState(null);
  const [inputValue, setInputValue] = React.useState("");
  const [options, setOptions] = React.useState([]);

  const fetch = React.useMemo(
    () =>
      debounce((request, callback) => {
        autocompleteService.current.getPlacePredictions(request, callback);
      }, 400),
    []
  );

  React.useEffect(() => {
    let active = true;

    if (!autocompleteService.current && window?.google) {
      autocompleteService.current =
        new window.google.maps.places.AutocompleteService();
    }
    if (!autocompleteService.current) {
      return undefined;
    }

    if (inputValue === "") {
      setOptions(value ? [value] : []);
      return undefined;
    }

    fetch({ input: inputValue }, (results) => {
      console.log("inputValue", inputValue);
      console.log("results", results);
      if (active) {
        let newOptions = [];

        if (value) {
          newOptions = [value];
        }

        if (results) {
          newOptions = [...newOptions, ...results];
        }

        setOptions(newOptions);
      }
    });

    return () => {
      active = false;
    };
  }, [value, inputValue, fetch]);
  // 搜索
  const handleSearch = (valueAddress) => {
    if (valueAddress["place_id"]) {
      getPlaceDetails(valueAddress["place_id"]).then((location) => {
        setZoom(15);
        onDetailAddress({
          ...valueAddress,
          location: {
            lat: location.lat(),
            lng: location.lng(),
          },
        });
      });
      return;
    }
  };
  // 获取详情
  const getPlaceDetails = async (placeId) => {
    return new Promise((resolve, reject) => {
      var map = new google.maps.Map(document.createElement("div"));
      var placeService = new window.google.maps.places.PlacesService(map);
      placeService.getDetails({ placeId: placeId }, function (result, status) {
        if (
          status === window.google.maps.places.PlacesServiceStatus.OK &&
          result
        ) {
          resolve(result.geometry.location);
        } else {
          reject(status);
        }
      });
    });
  };
  return (
    <Autocomplete
      sx={props.sx}
      id="google-map-search"
      label="address"
      size="small"
      disablePortal
      disableClearable //清除按钮
      getOptionLabel={(option) =>
        typeof option === "string" ? option : option.description
      }
      filterOptions={(x) => x}
      options={options}
      autoComplete
      includeInputInList
      filterSelectedOptions
      value={value}
      noOptionsText={t("common.common_input_location_search")}
      onChange={(event, newValue) => {
        setOptions(newValue ? [newValue, ...options] : options);
        setValue(newValue);
        handleSearch(newValue);
      }}
      onInputChange={(event, newInputValue) => {
        setInputValue(newInputValue);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label=""
          placeholder={props.placeholder}
          fullWidth
          size="small"
          sx={{
            width: 300,
            marginTop: 1,
            "& .MuiInputBase-root": {
              paddingRight: "8px", // 减少右侧内边距
            },
          }}
          // InputProps={{
          //   ...params.InputProps,
          //   type: "search",
          //   endAdornment: (
          //     <InputAdornment position="end">
          //       <IconButton onClick={() => handleSearch(inputValue)}>
          //         <SearchIcon />
          //       </IconButton>
          //     </InputAdornment>
          //   ),
          // }}
        />
      )}
      renderOption={(props, option) => {
        const matches =
          option && option.structured_formatting
            ? option.structured_formatting.main_text_matched_substrings || []
            : [];

        const parts = parse(
          option && option.structured_formatting
            ? option.structured_formatting.main_text
            : "",
          matches.map((match) => [match.offset, match.offset + match.length])
        );

        return (
          <li {...props}>
            <Grid container alignItems="center">
              <Grid item sx={{ display: "flex", width: 44 }}>
                <LocationOnIcon sx={{ color: "text.secondary" }} />
              </Grid>
              <Grid
                item
                sx={{ width: "calc(100% - 44px)", wordWrap: "break-word" }}>
                {parts.map((part, index) => (
                  <Box
                    key={index}
                    component="span"
                    sx={{ fontWeight: part.highlight ? "bold" : "regular" }}>
                    {part.text}
                  </Box>
                ))}
                <Typography variant="body2" color="text.secondary">
                  {option && option.structured_formatting
                    ? option.structured_formatting.secondary_text
                    : ""}
                </Typography>
              </Grid>
            </Grid>
          </li>
        );
      }}
    />
  );
}
