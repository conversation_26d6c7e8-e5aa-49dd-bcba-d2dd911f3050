// 看板
const scriptionRoute = [
  {
    path: "/scriptionRoute",
    component: () => import("@p/subscription/index"),
    meta: {
      id: "8963896573823699866",
      i18n: "scription_list",
    },
  },

  {
    path: "/add/scription",
    component: () => import("@p/subscription/AddSubscription"),
    meta: {
      id: "13435565656565866",
      i18n: "scription_add",
    },
  },

  {
    path: "/scription/list",
    component: () => import("@p/subscription/Table"),
    meta: {
      id: "13435565659676745456565866",
      i18n: "scription_record_list",
    },
  },
];
export default scriptionRoute;
