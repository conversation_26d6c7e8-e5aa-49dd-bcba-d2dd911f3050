import React from "react";
import { useTranslation } from "react-i18next";
import ZKInput from "./ZKInput";
import AnimateButton from "@c/@extended/AnimateButton";
import LoadingButton from "@mui/lab/LoadingButton";

function ZKSearch({ data, setData }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [retailName, setRetailName] = useState("");
  const [outletName, setOutletName] = useState("");

  // 用ref保存最初始数据，避免跟着data变化
  const originalDataRef = useRef([]);

  useEffect(() => {
    if (data && data.length > 0 && originalDataRef.current.length === 0) {
      originalDataRef.current = data;
    }
  }, [data]);

  const handlerSubmit = () => {
    const filteredData = originalDataRef.current.filter((item) => {
      const retailMatch = retailName.trim()
        ? item.principalName
            ?.toLowerCase()
            .includes(retailName.trim().toLowerCase())
        : true;
      const outletMatch = outletName.trim()
        ? item.outletName
            ?.toLowerCase()
            .includes(outletName.trim().toLowerCase())
        : true;
      return retailMatch && outletMatch;
    });
    setData(filteredData);
  };

  const handleReset = () => {
    setRetailName("");
    setOutletName("");
    setData(originalDataRef.current);
  };

  return (
    <React.Fragment>
      <Grid
        container
        xs={12}
        sx={{
          height: "106px",
          background: "#F3F9FD 0% 0% no-repeat padding-box",
          display: "flex",
          justifyContent: "flex-start",
          alignContent: "center",
          gap: 3,
          paddingLeft: "15px",
        }}>
        <ZKInput
          label={t("datascope.retail_client_name")}
          placeholder={t("datascope.retail_client_name_placeholder")}
          isSearch={false}
          value={retailName}
          handleChange={(e) => {
            setRetailName(e.target.value);
          }}
          sx={{
            background: "#fff",
            border: "1px solid #fff",
          }}></ZKInput>
        <ZKInput
          label={t("outlets.name")}
          placeholder={t("datascope.enter_outlet_name")}
          isSearch={false}
          value={outletName}
          handleChange={(e) => {
            setOutletName(e.target.value);
          }}
          sx={{
            background: "#fff",
            border: "1px solid #fff",
          }}></ZKInput>

        <Grid
          item
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 3,
            mt: 3.5,
          }}>
          <Button
            style={{
              width: "134px",
              height: "40px",
              borderRadius: "5px",
              background: "#fff",
              border: "1px solid #E3E3E3",
            }}
            onClick={handleReset}>
            {t("common.common_reset")}
          </Button>

          <AnimateButton>
            <LoadingButton
              loading={loading}
              disableElevation
              // disabled={formik?.isSubmitting}
              fullWidth
              type="submit"
              onClick={handlerSubmit}
              variant="contained"
              style={{
                width: "134px",
                height: "40px",
                borderRadius: "5px",

                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
              }}>
              {t("common.common_query")}
            </LoadingButton>
          </AnimateButton>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default ZKSearch;
