import { createSlice } from "@reduxjs/toolkit";
// 初始化状态
const initialState = {
  dicts: {},
};

// 创建分片
const dicts = createSlice({
  name: "dicts",
  initialState,
  reducers: {
    setDicts(state, action) {
      state.dicts[action.payload.type] = action.payload.data;
    },
    clearDicts(state) {
      state.dicts = {};
    },
  },
});

// 导出
export default dicts.reducer;

export const { clearDicts, setDicts } =
dicts.actions;
