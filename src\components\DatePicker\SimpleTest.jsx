import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  FormControl, 
  FormLabel,
  Alert,
  AlertTitle
} from '@mui/material';
import DatePicker from './index';
import dayjs from 'dayjs';

const SimpleTest = () => {
  const [basicValue, setBasicValue] = useState();

  const handleChange = (value, dateString) => {
    console.log('DatePicker onChange:', { value, dateString });
    setBasicValue(value);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Typography variant="h4" component="h1" gutterBottom align="center">
        基础日期选择测试
      </Typography>
      
      <Alert severity="warning" sx={{ mb: 4 }}>
        <AlertTitle>调试信息</AlertTitle>
        <Typography variant="body2">
          请打开浏览器控制台查看点击事件是否触发
        </Typography>
      </Alert>

      <Paper sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          📅 基础日期选择
        </Typography>
        
        <FormControl fullWidth sx={{ mb: 2 }}>
          <FormLabel sx={{ mb: 1 }}>选择日期</FormLabel>
          <DatePicker
            value={basicValue}
            onChange={handleChange}
            placeholder="请选择日期"
            allowClear
            format="YYYY-MM-DD"
          />
        </FormControl>
        
        <Box sx={{ mt: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>当前值:</strong> {basicValue ? basicValue.format('YYYY-MM-DD') : '未选择'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>值类型:</strong> {basicValue ? typeof basicValue : 'undefined'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>是否为 dayjs:</strong> {basicValue ? (dayjs.isDayjs(basicValue) ? '是' : '否') : 'N/A'}
          </Typography>
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>测试步骤:</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary" component="div">
            1. 点击输入框打开日期选择器<br/>
            2. 点击任意日期<br/>
            3. 查看控制台是否有 onChange 事件<br/>
            4. 查看上方是否显示选中的日期
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default SimpleTest;
