/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { forwardRef, useEffect, useState, useRef } from "react";

import {
  Stack,
  OutlinedInput,
  Grid,
  InputAdornment,
  InputBase,
  FormHelperText,
  InputLabel,
  Typography,
} from "@mui/material";
import { pxToRem } from "@u/zkUtils";
import { update, getTreeSelect, getById } from "@/service/api/area";
import * as Yup from "yup";
import AnimateButton from "@/components/@extended/AnimateButton";
import LoadingButton from "@mui/lab/LoadingButton";
import Treeselect from "@/components/zktreeselect";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import SelectMapBaidu from "@/components/baiduMap/selectMap"; //百度地图
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useStateUserInfo } from "@/hooks/user";
const EditArea = forwardRef((props, ref) => {
  const selectMapRef = useRef(null);
  const userInfor = useStateUserInfo();
  // 国际化
  const { t } = useTranslation();
  const treeSelectRef = React.useRef(null);
  // 添加分组弹窗
  const [open, setOpen] = React.useState(false);
  //保存按钮
  const [loading, setLoading] = React.useState(false);
  //是否可以修改所属上级区域
  const [changeParent, setChangeParent] = useState(true);
  //下拉树数据
  const [treeData, setTreeData] = React.useState([]);

  const [id, setId] = useState("");

  // 编辑表单
  const editFormik = useFormik({
    initialValues: {
      id: "",
      name: "",
      lng: "",
      lat: "",
      parentId: "",
      sort: 0,
      status: "0",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handleSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string()
        .max(60, t("common.common_rule_area_len60"))
        .matches(/^[a-zA-Z\u4e00-\u9fa5]+[a-zA-Z\d\u4e00-\u9fa5_\s]*$/, {
          message: t("common.common_area_name_regTip"),
          excludeEmptyString: true,
        })
        .required(t("common.common_area_name_not_null")),
      // parentId: Yup.string().when("name", {
      //   is: () => {
      //     return changeParent;
      //   },
      //   then: () => {
      //     return Yup.string().test({
      //       name: "parentId",
      //       test: (value, ctx) => {
      //         // if (userInfor.roleCode === "admin") {
      //         //   return true;
      //         // }
      //         if (value === undefined) {
      //           return ctx.createError({
      //             message: t("area.ips_enter_region"),
      //           });
      //         }
      //         // console.log(t("ips.ips_enter_region"), value);
      //         if (value !== null || value !== "" || value !== undefined) {
      //           return true;
      //         }
      //         return ctx.createError({
      //           message: t("area.ips_enter_region"),
      //         });
      //       },
      //     });
      //   },
      //   otherwise: () => {
      //     return Yup.mixed();
      //   },
      // }),

      lng: Yup.string().required(
        t("common.common_please_area_center_location")
      ),
      lat: Yup.string().required(
        t("common.common_please_area_center_location")
      ),
      sort: Yup.number()
        .required(t("common_input_sort_null"))
        .min(0, t("common_connot_lower_zero")),
    }),
  });

  React.useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
  }));

  const handleClose = () => {
    editFormik.handleReset();
    editFormik.setFieldValue("id", "");
    editFormik.setFieldValue("lat", "");
    editFormik.setFieldValue("lng", "");
    editFormik.setFieldValue("name", "");
    editFormik.setFieldValue("parentId", "");
    setOpen(false);
  };

  const handleOpen = async (cell) => {
    editFormik.handleReset();
    setId(cell.row.original.id);

    await getArea(cell.row.original.id);
    setOpen(true);
    setChangeParent(true);
    getTreeData();
  };

  const getArea = (id) => {
    getById(id)
      .then((res) => {
        let area = res.data;
        if (!area?.parentName) {
          setChangeParent(false);
        }
        // tips:加这个判断是为了怕没有经纬度导致了，其他数据无法赋值的情况
        if (area?.location && area?.location !== null) {
          const location = area?.location.split(",");
          editFormik.setFieldValue("lng", location[0]);
          editFormik.setFieldValue("lat", location[1]);
        }
        editFormik.setFieldValue("name", area.name);
        editFormik.setFieldValue("id", area.id);

        editFormik.setFieldValue(
          "parentId",
          area.parentId ? area.parentId : ""
        );

        editFormik.setFieldValue("sort", area.sort);
        editFormik.setFieldValue("status", area.status);

        treeSelectRef.current.setItem({
          id: area?.parentId,
          name: area?.parentName,
        });
        editFormik.validateForm();
        return Promise.resolve();
      })
      .catch((err) => {
        return Promise.reject(err);
      });
  };

  const handleSubmit = async (values) => {
    //处理经纬度，使用逗号连接
    const requestValues = { ...values, id: id };
    requestValues.location = `${values.lng},${values.lat}`;
    setLoading(true);
    await update(requestValues)
      .then((res) => {
        props.callback();
        setLoading(false);
        setOpen(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  };

  const getTreeData = () => {
    getTreeSelect().then((res) => {
      setTreeData(res.data);
    });
  };

  // 点图选点回调事件
  const getLocationGoogle = (lat, lng, location) => {
    editFormik.setFieldValue("lat", lat);
    editFormik.setFieldValue("lng", lng);
  };
  const getLocation = (value, location) => {
    editFormik.setFieldValue("lng", value.lng);
    editFormik.setFieldValue("lat", value.lat);
  };
  // 切换地图
  const handleSwitchMap = () => {
    return <SelectMapBaidu ref={selectMapRef} getLocation={getLocation} />;
    // if (window.localStorage.getItem("localVersion") === "EN") {
    //   return (
    //     <SelectMapGoogle ref={selectMapRef} getLocation={getLocationGoogle} />
    //   );
    // } else {
    //   return <SelectMapBaidu ref={selectMapRef} getLocation={getLocation} />;
    // }
  };

  return (
    <>
      {open && (
        <BootstrapDialog
          open={open}
          onClose={handleClose}
          aria-describedby="alert-dialog-slide-description">
          <BootstrapDialogTitle onClose={handleClose}>
            <Typography variant="h4" component="p">
              {t("menu.button_area_update")}
            </Typography>
          </BootstrapDialogTitle>
          <BootstrapContent
            sx={{
              minWidth: "600px",

              background: "#FFFFFF 0% 0% no-repeat padding-box",
              boxShadow: "0px 2px 6px #00000029",
              borderRadius: "10px",
            }}>
            <form noValidate onSubmit={editFormik.handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <InputLabel
                      htmlFor="area-name"
                      sx={{
                        overflow: "initial",
                        width: "30%",
                        fontSize: "14px",
                      }}>
                      {t("area.area_area_name")}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>
                    <OutlinedInput
                      id="area-name"
                      type="text"
                      name="name"
                      value={editFormik.values.name}
                      onBlur={editFormik.handleBlur}
                      onChange={editFormik.handleChange}
                      placeholder={t("Enter a Region Name")}
                      fullWidth
                      error={Boolean(
                        editFormik.touched.name && editFormik.errors.name
                      )}
                      sx={{
                        width: "100%",
                        "& .MuiOutlinedInput-input": {
                          height: pxToRem(35),
                          fontSize: "14px",
                        },
                        borderRadius: "7px",
                      }}
                    />
                    {editFormik.touched.name && editFormik.errors.name && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-area-name">
                        {editFormik.errors.name}
                      </FormHelperText>
                    )}
                  </Stack>
                </Grid>
                {changeParent && (
                  <Grid item xs={12}>
                    <Stack spacing={1}>
                      <InputLabel
                        htmlFor="area-parentId"
                        sx={{
                          overflow: "initial",
                          width: "30%",
                          fontSize: "14px",
                        }}>
                        {t("area.superior_area")}
                        {/* {(userInfor.roleCode === "DevOpt" ||
                          userInfor.roleCode === "admin") && (
                          <i style={{ color: "red" }}>*</i>
                        )} */}
                      </InputLabel>
                      <Treeselect
                        ref={treeSelectRef}
                        data={treeData}
                        optionValue="id"
                        optionLabel="name"
                        isClear={false}
                        placeholder={t("area.select_an_area")}
                        onChange={(values) => {
                          if (values != null && values != undefined) {
                            editFormik.setFieldValue("parentId", values.id);
                          }
                        }}
                        onClear={() => {
                          editFormik.setFieldValue("parentId", "");
                        }}
                        sx={{
                          width: "100%",
                          fontSize: "14px",
                          "& .MuiOutlinedInput-input": {
                            height: pxToRem(35),
                          },
                          borderRadius: "7px",
                        }}
                        disableParent={true}
                      />
                      {editFormik.touched.parentId &&
                        editFormik.errors.parentId && (
                          <FormHelperText
                            error
                            id="standard-weight-helper-text-area-parentId">
                            {editFormik.errors.parentId}
                          </FormHelperText>
                        )}
                    </Stack>
                  </Grid>
                )}
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <InputLabel
                      htmlFor="area-parentId"
                      sx={{
                        overflow: "initial",
                        width: "30%",
                        fontSize: "14px",
                      }}>
                      {t("area.region_center")}
                      <i style={{ color: "red" }}>*</i>
                    </InputLabel>
                    <OutlinedInput
                      id="location"
                      type="text"
                      sx={{
                        width: "100%",
                        cursor: "notAllowed",
                        "& .MuiOutlinedInput-input": {
                          height: pxToRem(35),
                          fontSize: "14px",
                        },
                        borderRadius: "7px",
                      }}
                      name="lng"
                      readOnly
                      onClick={() => selectMapRef.current.handleClickOpen()}
                      value={editFormik.values.lng}
                      onBlur={editFormik.handleBlur}
                      onChange={editFormik.handleChange}
                      placeholder={t("common.common_Longitude")}
                      fullWidth
                      startAdornment={
                        <InputAdornment position="start">
                          <InputBase
                            readOnly
                            sx={{
                              width: "184px",
                              cursor: "notAllowed",
                            }}
                            onBlur={editFormik.handleBlur}
                            onChange={editFormik.handleChange}
                            value={editFormik.values.lat}
                            placeholder={t("common.common_latitude")}
                            endAdornment={<div>-</div>}
                            error={Boolean(
                              editFormik.touched.lat && editFormik.errors.lat
                            )}
                          />
                        </InputAdornment>
                      }
                      error={Boolean(
                        editFormik.touched.lng && editFormik.errors.lng
                      )}
                    />
                    {editFormik.touched.lng && editFormik.errors.lng && (
                      <FormHelperText error id="lng-error">
                        {editFormik.errors.lng}
                      </FormHelperText>
                    )}
                  </Stack>
                </Grid>

                {/* <Grid item xs={12}>
                  <FormGroup>
                    <FormControlLabel
                      control={<Checkbox />}
                      label={t("area.accelerate")}
                      sx={{
                        color: "#474B4F",
                        "& .MuiTypography-root": {
                          fontSize: "14px",
                        },
                      }}
                    />
                  </FormGroup>
                </Grid> */}
                {/* <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel
                    sx={{
                      overflow: "initial",
                    }}
                    htmlFor="area-sort"
                  >
                    {t("common.common_sort")} <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    sx={{
                      width: "100%",
                    }}
                    id="area-sort"
                    type="number"
                    name="sort"
                    value={editFormik.values.sort}
                    onBlur={editFormik.handleBlur}
                    onChange={editFormik.handleChange}
                    placeholder={t("common.common_input_sort")}
                    fullWidth
                    error={Boolean(
                      editFormik.touched.sort && editFormik.errors.sort
                    )}
                  />
                  {editFormik.touched.sort && editFormik.errors.sort && (
                    <FormHelperText
                      error
                      id="standard-weight-helper-text-area-sort"
                    >
                      {editFormik.errors.sort}
                    </FormHelperText>
                  )}
                </Stack>
              </Grid> */}
                {/* <Grid item xs={12}>
                <Stack spacing={4}>
                  <InputLabel
                    sx={{
                      overflow: "initial",
                      marginBottom: "5px",
                    }}
                    htmlFor="groupStatus"
                  >
                    {t("common.common_status")}{" "}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <RadioGroup
                    row
                    name="status"
                    onChange={editFormik.handleChange}
                    // defaultValue={}
                    value={editFormik.values.status}
                    style={{ marginTop: "0px", paddingLeft: "10px" }}
                  >
                    <Stack
                      direction="row"
                      justifyContent="flex-start"
                      alignItems="center"
                      spacing={5}
                    >
                      <FormControlLabel
                        sx={{
                          border: "1px solid #dcdfe6",
                          paddingRight: "10px",
                          borderRadius: "4px",
                        }}
                        value="0"
                        control={<Radio />}
                        label={t("common.common_normal")}
                      />
                      <FormControlLabel
                        sx={{
                          border: "1px solid #dcdfe6",
                          paddingRight: "10px",
                          borderRadius: "4px",
                        }}
                        value="1"
                        control={<Radio />}
                        label={t("common.common_stop")}
                      />
                    </Stack>
                  </RadioGroup>
                </Stack>
              </Grid> */}

                <Grid item xs={12}>
                  <AnimateButton>
                    <LoadingButton
                      loading={loading}
                      disableElevation
                      disabled={editFormik.isSubmitting}
                      fullWidth
                      size="large"
                      type="submit"
                      variant="contained"
                      color="primary"
                      style={{
                        width: "100%",
                        height: "48px",
                        borderRadius: "10px",
                        color: "#FFFFFF",
                        textAlign: "center",
                        font: `normal normal medium 14px/18px Proxima Nova`,
                        background:
                          "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                      }}>
                      {t("common.common_submit")}
                    </LoadingButton>
                  </AnimateButton>
                </Grid>
                {editFormik.errors.submit && (
                  <Grid item xs={12}>
                    <FormHelperText error>
                      {editFormik.errors.submit}
                    </FormHelperText>
                  </Grid>
                )}
              </Grid>
            </form>
          </BootstrapContent>
        </BootstrapDialog>
      )}

      {/* 谷歌地图  注意回显方法 */}
      {handleSwitchMap()}
    </>
  );
});
export default EditArea;
