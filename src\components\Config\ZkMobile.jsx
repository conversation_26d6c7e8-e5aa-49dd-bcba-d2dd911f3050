import React from "react";
import { useTranslation } from "react-i18next";
import cn from "react-phone-input-2/lang/cn.json";
import es from "react-phone-input-2/lang/es.json";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/material.css";
import {
  Stack,
  InputLabel,
  TextField,
  Divider,
  FormHelperText,
} from "@mui/material";
import RequirePoint from "../RequirePoint";
import { isEmpty, isEmptyString } from "@/utils/zkUtils";
import { pxToRem } from "@/utils/zkUtils.js";
import i18n from "i18next";

function ZkMobile(props) {
  const {
    formik,
    codename, // formik 对应区号的字段名
    name, // formik 对应电话号码的字段名
    label,
    labelpostion,
    handleChange,
    error,
    placeholder = i18n.t("common.common_country_code"),
    handleCountryCode = () => {},
  } = props;

  const { t } = useTranslation();

  const changeFn = (e) => {
    if (formik?.handleChange) {
      formik?.handleChange(e);
    }
    if (handleChange) {
      handleChange(e);
    }
  };

  const changeCode = (value, data) => {
    if (formik?.setFieldValue) {
      formik.setFieldValue(codename, value); // 更新区号字段
    }
    if (handleCountryCode) {
      handleCountryCode(data); // 如果父组件有额外处理
    }
  };

  return (
    <React.Fragment>
      <Stack spacing={1}>
        <Stack
          direction={labelpostion === "left" ? "row" : "column"}
          sx={{
            alignItems: labelpostion === "left" ? "flex-start" : "",
          }}
          spacing={1}>
          {label && (
            <InputLabel
              style={{
                marginTop: labelpostion === "left" ? "12px" : "",
                color: "#474b4fcc",
                fontSize: "14px",
              }}
              htmlFor={"zkInput_" + name}>
              {label} {props.required && <RequirePoint />}
            </InputLabel>
          )}

          <Stack
            sx={{
              flexGrow: 1,
              width: "100%",
            }}>
            <TextField
              {...props}
              autoComplete="off"
              fullWidth={true}
              sx={{
                "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall": {
                  fontSize: "14px",
                },
                "& .MuiInputBase-root ": {
                  lineHeight: "0.6rem",
                  height: pxToRem(46),
                },
              }}
              type={props.type}
              value={formik.values[name] || ""}
              helperText={props.helperText}
              error={error}
              name={name}
              label={""}
              placeholder={t("common.common_enter_mobile")}
              onChange={changeFn}
              InputProps={{
                startAdornment: (
                  <>
                    <PhoneInput
                      disabled={props.disabled}
                      autoFormat={true}
                      countryCodeEditable={true}
                      enableSearch={true}
                      searchPlaceholder={"Search"}
                      searchNotFound={"No Options Found"}
                      onChange={changeCode}
                      localization={"cn"}
                      inputProps={{
                        disabled: true,
                        margin: "0px 10px",
                      }}
                      value={formik.values[codename]}
                      placeholder={placeholder}
                      specialLabel={true}
                      isValid={(inputNumber, country, countries) => {
                        return formik.values[codename] ? false : true;
                      }}
                      style={{ width: "auto" }}
                      inputStyle={{
                        marginBottom: "0px",
                        lineHeight: "0.6rem",
                        border: "none",
                        width: "150px",
                      }}
                    />

                    <Divider
                      orientation="vertical"
                      flexItem
                      style={{ marginRight: "20px" }}
                    />
                  </>
                ),
              }}
            />

            {/* Error handling for the phone number */}
            {formik?.touched[name] && formik?.errors[name] && (
              <FormHelperText error id={`standard-weight-helper-text-${name}`}>
                {formik?.errors[name]}
              </FormHelperText>
            )}

            {/* Error handling for country code (only show if touched and not empty) */}
            {formik?.touched[codename] &&
              formik?.values[codename] &&
              formik?.errors[codename] && (
                <FormHelperText
                  error
                  id={`standard-weight-helper-text-${codename}`}>
                  {formik?.errors[codename]}
                </FormHelperText>
              )}
          </Stack>
        </Stack>
      </Stack>
    </React.Fragment>
  );
}

export default ZkMobile;
