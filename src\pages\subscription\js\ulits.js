import dayjs from "dayjs";
import { getTreeSelect } from "@s/api/area";
// 时间计算公共方法
export const computerTime = (num, type, startTime) => {
  return dayjs(startTime)
    .add(type === "0" ? 30 * num : 365 * num, "day")
    .format("YYYY-MM-DD");
};

export const formatDateFromString = (dateString) => {
  // 将时间字符串解析为日期对象
  const dateObj = new Date(dateString);

  // 从日期对象中获取年、月、日
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0"); // 月份从0开始，需要加1；并确保两位数格式
  const day = String(dateObj.getDate()).padStart(2, "0"); // 日需要确保两位数格式

  // 构建目标格式的日期字符串
  const formattedDate = `${year}-${month}-${day}`;

  // 返回格式化后的日期字符串
  return formattedDate;
};

export const handlerTime = (state, formik) => {



  if (state?.isFree == 0) {
    let freeTime = dayjs(formik.values["activateTime"])
      .add(14, "day")
      .format("YYYY-MM-DD");

    formik.setFieldValue("expiraTime", freeTime);
  } else {
    if (state?.type == "1") {
      let starttime = dayjs(
        computerTime(
          formik.values["count"],
          formik.values["unit"],
          formik.values["activateTime"]
        )
      )
        .add(0, "day")
        .format("YYYY-MM-DD");
      formik.setFieldValue("expiraTime", starttime);
    } else if (state?.type == "3") {
      if (dayjs(state?.currentItem?.expiraTime).isBefore(dayjs())) {
        let starttime = dayjs(
          computerTime(formik.values["count"], formik.values["unit"], dayjs())
        )
          .add(1, "day")
          .format("YYYY-MM-DD");

        formik.setFieldValue("activateTime", state?.data?.activateTime);
        formik.setFieldValue("expiraTime", starttime);
      } else {
        let starttime = dayjs(
          computerTime(
            formik.values["count"],
            formik.values["unit"],
            state?.data?.expiraTime
          )
        )
          .add(0, "day")
          .format("YYYY-MM-DD");
        formik.setFieldValue("expiraTime", starttime);
      }
    } else if (state?.type == "2") {
      formik.setFieldValue(
        "expiraTime",
        dayjs(state?.data?.expiraTime).format("YYYY-MM-DD")
      );
    } else {
      let tiemInfo = dayjs(
        computerTime(
          formik.values["count"],
          formik.values["unit"],
          formik.values["activateTime"]
        )
      )
        .add(0, "day")
        .format("YYYY-MM-DD");
      formik.setFieldValue("expiraTime", tiemInfo);
    }
  }
};

// 获取区域下拉选择树
export const getTreeList = (setTreeList) => {
  getTreeSelect().then((res) => {
    setTreeList(res?.data);
  });
};
