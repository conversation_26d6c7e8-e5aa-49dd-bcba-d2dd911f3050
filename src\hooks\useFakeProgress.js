import { useState, useEffect } from 'react';

const useFakeProgress = () => {
  const [progress, setProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [startProgress, setStartProgress] = useState(false);

  useEffect(() => {
    let progressInterval;

    const simulateProgress = () => {
      if (!isComplete && progress < 99) {
        progressInterval = setInterval(() => {
          setProgress((prevProgress) => {
            const nextProgress = prevProgress + 1;
            if (nextProgress === 100) {
              setIsComplete(true);
              clearInterval(progressInterval);
            }
            return nextProgress;
          });
        }, 1000); // 每隔1秒增加1%
      }
    };

    if (startProgress && !isComplete) {
      simulateProgress();
    }

    return () => {
      clearInterval(progressInterval);
    };
  }, [startProgress, progress, isComplete]);

  // 在进度条状态更新时，立即执行一些操作
  useEffect(() => {
    // 这里可以添加进度条状态更新后的操作
    console.log(`Progress updated to: ${progress}%`);
    // 例如，可以在这里更新 UI 中的进度条组件
  }, [progress]);

  // 返回进度、是否完成、开始进度的状态和设置进度的方法
  return { progress, isComplete, startProgress, setStartProgress, setProgress };
};

export default useFakeProgress;
