// 看板
const organizationRoute = [
  {
    path: "/branch",
    component: () => import("@p/Organization/branch/Company/index"),
    meta: {
      title: "Branch",
      i18n: "branch",
      id: "543658692738809856",
    },
  },

  {
    path: "/editor/branch",
    component: () =>
      import("@p/Organization/branch/Company/components/AddBranch"),
    meta: {
      title: "新增部门",
      i18n: "editor_branch",
      id: "543658692738809857",
    },
  },

  {
    path: "/view/branch",
    component: () =>
      import("@p/Organization/branch/Company/components/ViewBranch"),
    meta: {
      title: "预览部门",
      i18n: "view_branch",
      id: "54365865654592738809857",
    },
  },

  {
    path: "/branch/employee/list",
    component: () => import("@p/Organization/branch/Employee/index"),
    meta: {
      title: "Employee List",
      i18n: "branch_user_list",
      id: "543658692738809858",
    },
  },

  {
    path: "/add/branch/employee",
    component: () =>
      import("@p/Organization/branch/Employee/components/AddEmployee"),
    meta: {
      title: "Add Employee",
      i18n: "branch_user_add",
      id: "64396896889809856",
    },
  },

  {
    path: "/view/branch/employee",
    component: () =>
      import("@p/Organization/branch/Employee/components/ViewEmployee"),
    meta: {
      title: "View Employee",
      i18n: "branch_user_view",
      id: "64396896889809866",
    },
  },

  {
    path: "/partner",
    component: () => import("@p/Organization/partner/company/index"),
    meta: {
      title: "Partner",
      i18n: "parter_list",
      id: "368699898812158896",
    },
  },
  {
    path: "/add/partner",
    component: () =>
      import("@p/Organization/partner/company/components/AddPartner"),
    meta: {
      title: "Add Partner",
      i18n: "parter_add",
      id: "368699898812159696",
    },
  },

  {
    path: "/view/partner",
    component: () =>
      import("@p/Organization/partner/company/components/ViewPartner"),
    meta: {
      title: "View Partner",
      i18n: "parter_view",
      id: "368699898817455896",
    },
  },

  {
    path: "/partner/employee/list",
    component: () => import("@p/Organization/partner/Employee/index"),
    meta: {
      title: "Employee List",
      i18n: "parter_user_list",
      id: "4898988174999666",
    },
  },

  {
    path: "/partner/add/employee",
    component: () =>
      import("@p/Organization/partner/Employee/components/AddEmployee"),
    meta: {
      title: "Add Employee",
      i18n: "parter_user_add",
      id: "48989881774998666",
    },
  },

  {
    path: "/partner/view/employee",
    component: () =>
      import("@p/Organization/partner/Employee/components/ViewEmployee"),
    meta: {
      title: "View Employee",
      i18n: "parter_user_view",
      id: "1369989881774998666",
    },
  },

  {
    path: "/principal/list",
    component: () => import("@p/Organization/Principal/Company/index"),
    meta: {
      title: "Principal",
      i18n: "principal_list",
      id: "54544547888678765656534",
    },
  },

  {
    path: "/add/principal",
    component: () =>
      import("@p/Organization/Principal/Company/components/AddPrincipal"),
    meta: {
      title: "Add Principal",
      i18n: "principal_add",
      id: "9047888678765656532244",
    },
  },

  {
    path: "/view/principal",
    component: () =>
      import("@p/Organization/Principal/Company/components/ViewPrincipal"),
    meta: {
      title: "View Principal",
      i18n: "principal_view",
      id: "889945554788867534",
    },
  },

  {
    path: "/principal/employee/list",
    component: () => import("@p/Organization/Principal/Employee/index"),
    meta: {
      title: "Employee List",
      i18n: "principal_user_list",
      id: "21343547888678765656534",
    },
  },

  {
    path: "/principal/add/employee",
    component: () =>
      import("@p/Organization/Principal/Employee/components/AddEmployee"),
    meta: {
      title: "Add Employee",
      i18n: "principal_user_add",
      id: "545456786787678765656534",
    },
  },

  {
    path: "/principal/view/employee",
    component: () =>
      import("@p/Organization/Principal/Employee/components/ViewEmployee"),
    meta: {
      title: "View Employee",
      i18n: "principal_user_view",
      id: "5767454565656534",
    },
  },
];

export default organizationRoute;
