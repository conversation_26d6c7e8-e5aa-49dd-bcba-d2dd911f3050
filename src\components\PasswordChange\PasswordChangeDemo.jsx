import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  Grid,
  Alert,
  AlertTitle,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Chip,
} from "@mui/material";
import {
  Security,
  Person,
  Email,
  Phone,
  Check,
  Vpn<PERSON>ey,
  Shield,
  Lock,
} from "@mui/icons-material";
import { styled } from "@mui/material/styles";
import PasswordChangeDialog from "./PasswordChangeDialog";

const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  boxShadow: theme.shadows[2],
  transition: theme.transitions.create(["box-shadow", "transform"], {
    duration: theme.transitions.duration.short,
  }),
  "&:hover": {
    boxShadow: theme.shadows[4],
    transform: "translateY(-2px)",
  },
}));

const ProfileCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: theme.palette.primary.contrastText,
  "& .MuiCardContent-root": {
    padding: theme.spacing(3),
  },
}));

const PasswordChange = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [notification, setNotification] = useState({
    show: false,
    type: "",
    message: "",
  });

  // 模拟用户数据
  const userData = {
    name: "厂门测试",
    email: "<EMAIL>",
    phone: "XX XX",
    lastPasswordChange: "2024-01-15",
  };

  // 模拟验证当前密码的函数
  const validateCurrentPassword = async (password) => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 简单验证逻辑（实际应用中应该调用后端API）
    if (password === "123456") {
      return true;
    }
    return false;
  };

  // 模拟修改密码的函数
  const handlePasswordChange = async ({ currentPassword, newPassword }) => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // 模拟成功或失败
    if (Math.random() > 0.1) {
      // 90% 成功率
      setNotification({
        show: true,
        type: "success",
        message: "密码修改成功！请使用新密码重新登录。",
      });
      return true;
    } else {
      throw new Error("服务器错误，请稍后重试");
    }
  };

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  return (
    <Box sx={{ p: 4, backgroundColor: "#f5f5f5", minHeight: "100vh" }}>
      {notification.show && (
        <Alert
          severity={notification.type}
          sx={{ mb: 3, maxWidth: 600, mx: "auto" }}
          onClose={() =>
            setNotification({ show: false, type: "", message: "" })
          }>
          {notification.message}
        </Alert>
      )}

      <Grid container spacing={3} maxWidth={1200} mx="auto">
        {/* 用户信息卡片 */}
        <Grid item xs={12} md={4}>
          <ProfileCard>
            <CardContent sx={{ textAlign: "center" }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  mx: "auto",
                  mb: 2,
                  bgcolor: "rgba(255, 255, 255, 0.2)",
                  fontSize: "2rem",
                }}>
                {userData.name.charAt(0)}
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {userData.name}
              </Typography>
              <Chip
                label="管理员"
                size="small"
                sx={{
                  bgcolor: "rgba(255, 255, 255, 0.2)",
                  color: "inherit",
                  mb: 2,
                }}
              />

              <List dense>
                <ListItem>
                  <ListItemIcon sx={{ color: "inherit", minWidth: 36 }}>
                    <Email fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={userData.email}
                    primaryTypographyProps={{ variant: "body2" }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ color: "inherit", minWidth: 36 }}>
                    <Phone fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={userData.phone}
                    primaryTypographyProps={{ variant: "body2" }}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ color: "inherit", minWidth: 36 }}>
                    <Security fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={`上次修改密码: ${userData.lastPasswordChange}`}
                    primaryTypographyProps={{ variant: "body2" }}
                  />
                </ListItem>
              </List>
            </CardContent>
          </ProfileCard>
        </Grid>

        {/* 密码修改操作 */}
        <Grid item xs={12} md={8}>
          <StyledCard>
            <CardHeader
              avatar={<Security color="primary" />}
              title="账户安全设置"
              subheader="管理您的账户密码和安全选项"
              titleTypographyProps={{ variant: "h6", fontWeight: 500 }}
            />
            <CardContent>
              <Box sx={{ mb: 3 }}>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <AlertTitle>安全提示</AlertTitle>
                  为了您的账户安全，建议定期更换密码。新密码应包含大小写字母、数字和特殊字符。
                </Alert>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={<VpnKey />}
                  onClick={handleOpenDialog}
                  sx={{
                    py: 1.5,
                    px: 3,
                    borderRadius: 2,
                    textTransform: "none",
                    fontSize: "1rem",
                  }}>
                  修改密码
                </Button>
              </Box>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* 使用说明 */}
        <Grid item xs={12}>
          <StyledCard>
            <CardHeader
              title="组件使用说明"
              subheader="如何在项目中使用修改密码组件"
              titleTypographyProps={{ variant: "h6", fontWeight: 500 }}
            />
            <CardContent>
              <Typography variant="body1" paragraph>
                <strong>测试账户：</strong>当前密码为 <code>123456</code>
                ，您可以使用此密码进行测试。
              </Typography>

              <Typography variant="body1" paragraph>
                <strong>组件特点：</strong>
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <Check color="success" />
                  </ListItemIcon>
                  <ListItemText primary="两步验证流程：先验证当前密码，再设置新密码" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <Check color="success" />
                  </ListItemIcon>
                  <ListItemText primary="实时密码强度检测和安全建议" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <Check color="success" />
                  </ListItemIcon>
                  <ListItemText primary="完整的表单验证和错误处理" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <Check color="success" />
                  </ListItemIcon>
                  <ListItemText primary="Material-UI 设计风格，响应式布局" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <Check color="success" />
                  </ListItemIcon>
                  <ListItemText primary="支持自定义验证函数和回调处理" />
                </ListItem>
              </List>
            </CardContent>
          </StyledCard>
        </Grid>
      </Grid>

      {/* 修改密码对话框 */}
      <PasswordChangeDialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        validateCurrentPassword={validateCurrentPassword}
        onPasswordChange={handlePasswordChange}
        title="修改密码"
        currentPasswordLabel="请输入当前密码"
        newPasswordLabel="请输入新密码"
        confirmPasswordLabel="请确认新密码"
      />
    </Box>
  );
};

export default PasswordChange;
