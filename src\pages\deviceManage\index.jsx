import React from "react";
import LayoutList from "@/layout/components/LayoutList.jsx";
import CustomDelete from "@c/Toast/CustomDelete";
import { toast } from "react-toastify";
import { unbindDevice } from "@/service/api/device.js";
import TableList from "./components/TableList";
import AppTap from "@c/AppTap.jsx";
import { useTranslation } from "react-i18next";
const index = () => {
  const { t } = useTranslation();
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [currentId, setCurrentId] = useState("");
  const [selectedValue, setSelectedValue] = useState("SD");
  const [deleteCompleted, setDeleteCompleted] = useState(false);
  const handlerDelete = () => {
    unbindDevice(currentId).then((res) => {
      toast.success(res.message);
      setDeleteOpen(false);
      getTableData();
    });
  };

  const applicationList = () => {
    return (
      <AppTap
        isShowL3={false}
        value={selectedValue}
        setValue={setSelectedValue}></AppTap>
    );
  };

  const tableList = useCallback(() => {
    return (
      <TableList
        selectedValue={selectedValue}
        setSelectedValue={setSelectedValue}
        setDeleteOpen={setDeleteOpen}
        setCurrentId={setCurrentId}
      />
    );
  }, [selectedValue, setSelectedValue, setDeleteOpen, setCurrentId]);

  useEffect(() => {
    setSelectedValue(sessionStorage.getItem("APP_TYPE"));
  }, [selectedValue]);
  return (
    <React.Fragment>
      <LayoutList
        title={t("device.title")}
        header={applicationList()}
        content={tableList()}></LayoutList>

      <CustomDelete
        open={deleteOpen}
        setOpen={setDeleteOpen}
        handlerDetele={handlerDelete}
        title={"Delete Confirmation"}
        content={t("outlets.sure")}
        onContent={t("outlets.outlets_delete")}
        noDelete={deleteCompleted}></CustomDelete>
    </React.Fragment>
  );
};

export default index;
