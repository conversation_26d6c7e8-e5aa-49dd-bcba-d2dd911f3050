import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Collapse,
  IconButton,
  Typography,
  Tabs,
  Tab,
  TabPanel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Paper
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess
} from '@mui/icons-material';

/**
 * Schema布局引擎
 * 负责渲染各种布局组件
 */
class SchemaLayoutEngine {
  constructor() {
    this.layouts = new Map();
    this.registerDefaultLayouts();
  }

  /**
   * 注册默认布局组件
   */
  registerDefaultLayouts() {
    this.registerLayout('group', this.renderGroup.bind(this));
    this.registerLayout('grid', this.renderGrid.bind(this));
    this.registerLayout('card', this.renderCard.bind(this));
    this.registerLayout('tabs', this.renderTabs.bind(this));
    this.registerLayout('accordion', this.renderAccordion.bind(this));
    this.registerLayout('fieldset', this.renderFieldset.bind(this));
    this.registerLayout('stepper', this.renderStepper.bind(this));
  }

  /**
   * 注册布局组件
   * @param {string} type 布局类型
   * @param {Function} renderer 渲染函数
   */
  registerLayout(type, renderer) {
    this.layouts.set(type, renderer);
  }

  /**
   * 渲染布局
   * @param {Object} layoutConfig 布局配置
   * @param {Array} children 子元素
   * @param {Object} context 上下文
   * @returns {React.Element}
   */
  render(layoutConfig, children, context = {}) {
    if (!layoutConfig || !layoutConfig.type) {
      return children;
    }

    const renderer = this.layouts.get(layoutConfig.type);
    if (!renderer) {
      console.warn(`Unknown layout type: ${layoutConfig.type}`);
      return children;
    }

    return renderer(layoutConfig, children, context);
  }

  /**
   * 渲染分组布局
   */
  renderGroup(config, children, context) {
    const {
      title,
      description,
      collapsible = false,
      defaultExpanded = true,
      spacing = 2,
      divider = false,
      ...props
    } = config;

    const [expanded, setExpanded] = React.useState(defaultExpanded);

    const content = (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: spacing,
          ...props.sx
        }}
        className="schema-form-group"
        {...props}
      >
        {children}
      </Box>
    );

    if (!title && !description) {
      return content;
    }

    return (
      <Box className="schema-form-group">
        {title && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 1
            }}
          >
            <Typography variant="h6" component="h3" sx={{ flexGrow: 1 }}>
              {title}
            </Typography>
            {collapsible && (
              <IconButton
                size="small"
                onClick={() => setExpanded(!expanded)}
              >
                {expanded ? <ExpandLess /> : <ExpandMore />}
              </IconButton>
            )}
          </Box>
        )}
        
        {description && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mb: 2 }}
          >
            {description}
          </Typography>
        )}
        
        {divider && <Divider sx={{ mb: 2 }} />}
        
        {collapsible ? (
          <Collapse in={expanded}>
            {content}
          </Collapse>
        ) : (
          content
        )}
      </Box>
    );
  }

  /**
   * 渲染网格布局
   */
  renderGrid(config, children, context) {
    const {
      columns = 2,
      spacing = 2,
      title,
      description,
      ...props
    } = config;

    const gridContent = (
      <Grid container spacing={spacing} {...props}>
        {children.map((child, index) => (
          <Grid
            item
            xs={12}
            sm={12 / Math.min(columns, children.length)}
            key={index}
          >
            {child}
          </Grid>
        ))}
      </Grid>
    );

    if (title || description) {
      return (
        <Box className="schema-form-group">
          {title && (
            <Typography variant="h6" component="h3" sx={{ mb: 1 }}>
              {title}
            </Typography>
          )}
          {description && (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 2 }}
            >
              {description}
            </Typography>
          )}
          {gridContent}
        </Box>
      );
    }

    return gridContent;
  }

  /**
   * 渲染卡片布局
   */
  renderCard(config, children, context) {
    const {
      title,
      subtitle,
      description,
      collapsible = false,
      defaultExpanded = true,
      elevation = 1,
      variant = 'outlined',
      ...props
    } = config;

    const [expanded, setExpanded] = React.useState(defaultExpanded);

    const cardContent = (
      <CardContent>
        {description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {description}
          </Typography>
        )}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {children}
        </Box>
      </CardContent>
    );

    return (
      <Card
        elevation={elevation}
        variant={variant}
        className="schema-form-group"
        {...props}
      >
        {(title || subtitle) && (
          <CardHeader
            title={title}
            subheader={subtitle}
            action={
              collapsible ? (
                <IconButton
                  onClick={() => setExpanded(!expanded)}
                  aria-expanded={expanded}
                >
                  {expanded ? <ExpandLess /> : <ExpandMore />}
                </IconButton>
              ) : null
            }
          />
        )}
        
        {collapsible ? (
          <Collapse in={expanded}>
            {cardContent}
          </Collapse>
        ) : (
          cardContent
        )}
      </Card>
    );
  }

  /**
   * 渲染标签页布局
   */
  renderTabs(config, children, context) {
    const {
      tabs = [],
      orientation = 'horizontal',
      variant = 'standard',
      ...props
    } = config;

    const [activeTab, setActiveTab] = React.useState(0);

    if (!tabs.length) {
      return children;
    }

    return (
      <Box className="schema-form-group" {...props}>
        <Tabs
          value={activeTab}
          onChange={(event, newValue) => setActiveTab(newValue)}
          orientation={orientation}
          variant={variant}
        >
          {tabs.map((tab, index) => (
            <Tab
              key={tab.key || index}
              label={tab.label}
              disabled={tab.disabled}
            />
          ))}
        </Tabs>
        
        {tabs.map((tab, index) => (
          <TabPanel
            key={tab.key || index}
            value={activeTab}
            index={index}
          >
            <Box sx={{ pt: 2 }}>
              {tab.fields ? this.renderTabFields(tab.fields, context) : children[index]}
            </Box>
          </TabPanel>
        ))}
      </Box>
    );
  }

  /**
   * 渲染手风琴布局
   */
  renderAccordion(config, children, context) {
    const {
      sections = [],
      multiple = false,
      defaultExpanded = [],
      ...props
    } = config;

    const [expanded, setExpanded] = React.useState(
      multiple ? defaultExpanded : defaultExpanded[0] || 0
    );

    const handleChange = (panel) => (event, isExpanded) => {
      if (multiple) {
        setExpanded(prev => 
          isExpanded 
            ? [...prev, panel]
            : prev.filter(p => p !== panel)
        );
      } else {
        setExpanded(isExpanded ? panel : false);
      }
    };

    if (!sections.length) {
      return children;
    }

    return (
      <Box className="schema-form-group" {...props}>
        {sections.map((section, index) => (
          <Accordion
            key={section.key || index}
            expanded={
              multiple 
                ? expanded.includes(index)
                : expanded === index
            }
            onChange={handleChange(index)}
          >
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="h6">
                {section.title}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              {section.description && (
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {section.description}
                </Typography>
              )}
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {section.fields ? this.renderSectionFields(section.fields, context) : children[index]}
              </Box>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    );
  }

  /**
   * 渲染字段集布局
   */
  renderFieldset(config, children, context) {
    const {
      legend,
      description,
      ...props
    } = config;

    return (
      <Paper
        component="fieldset"
        variant="outlined"
        className="schema-form-group"
        sx={{
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 1,
          p: 2,
          m: 0
        }}
        {...props}
      >
        {legend && (
          <Typography
            component="legend"
            variant="h6"
            sx={{
              px: 1,
              mx: -1,
              backgroundColor: 'background.paper'
            }}
          >
            {legend}
          </Typography>
        )}
        
        {description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {description}
          </Typography>
        )}
        
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {children}
        </Box>
      </Paper>
    );
  }

  /**
   * 渲染步骤器布局
   */
  renderStepper(config, children, context) {
    const {
      steps = [],
      activeStep = 0,
      orientation = 'horizontal',
      ...props
    } = config;

    // 这里可以集成Material-UI的Stepper组件
    // 为了简化，这里只是基础实现
    return (
      <Box className="schema-form-group" {...props}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Step {activeStep + 1} of {steps.length}: {steps[activeStep]?.title}
        </Typography>
        
        {steps[activeStep]?.description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {steps[activeStep].description}
          </Typography>
        )}
        
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {children[activeStep]}
        </Box>
      </Box>
    );
  }

  /**
   * 渲染标签页字段
   */
  renderTabFields(fields, context) {
    // 这里需要与主渲染器集成
    return fields;
  }

  /**
   * 渲染区块字段
   */
  renderSectionFields(fields, context) {
    // 这里需要与主渲染器集成
    return fields;
  }
}

/**
 * TabPanel组件
 */
const TabPanel = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && children}
    </div>
  );
};

export default SchemaLayoutEngine;
