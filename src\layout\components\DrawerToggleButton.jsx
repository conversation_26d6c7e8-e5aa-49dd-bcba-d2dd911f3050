// src/layout/MainLayout/components/DrawerToggleButton.jsx
import { Box } from "@mui/material";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";

const DrawerToggleButton = ({ open, onClick }) => {
  return (
    <>
      {open && (
        <Box
          sx={{
            position: "fixed",
            display: "flex",
            zIndex: 99,
            alignItems: "center",
            justifyContent: "center",
            top: "70px",
            left: "260px",
            cursor: "pointer",
            background: "white",
            width: "30px",
            height: "30px",
            borderRadius: "50%",
            boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
            "&:hover": { transform: "scale(1.1)" },
            transition: "transform 0.3s ease",
          }}
          onClick={onClick}>
          <ChevronLeftIcon />
        </Box>
      )}

      {!open && (
        <Box
          sx={{
            zIndex: 99,
            position: "fixed",
            top: "90px",
            left: "64px",
            cursor: "pointer",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            background: "white",
            width: "30px",
            height: "30px",
            borderRadius: "50%",
            boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
            transition:
              "transform 0.3s cubic-bezier(.4, 0, .2, 1), opacity 0.3s cubic-bezier(.4, 0, .2, 1)",
            // zIndex: 2000,
          }}
          onClick={onClick}>
          <ChevronRightIcon />
        </Box>
      )}
    </>
  );
};

export default DrawerToggleButton;
