/**
 *  国际化插件工具类
 * <AUTHOR>
 * @date 2022-11-28 15:06
 */

const cacheLangKey = "zkBioCloudMediaLang";
import config from "@/config/config";

/**
 * 保存国际化语言的浏览器缓存中
 * @param {yu} value 国际化语言
 */
export const setStoreLang = (value) => {
  window.localStorage.setItem(cacheLangKey, value);
  window.sessionStorage.setItem(cacheLangKey, value);
};

/**
 * 获取当前浏览器缓存中的语言，如果不存在则默认返回中文
 * @returns 语言
 */
export const getStoreLang = () => {
  const lang = window.localStorage.getItem(cacheLangKey);
  if (!lang) {
    return config.i18n;
  }
  return lang;
};

/**
 * 获取当前浏览器国际化语言
 * @returns 返回当前浏览器语言
 */
export const getBrowserLang = () => {
  return navigator.language;
};

/**
 * 转换成后端需要的国际化
 */
export const converLang = (lang) => {
  switch (lang) {
    case "en":
      return "en-US";
    case "es":
      return "es-US";
    case "zh":
      return "zh-CN";
    case "pt":
      return "pt-BR";
    case "jp":
      return "ja-JP"
    default:
      return "en-US";
  }
};

export const uploadCompnatLang = ()=>{
  const lang = window.localStorage.getItem(cacheLangKey) || config.i18n;
   switch (lang) {
    case "en":
      return "EN-en";
    case "es":
      return "ES-es";
    case "zh":
      return "ZH-cn";
    case "jp":
      return "ja-JP"
    case "pt":
      return "pt-BR";
    default:
      return "EN-en";
  }
}
