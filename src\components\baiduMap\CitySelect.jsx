/* eslint-disable react/prop-types */
 
import { useEffect, useState } from "react";
import { Select, MenuItem } from "@mui/material";
import { makeStyles } from "@material-ui/core/styles";
import chinaRegions from "./ChinaRegions";
import { t } from "i18next";
// 省、市、区数据
const regionArray = chinaRegions;

const useStyles = makeStyles((theme) => ({
  select: {
    minWidth: 120,
    margin: theme.spacing(1),
  },
}));

function SearchSelect({
  label,
  value,
  options,
  onChange,
  placeholder,
  currentSelected,
}) {
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleChange = (event) => {
    onChange(event.target.value);
  };
  return (
    <>
      <Select
        displayEmpty
        className={classes.select}
        labelId={`${label}-label`}
        id={label}
        open={open}
        align="left"
        onClose={handleClose}
        onOpen={handleOpen}
        onChange={handleChange}
        value={value}
        sx={{
          width: 150,
          margin:'1px 8px !important',
         "& .MuiSelect-select": {
            padding:'8px'
          }
         }}
        renderValue={(selected) => {
          if (
            null === selected ||
            undefined === selected ||
            selected.length === 0 ||
            "0" === currentSelected
          ) {
            return <>{placeholder}</>;
          }
          return <>{selected.name}</>;
        }}
        MenuProps={{
          // getContentAnchorEl: null,
          anchorOrigin: {
            vertical: "bottom",
            horizontal: "left",
          },
          transformOrigin: {
            vertical: "top",
            horizontal: "left",
          },
          style: {
            maxHeight: 300, // 修改下拉菜单的最大高度
          },
        }}
      >
        {options.map((option) => (
          <MenuItem key={option.name} value={option}>
            {option.name}
          </MenuItem>
        ))}
      </Select>
    </>
  );
}

const CitySelect = (props) => {
  const { changeSelectAddress, search } = props;
  //下拉框展示数据
  const [province, setProvince] = useState(null);
  const [city, setCity] = useState(null);
  const [area, setArea] = useState(null);
  //是否显示区/县,直辖市和特别行政区就不显示
  const [showArea, setShowArea] = useState(true);
  //选中的省市地址
  const [provinceAddress, setProvinceAddress] = useState("");
  const [cityAddress, setCityAddress] = useState("");
  const [areaAddress, setAreaAddress] = useState("");
  //用于当上级选择的变化后，下级选中的置空
  const [citySelected, setCitySelected] = useState("0");
  const [areaSelected, setAreaSelected] = useState("0");

  const handleProvinceChange = (selectedProvince) => {
    setProvince(selectedProvince);
    setCity(null);
    setArea(null);
    if ("" != cityAddress && provinceAddress != selectedProvince.name) {
      setCitySelected("0");
      setAreaSelected("0");
    }
    setProvinceAddress(selectedProvince.name);
    search(selectedProvince.name, selectedProvince.code, selectedProvince.name);
  };

  const handleCityChange = (selectedCity) => {
    setCity(selectedCity);
    setArea(null);
    setCityAddress(selectedCity.name);
    setShowArea(true);
    setCitySelected("1");
    if ("" != areaAddress && cityAddress != selectedCity.name) {
      setAreaSelected("0");
    }
    //直辖市和特别行政区只有2级，这边就要调用地图定位
    if (
      province.name === "北京市" ||
      province.name === "天津市" ||
      province.name === "上海市" ||
      province.name === "重庆市" ||
      province.name === "香港特别行政区" ||
      province.name === "澳门特别行政区" ||
      selectedCity.name === "东莞市" ||
      selectedCity.name === "中山市" ||
      selectedCity.name === "宜兰县" ||
      selectedCity.name === "新竹县" ||
      selectedCity.name === "苗栗县" ||
      selectedCity.name === "彰化县" ||
      selectedCity.name === "南投县" ||
      selectedCity.name === "嘉义县" ||
      selectedCity.name === "云林县" ||
      selectedCity.name === "屏东县" ||
      selectedCity.name === "台东县" ||
      selectedCity.name === "花莲县" ||
      selectedCity.name === "澎湖县" ||
      selectedCity.name === "金门县" ||
      selectedCity.name === "连江县"
    ) {
      changeSelectAddress(provinceAddress + selectedCity.name);
      setShowArea(false);
    }
    search(
      provinceAddress + selectedCity.name,
      selectedCity.code,
      selectedCity.name
    );
  };

  const handleAreaChange = (selectedArea) => {
    setArea(selectedArea);
    setAreaSelected("1");
    setAreaAddress(selectedArea.name);
    changeSelectAddress(provinceAddress + cityAddress + selectedArea.name);
    //调用地图定位
    // console.log(selectedArea.code);
    search(
      provinceAddress + cityAddress + selectedArea.name,
      selectedArea.code,
      selectedArea.name
    );
  };

  return (
    <>
      <SearchSelect
        label="province"
        value={province ? province : ""}
        options={regionArray}
        onChange={handleProvinceChange}
        placeholder={t("ips.ips_select_province")}
      />
      {province && (
        <SearchSelect
          label="city"
          value={city ? city : ""}
          options={province.children}
          onChange={handleCityChange}
          placeholder={t("ips.ips_store_tips_select_city")}
          currentSelected={citySelected}
        />
      )}
      {city && showArea && (
        <SearchSelect
          label="area"
          value={area ? area : ""}
          options={city.children}
          onChange={handleAreaChange}
          placeholder={t("ips.ips_select_region_county")}
          currentSelected={areaSelected}
        />
      )}
    </>
  );
};
export default CitySelect;
