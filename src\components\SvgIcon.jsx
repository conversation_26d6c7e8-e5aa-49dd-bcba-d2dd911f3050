import { Icon } from "@iconify/react";

const defaultLocalIcon = "no-icon";
const { VITE_ICON_LOCAL_PREFIX: prefix } = import.meta.env;
const symbolId = (localIcon = defaultLocalIcon) => {
  const iconName = localIcon || defaultLocalIcon;
  return `#${prefix}-${iconName}`;
};
const SvgIcon = ({ icon, localIcon, ...props }) => {
  /** If localIcon is passed, render localIcon first */
  return localIcon || !icon ? (
    <svg height="1em" width="1em" {...props} aria-hidden="true">
      <use fill="currentColor" href={symbolId(localIcon)} />
    </svg>
  ) : (
    <Icon icon={icon} {...props} />
  );
};

export default SvgIcon;
