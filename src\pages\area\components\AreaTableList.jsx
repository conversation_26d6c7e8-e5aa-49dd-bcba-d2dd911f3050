/* eslint-disable react-hooks/exhaustive-deps */

import React, { useState } from "react";
import MaterialReactTable from "material-react-table";
import { treeList, del } from "@/service/api/area";
import DeleteIcon from "@/assets/Icons/DeteleIcon.svg?react";
import EditorIcon from "@/assets/Icons/EditorIcon.svg?react";
import { pxToRem } from "@u/zkUtils";
import LayoutList from "@l/components/LayoutList";
import AddArea from "./AddArea";
import RefreshIcon from "@/assets/Icons/RefreshIcon.svg?react";
import AddIcon from "@/assets/Icons/Plus icon.svg?react";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";
import { tableI18n } from "@/utils/tableLang";
import EditArea from "./EditArea";
import { useFormik } from "formik";
import AuthButton from "@/components/AuthButton";

const AreaTableList = () => {
  const { t } = useTranslation();
  const confirm = useConfirm();
  const addAreaRef = React.useRef(null);
  const editAreaRef = React.useRef(null);
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);

  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  // 查询参数
  const requestParams = useRef(null);

  // 构建参数
  const buildParams = () => {
    const params = {
      ...requestParams.current,
    };

    return params;
  };

  //删除区域
  const handleDel = (values) => {
    const id = [values.original.id];
    confirm({
      title: t("area.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("area.area_del", { name: values.original.name }),
    }).then(() => {
      del(id)
        .then((res) => {
          getTableData();
        })
        .catch((err) => {});
    });
  };

  // 获取数据
  const getTableData = () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    treeList(buildParams())
      .then((res) => {
        // 设置数据
        if (res.data == null) {
          setData([]);
        } else {
          setData(res.data);
        }
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    getTableData();
  }, []);

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("area.area_area_name"),
      },
    ],
    []
  );

  const [serchName, setSeachName] = useState(null);
  const handlerSeacher = () => {
    let params = {
      pageIndex: 1,
      pageSize: 5,
      name: serchName,
    };

    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    treeList(params)
      .then((res) => {
        // 设置数据
        if (res.data == null) {
          setData([]);
        } else {
          setData(res.data);
        }
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };

  const handleKeyDown = (event) => {
    if (event.key == "Enter") {
      handlerSeacher(); // 触发搜索操作
    }
  };
  const handleClear = () => {
    setSeachName(""); // 清除搜索名称
    getTableData();
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("area.title")}
        isSearch={true}
        onClick={handlerSeacher}
        serchName={serchName}
        setSeachName={setSeachName}
        handleKeyDown={handleKeyDown}
        onClear={handleClear} // 添加 onClear 属性
        content={
          <MaterialReactTable
            renderToolbarInternalActions={({ table }) => <></>}
            muiTablePaperProps={{
              elevation: 0,
              sx: {
                borderRadius: "5px",
                border: "1px solid #f0f0f0",
                "& .MuiTableCell-body": {
                  justifyContent: "center",
                  alignItems: "center",
                  textAlign: "center",
                },
              },
            }}
            muiTableHeadRowProps={{
              sx: {
                backgroundColor: "#F0F0F0",
                boxShadow: "none",
                height: "34px",

                "& .Mui-TableHeadCell-Content": {
                  justifyContent: "center",
                  alignItems: "center",
                  lineHeight: "34px",
                  fontSize: pxToRem(16),
                },
              },
            }}
            //将筛选应用于所有子行
            filterFromLeafRows
            //启用树形拓展
            enableExpanding
            // 禁用列操作菜单
            enableColumnActions={false}
            // 禁用列排序
            enableSorting={false}
            // 禁用列隐藏
            enableHiding={false}
            // 禁用列拖拽
            enableColumnDragging={false}
            // table 状态
            state={{
              // 加载状态
              isLoading,
              // 分页参数
              // pagination,
              // 重新拉取
              showProgressBars: isRefetching,
              showAlertBanner: isError,
            }}
            // 解决列太多宽度太长问题
            enableColumnResizing
            // 初始化状态
            initialState={{ expanded: true }}
            muiToolbarAlertBannerProps={
              isError
                ? {
                    color: "error",
                    children: t("common_loading_error"),
                  }
                : undefined
            }
            // 列数
            rowCount={rowCount}
            // 固定头部
            enableStickyHeader
            // 处理表格高度
            muiTableContainerProps={{
              sx: {
                maxHeight: "550px",
                background: "#F0F0F0 0% 0% no-repeat padding-box;",
              },
            }}
            // 设置背景颜色
            muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
            muiTableProps={{ sx: { backgroundColor: "white" } }}
            muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
            muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
            // 分页回调函数
            // onPaginationChange={setPagination}
            // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
            manualFiltering
            // manualPagination
            // manualSorting

            // 开启分页
            enablePagination={false}
            // 列定义
            columns={columns}
            // 数据
            data={data}
            // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
            localization={tableI18n}
            muiLinearProgressProps={({ isTopToolbar }) => ({
              sx: { display: isTopToolbar ? "block" : "none" },
            })}
            // 多选底部提示
            // positionToolbarAlertBanner="none"
            // 开启action操作
            enableRowActions
            // action操作位置
            positionActionsColumn="last"
            displayColumnDefOptions={{
              "mrt-row-actions": {
                header: t("common.common_relatedOp"), //change header text
                size: 150, //make actions column wider
              },
            }}
            renderTopToolbarCustomActions={({ table }) => {
              return (
                <Grid
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    width: "100%",
                  }}>
                  <AuthButton button="">
                    <Button
                      onClick={() => {
                        addAreaRef.current.handleOpen();
                      }}>
                      <AddIcon></AddIcon>
                    </Button>
                  </AuthButton>
                  <Button onClick={() => getTableData()}>
                    <RefreshIcon></RefreshIcon>
                  </Button>
                </Grid>
              );
            }}
            renderRowActions={(cell, row, table) => {
              return (
                <Stack
                  direction="row"
                  sx={{
                    justifyContent: "center",
                    height: "40px",
                  }}>
                  <>
                    <Button
                      onClick={() => {
                        editAreaRef.current.handleOpen(cell);
                      }}>
                      <EditorIcon></EditorIcon>
                    </Button>
                    {(cell.row.original.parentId == null ||
                      cell.row.original.parentId) !== 1 && (
                      <AuthButton button="">
                        <Button
                          onClick={() => {
                            handleDel(cell.row);
                          }}
                          style={{
                            background: `transparent url('${DeleteIcon}') 50% 50% no-repeat padding-box`,
                          }}>
                          <DeleteIcon></DeleteIcon>
                        </Button>
                      </AuthButton>
                    )}
                  </>
                </Stack>
              );
            }}
          />
        }></LayoutList>
      <AddArea ref={addAreaRef} callback={getTableData} />
      <EditArea ref={editAreaRef} callback={getTableData} />
    </React.Fragment>
  );
};

export default AreaTableList;
