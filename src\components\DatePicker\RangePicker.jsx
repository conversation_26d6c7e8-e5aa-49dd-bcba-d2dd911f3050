import React, { useState, useCallback, useRef } from "react";
import {
  TextField,
  Popover,
  Box,
  InputAdornment,
  IconButton,
  FormHelperText,
  Button,
  Stack,
  useTheme,
  alpha,
} from "@mui/material";
import {
  LocalizationProvider,
  DateCalendar,
  PickersDay,
} from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import {
  CalendarToday as CalendarIcon,
  Clear as ClearIcon,
} from "@mui/icons-material";
import { styled } from '@mui/material/styles';
import dayjs from 'dayjs';

// Material-UI 风格的样式组件
const StyledPopover = styled(Popover)(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: theme.spacing(1),
    boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
    border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
    overflow: 'visible',
    minWidth: 'auto',
    background: theme.palette.background.paper,
    pointerEvents: 'auto',
    zIndex: theme.zIndex.modal,
  },
  '& .MuiPopover-paper': {
    pointerEvents: 'auto',
    overflow: 'visible',
  },
}));

const StyledDateCalendar = styled(DateCalendar)(({ theme }) => ({
  width: 280,
  height: 'auto',
  margin: 0,
  padding: theme.spacing(1),
  pointerEvents: 'auto',
  '& *': {
    pointerEvents: 'auto',
  },
  '& .MuiPickersCalendarHeader-root': {
    paddingLeft: theme.spacing(1),
    paddingRight: theme.spacing(1),
    marginTop: 0,
    marginBottom: theme.spacing(1),
  },
  '& .MuiPickersCalendarHeader-label': {
    fontSize: '0.875rem',
    fontWeight: 500,
  },
  '& .MuiDayCalendar-header': {
    paddingLeft: theme.spacing(1),
    paddingRight: theme.spacing(1),
  },
  '& .MuiDayCalendar-weekDayLabel': {
    fontSize: '0.75rem',
    fontWeight: 400,
    color: theme.palette.text.secondary,
    width: 32,
    height: 32,
    margin: 0,
  },
  '& .MuiDayCalendar-slideTransition': {
    minHeight: 240,
  },
  '& .MuiDayCalendar-monthContainer': {
    position: 'relative',
  },
  '& .MuiDayCalendar-weekContainer': {
    margin: 0,
    justifyContent: 'space-around',
  },
  '& .MuiPickersDay-root': {
    fontSize: '0.875rem',
    width: 32,
    height: 32,
    margin: '2px',
    borderRadius: theme.spacing(0.5),
    cursor: 'pointer !important',
    position: 'relative',
    zIndex: 1,
    pointerEvents: 'auto',
    '&:hover': {
      backgroundColor: `${alpha(theme.palette.primary.main, 0.08)} !important`,
    },
    '&.Mui-selected': {
      backgroundColor: `${theme.palette.primary.main} !important`,
      color: `${theme.palette.primary.contrastText} !important`,
      '&:hover': {
        backgroundColor: `${theme.palette.primary.dark} !important`,
      },
    },
    '&.MuiPickersDay-today': {
      border: `1px solid ${theme.palette.primary.main}`,
      backgroundColor: 'transparent',
      '&:not(.Mui-selected)': {
        color: theme.palette.primary.main,
      },
    },
    '&.Mui-disabled': {
      color: theme.palette.text.disabled,
      cursor: 'not-allowed',
      pointerEvents: 'none',
    },
    '&.range-start': {
      backgroundColor: `${theme.palette.primary.main} !important`,
      color: `${theme.palette.primary.contrastText} !important`,
      borderRadius: `${theme.spacing(0.5)} 0 0 ${theme.spacing(0.5)}`,
      '&:hover': {
        backgroundColor: `${theme.palette.primary.dark} !important`,
      },
    },
    '&.range-end': {
      backgroundColor: `${theme.palette.primary.main} !important`,
      color: `${theme.palette.primary.contrastText} !important`,
      borderRadius: `0 ${theme.spacing(0.5)} ${theme.spacing(0.5)} 0`,
      '&:hover': {
        backgroundColor: `${theme.palette.primary.dark} !important`,
      },
    },
    '&.range-middle': {
      backgroundColor: `${alpha(theme.palette.primary.main, 0.2)} !important`,
      borderRadius: 0,
      '&:hover': {
        backgroundColor: `${alpha(theme.palette.primary.main, 0.3)} !important`,
      },
    },
    '&.range-start.range-end': {
      borderRadius: theme.spacing(0.5),
    },
  },
  // 年份和月份选择器样式
  '& .MuiYearCalendar-root': {
    width: '100%',
    maxHeight: 300,
    overflow: 'auto',
  },
  '& .MuiPickersYear-root': {
    fontSize: '0.875rem',
    '&.Mui-selected': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
    },
  },
  '& .MuiMonthCalendar-root': {
    width: '100%',
  },
  '& .MuiPickersMonth-root': {
    fontSize: '0.875rem',
    '&.Mui-selected': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
    },
  },
}));

const PresetButton = styled(Button)(({ theme }) => ({
  justifyContent: 'flex-start',
  textTransform: 'none',
  fontSize: '0.875rem',
  padding: theme.spacing(0.5, 1),
  minHeight: 32,
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.04),
  },
}));

/**
 * RangePicker 组件 - 兼容 Ant Design RangePicker API
 */
const RangePicker = ({
  // 基础属性
  value,
  defaultValue,
  onChange,
  onCalendarChange,
  onOk,
  onOpenChange,
  onFocus,
  onBlur,

  // 显示相关
  placeholder = ["开始日期", "结束日期"],
  size = "middle",
  variant = "outlined",
  disabled = false,
  allowClear = true,
  allowEmpty = [false, false],

  // 格式化
  format = "YYYY-MM-DD",

  // 下拉框相关
  open,
  defaultOpen = false,
  popupClassName,
  popupStyle,
  placement = "bottomLeft",

  // 时间选择
  showTime = false,

  // 日期限制
  disabledDate,
  disabledTime,
  minDate,
  maxDate,

  // 选择器类型
  picker = "date",

  // 面板相关
  renderExtraFooter,
  cellRender,
  panelRender,

  // 预设
  presets = [],

  // 分隔符
  separator = "~",

  // 图标
  suffixIcon,

  // 状态
  status,

  // 其他
  className,
  style,
  needConfirm = false,

  ...restProps
}) => {
  // 状态管理
  const [internalOpen, setInternalOpen] = useState(defaultOpen);
  const [internalValue, setInternalValue] = useState(defaultValue || [null, null]);
  const [anchorEl, setAnchorEl] = useState(null);
  const [tempValue, setTempValue] = useState([null, null]);
  const [leftCalendarDate, setLeftCalendarDate] = useState(dayjs());
  const [rightCalendarDate, setRightCalendarDate] = useState(dayjs().add(1, 'month'));


  const inputRef = useRef(null);
  const theme = useTheme();

  // 受控状态处理
  const isControlledOpen = open !== undefined;
  const isControlledValue = value !== undefined;

  const currentOpen = isControlledOpen ? open : internalOpen;
  const currentValue = isControlledValue ? value : internalValue;

  // 处理打开状态变化
  const handleOpenChange = useCallback((newOpen) => {
    if (!isControlledOpen) {
      setInternalOpen(newOpen);
    }

    if (onOpenChange) {
      onOpenChange(newOpen);
    }
  }, [isControlledOpen, onOpenChange]);

  // 处理值变化
  const handleValueChange = useCallback((newValue, triggerEvent = 'change') => {
    const formattedValue = newValue ? [
      newValue[0] ? dayjs(newValue[0]) : null,
      newValue[1] ? dayjs(newValue[1]) : null,
    ] : [null, null];

    const dateStrings = [
      formattedValue[0] ? formattedValue[0].format(format) : '',
      formattedValue[1] ? formattedValue[1].format(format) : '',
    ];

    if (!isControlledValue) {
      setInternalValue(formattedValue);
    }

    if (onChange) {
      onChange(formattedValue, dateStrings);
    }

    // 只有在以下情况才关闭面板：
    // 1. 需要确认且用户点击了确认按钮
    // 2. 不需要确认且两个日期都选择了且是完整的范围选择
    if (triggerEvent === 'ok' ||
        (!needConfirm && !showTime && formattedValue[0] && formattedValue[1] && triggerEvent === 'complete')) {
      handleOpenChange(false);
    }
  }, [isControlledValue, onChange, format, needConfirm, showTime, handleOpenChange]);

  // 处理日历变化（单个日期选择时触发）
  const handleCalendarChange = useCallback((newValue) => {
    if (onCalendarChange) {
      const formattedValue = newValue ? [
        newValue[0] ? dayjs(newValue[0]) : null,
        newValue[1] ? dayjs(newValue[1]) : null,
      ] : [null, null];

      const dateStrings = [
        formattedValue[0] ? formattedValue[0].format(format) : '',
        formattedValue[1] ? formattedValue[1].format(format) : '',
      ];

      // 确定是开始还是结束日期的变化
      const range = !formattedValue[0] || (formattedValue[0] && !formattedValue[1]) ? 'start' : 'end';

      onCalendarChange(formattedValue, dateStrings, { range });
    }
  }, [onCalendarChange, format]);

  // 处理确认
  const handleOk = useCallback(() => {
    if (tempValue[0] !== null || tempValue[1] !== null) {
      handleValueChange(tempValue, 'ok');
      setTempValue([null, null]);
    }
    handleOpenChange(false);

    if (onOk) {
      onOk();
    }
  }, [tempValue, handleValueChange, handleOpenChange, onOk]);

  // 处理取消
  const handleCancel = useCallback(() => {
    setTempValue([null, null]);
    handleOpenChange(false);
  }, [handleOpenChange]);

  // 处理清除
  const handleClear = useCallback((e) => {
    e.stopPropagation();
    handleValueChange([null, null], 'clear');
  }, [handleValueChange]);

  // 处理输入框点击
  const handleInputClick = useCallback((e) => {
    if (!disabled) {
      setAnchorEl(e.currentTarget);
      handleOpenChange(true);
    }
  }, [disabled, handleOpenChange]);

  // 获取显示文本
  const getDisplayText = useCallback(() => {
    if (!currentValue || (!currentValue[0] && !currentValue[1])) return "";

    const startText = currentValue[0] ? dayjs(currentValue[0]).format(format) : '';
    const endText = currentValue[1] ? dayjs(currentValue[1]).format(format) : '';

    if (!startText && !endText) return '';
    if (!startText) return endText;
    if (!endText) return startText;

    return `${startText} ${separator} ${endText}`;
  }, [currentValue, format, separator]);

  // 获取输入框尺寸
  const getInputSize = () => {
    switch (size) {
      case 'large': return 'medium';
      case 'small': return 'small';
      default: return 'medium';
    }
  };

  // 判断日期是否禁用
  const isDateDisabled = useCallback((date) => {
    if (disabledDate) {
      return disabledDate(dayjs(date));
    }

    if (minDate && dayjs(date).isBefore(dayjs(minDate), 'day')) {
      return true;
    }

    if (maxDate && dayjs(date).isAfter(dayjs(maxDate), 'day')) {
      return true;
    }

    return false;
  }, [disabledDate, minDate, maxDate]);

  // 渲染预设按钮
  const renderPresets = () => {
    if (!presets || presets.length === 0) return null;

    return (
      <Box sx={{ p: 1, borderRight: `1px solid ${theme.palette.divider}`, minWidth: 120 }}>
        <Stack spacing={0.5}>
          {presets.map((preset, index) => (
            <PresetButton
              key={index}
              variant="text"
              size="small"
              fullWidth
              onClick={() => {
                const presetValue = typeof preset.value === 'function' ? preset.value() : preset.value;
                handleValueChange(presetValue, 'preset');
              }}
            >
              {preset.label}
            </PresetButton>
          ))}
        </Stack>
      </Box>
    );
  };

  // 处理日期点击
  const handleDateClick = useCallback((newDate) => {
    const [start, end] = currentValue || [null, null];
    let newValue;
    let isComplete = false;

    if (!start || (start && end)) {
      // 开始新的选择
      newValue = [newDate, null];
      console.log('选择开始日期:', newDate.format('YYYY-MM-DD'));

      // 更新右侧日历显示下一个月
      setRightCalendarDate(newDate.add(1, 'month'));
    } else if (start && !end) {
      // 选择结束日期
      if (newDate.isBefore(start)) {
        newValue = [newDate, start];
      } else {
        newValue = [start, newDate];
      }
      isComplete = true; // 标记为完整选择
      console.log('选择结束日期:', newDate.format('YYYY-MM-DD'), '范围完整');
    }

    handleCalendarChange(newValue);
    if (needConfirm) {
      setTempValue(newValue);
    } else {
      // 根据是否完整选择来决定触发事件类型
      const triggerEvent = isComplete ? 'complete' : 'select';
      handleValueChange(newValue, triggerEvent);
    }
  }, [currentValue, needConfirm, handleCalendarChange, handleValueChange]);



  // 处理左侧日历的 onChange（简化版本，类似基础日期选择器）
  const handleLeftCalendarChange = useCallback((newDate) => {
    if (newDate && dayjs(newDate).isValid()) {
      console.log('Left calendar onChange:', newDate.format('YYYY-MM-DD'));

      // 检查是否是同一天的点击（日期选择）
      const isSameDay = leftCalendarDate.isSame(newDate, 'day');
      const isSameMonth = leftCalendarDate.isSame(newDate, 'month');
      const isSameYear = leftCalendarDate.isSame(newDate, 'year');

      if (isSameMonth && isSameYear && !isSameDay) {
        // 在同一个月内选择不同日期 = 日期选择
        console.log('Left calendar: date selection');
        handleDateClick(newDate);
      } else {
        // 年份或月份变化 = 视图导航
        console.log('Left calendar: view navigation to:', newDate.format('YYYY-MM'));
        setLeftCalendarDate(newDate);
      }
    }
  }, [handleDateClick, leftCalendarDate]);

  // 处理右侧日历的 onChange（简化版本，类似基础日期选择器）
  const handleRightCalendarChange = useCallback((newDate) => {
    if (newDate && dayjs(newDate).isValid()) {
      console.log('Right calendar onChange:', newDate.format('YYYY-MM-DD'));

      // 检查是否是同一天的点击（日期选择）
      const isSameDay = rightCalendarDate.isSame(newDate, 'day');
      const isSameMonth = rightCalendarDate.isSame(newDate, 'month');
      const isSameYear = rightCalendarDate.isSame(newDate, 'year');

      if (isSameMonth && isSameYear && !isSameDay) {
        // 在同一个月内选择不同日期 = 日期选择
        console.log('Right calendar: date selection');
        handleDateClick(newDate);
      } else {
        // 年份或月份变化 = 视图导航
        console.log('Right calendar: view navigation to:', newDate.format('YYYY-MM'));
        setRightCalendarDate(newDate);
      }
    }
  }, [handleDateClick, rightCalendarDate]);

  // 自定义日期渲染，支持范围选择样式
  const renderDay = useCallback((day, _selectedDays, pickersDayProps) => {
    const dayValue = dayjs(day);
    const [start, end] = currentValue || [null, null];

    let className = '';
    let isSelected = false;

    if (start && end) {
      if (dayValue.isSame(start, 'day')) {
        className = 'range-start';
        isSelected = true;
      } else if (dayValue.isSame(end, 'day')) {
        className = 'range-end';
        isSelected = true;
      } else if (dayValue.isAfter(start, 'day') && dayValue.isBefore(end, 'day')) {
        className = 'range-middle';
      }
    } else if (start && dayValue.isSame(start, 'day')) {
      isSelected = true;
    }

    return (
      <PickersDay
        {...pickersDayProps}
        selected={isSelected}
        className={className}
      />
    );
  }, [currentValue, handleDateClick]);



  // 渲染日期范围面板
  const renderDateRangePanel = () => {
    const dateValue = needConfirm ? tempValue : currentValue;
    const startDate = dateValue?.[0];
    const endDate = dateValue?.[1];

    return (
      <Box sx={{ display: 'flex' }}>
        {renderPresets()}
        <Box sx={{ flex: 1 }}>
          <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="zh-cn">
            <Box sx={{ display: 'flex', gap: 1 }}>
              {/* 开始日期日历 */}
              <Box>
                <Box sx={{
                  p: 1,
                  textAlign: 'center',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  borderBottom: `1px solid ${theme.palette.divider}`,
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                }}>
                  开始日期
                </Box>
                <StyledDateCalendar
                  key={`left-${leftCalendarDate.format('YYYY-MM')}`}
                  value={leftCalendarDate}
                  onChange={handleLeftCalendarChange}
                  shouldDisableDate={isDateDisabled}
                  displayWeekNumber={false}
                  fixedWeekNumber={6}
                  views={['year', 'month', 'day']}
                  openTo="day"
                  slots={{
                    day: (props) => renderDay(props.day, [startDate, endDate], props),
                  }}
                />
              </Box>

              {/* 结束日期日历 */}
              <Box sx={{ borderLeft: `1px solid ${theme.palette.divider}` }}>
                <Box sx={{
                  p: 1,
                  textAlign: 'center',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  borderBottom: `1px solid ${theme.palette.divider}`,
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                }}>
                  结束日期
                </Box>
                <StyledDateCalendar
                  key={`right-${rightCalendarDate.format('YYYY-MM')}`}
                  value={rightCalendarDate}
                  onChange={handleRightCalendarChange}
                  shouldDisableDate={isDateDisabled}
                  displayWeekNumber={false}
                  fixedWeekNumber={6}
                  views={['year', 'month', 'day']}
                  openTo="day"
                  slots={{
                    day: (props) => renderDay(props.day, [startDate, endDate], props),
                  }}
                />
              </Box>
            </Box>
          </LocalizationProvider>

          {/* 额外的页脚 */}
          {renderExtraFooter && (
            <Box sx={{ p: 1, borderTop: `1px solid ${theme.palette.divider}` }}>
              {renderExtraFooter()}
            </Box>
          )}

          {/* 确认按钮 */}
          {(needConfirm || showTime) && (
            <Box sx={{ p: 1, borderTop: `1px solid ${theme.palette.divider}`, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button size="small" onClick={handleCancel}>
                取消
              </Button>
              <Button size="small" variant="contained" onClick={handleOk}>
                确定
              </Button>
            </Box>
          )}
        </Box>
      </Box>
    );
  };

  return (
    <Box className={className} style={style}>
      {/* 输入框 */}
      <TextField
        ref={inputRef}
        value={getDisplayText()}
        placeholder={Array.isArray(placeholder) ? placeholder.join(` ${separator} `) : placeholder}
        size={getInputSize()}
        variant={variant}
        disabled={disabled}
        error={status === 'error'}
        fullWidth
        onClick={handleInputClick}
        onFocus={(e) => onFocus && onFocus(e, { range: 'start' })}
        onBlur={(e) => onBlur && onBlur(e, { range: 'start' })}
        InputProps={{
          readOnly: true,
          style: { cursor: disabled ? 'default' : 'pointer' },
          endAdornment: (
            <InputAdornment position="end">
              {allowClear && (currentValue[0] || currentValue[1]) && !disabled && (
                <IconButton
                  size="small"
                  onClick={handleClear}
                  sx={{ mr: 0.5 }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              )}
              {suffixIcon || <CalendarIcon />}
            </InputAdornment>
          ),
        }}
        {...restProps}
      />

      {/* 下拉日历面板 */}
      <StyledPopover
        open={currentOpen}
        anchorEl={anchorEl}
        onClose={() => {
          setAnchorEl(null);
          handleOpenChange(false);
        }}
        anchorOrigin={{
          vertical: placement.includes('top') ? 'top' : 'bottom',
          horizontal: placement.includes('Right') ? 'right' : 'left',
        }}
        transformOrigin={{
          vertical: placement.includes('top') ? 'bottom' : 'top',
          horizontal: placement.includes('Right') ? 'right' : 'left',
        }}
        className={popupClassName}
        slotProps={{
          paper: {
            style: popupStyle,
          },
        }}
      >
        {panelRender ? panelRender(renderDateRangePanel()) : renderDateRangePanel()}
      </StyledPopover>

      {/* 状态提示 */}
      {status === 'warning' && (
        <FormHelperText sx={{ color: 'warning.main' }}>
          警告状态
        </FormHelperText>
      )}
    </Box>
  );
};

export default RangePicker;
