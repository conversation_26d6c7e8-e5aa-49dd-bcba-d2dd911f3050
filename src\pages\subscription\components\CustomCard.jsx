import React from "react";

function CustomCard(props) {
  const { title, children } = props;
  return (
    <Grid
      container
      sx={{
        background: ` #FFFFFF 0% 0% no-repeat padding-box`,
        boxShadow: `0px 2px 6px #0000001A`,
        borderRadius: "15px",
      }}>
      <Grid
        item
        xs={12}
        bgcolor={"#F4F4F7"}
        height={"50px"}
        borderRadius={"15px 15px 0 0"}>
        <Typography fontSize={16} ml={4} pt={2} variant="h3">
          {title}
        </Typography>
      </Grid>

      <Grid item xs={12} pl={10} mr={4} mt={2} pb={2} pr={6}>
        {children}
      </Grid>
    </Grid>
  );
}

export default CustomCard;
