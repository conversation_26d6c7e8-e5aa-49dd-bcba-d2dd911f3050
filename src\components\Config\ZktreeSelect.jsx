/* eslint-disable react/prop-types */
import { <PERSON><PERSON><PERSON>, <PERSON>View } from "@mui/lab";
import React, { useState, forwardRef, useEffect } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import DeleteIcon from "@mui/icons-material/Close";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { Box, IconButton, Popover, TextField, Typography } from "@mui/material";
import styled from "@emotion/styled";
import Measure from "react-measure";
import RequirePoint from "../RequirePoint";
import { pxToRem } from "@/utils/zkUtils.js";
import PropTypes from "prop-types";
import ExpandMoreOutlinedIcon from "@mui/icons-material/ExpandMoreOutlined";
import i18n from "i18next";
const StyledPopover = styled(Popover)((props) => ({
  "& .MuiPaper-root": {
    width: "100%",
    //@ts-ignore
    maxWidth: pxToRem(props?.__width),
    maxHeight: "18rem",
    boxShadow:
      " 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",
    "&::-webkit-scrollbar": {
      width: "0.4em",
    },
    "&::-webkit-scrollbar-track": {
      backgroundColor: "#f5f5f5",
      borderRadius: "5px",
      // boxShadow: 'inset 0 0 6px rgba(0,0,0,0.00)',
      webkitBoxShadow: "inset 0 0 3px rgba(0,0,0,0.1)",
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: "rgba(0, 0, 0, 0.2)",
      borderRadius: "5px",
      // outline: '1px solid slategrey'
    },
    "&::-webkit-scrollbar-button": {
      backgroundColor: "#eee",
      display: "none",
    },
    "&::-webkit-scrollbar-corner": {
      backgroundColor: "black",
    },
    // overflowY: 'scroll',
    // overflow: 'hidden'
  },
}));

const StyledTreeView = styled(TreeView)(() => ({
  "& .MuiTreeItem-iconContainer > svg": {
    fontSize: 22,
  },
}));

const ZKTreeSelect = forwardRef((props, ref) => {
  const {
    error,
    disableParent = true,
    data = [],
    label,
    placeholder = "请选择",
    isClear = true,
    onChange = () => {},
    onClear = () => {},
    dir = "ltr",
    optionValue = "id",
    emptyLabel = i18n.t("common.common_no_data"),
    defaultValue = undefined,
    size = "Normal",
    labelpostion = "left",
    formik,
    name,
    disabled = false,
  } = props;
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const [equipmentItem, setEquipmentItem] = useState(
    defaultValue ? defaultValue.name : ""
  );
  const [equipmentId, setEquipmentId] = useState(
    defaultValue ? defaultValue.id?.toString() : ""
  );
  const [expanded, setExpanded] = useState([]);
  const findItemById = (list, targetId) => {
    for (const item of list) {
      if (item.id == targetId) {
        return item; // 找到匹配项，返回
      }

      // 如果有子数组，递归查找
      if (item.children && Array.isArray(item.children)) {
        const found = findItemById(item.children, targetId);
        if (found) return found; // 如果在子数组中找到，则返回
      }
    }
    return null; // 未找到，返回 null
  };

  useEffect(() => {
    const findData = findItemById(data || [], formik.values[name]);

    formik?.setFieldValue(name, findData?.id);

    setEquipmentItem(findData?.name);
  }, [data, formik.values[name]]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const clear = () => {
    setEquipmentId(undefined);
    setEquipmentItem("");
  };

  const setItem = (item) => {
    setEquipmentId(item.id);
    setEquipmentItem(item.name);
  };

  React.useImperativeHandle(ref, () => ({
    clear,
    handleClose,
    setItem,
  }));

  const changeFn = (item) => {
    if (formik?.onChange) {
      formik?.onChange(item);
    }
    if (onChange) {
      onChange(item);
    }
  };

  const RenderItems = ({ items }) => {
    if (items.length <= 0) return <></>;
    return (
      <>
        {items?.map((item) => {
          if (item.children && item.children?.length > 0) {
            return (
              <TreeItem
                key={item.id}
                onClick={() => {
                  if (!disableParent) {
                    // 展开
                    setExpanded((oldExpanded) => {
                      const index = oldExpanded.indexOf(String(item.id));
                      if (index > -1) {
                        return oldExpanded.slice(0, index);
                      }
                      return [...oldExpanded, String(item.id)];
                    });
                    return;
                  }
                  // 展开
                  let first = false;
                  setExpanded((oldExpanded) => {
                    const index = oldExpanded.indexOf(
                      String(item[optionValue])
                    );
                    if (index > -1) {
                      return oldExpanded.slice(0, index);
                    }
                    first = true;
                    return [...oldExpanded, String(item.id)];
                  });

                  //首次点击顶级结点，直接退出不进行选中
                  if (first) {
                    return;
                  }
                  if (!item || !item.name || !item.id) return;
                  setEquipmentId(item.id);
                  setEquipmentItem(item.name);
                  setAnchorEl(null);
                  changeFn(item);
                }}
                nodeId={String(item?.id)}
                label={
                  <Typography
                    sx={{ p: 1.2, whiteSpace: "wrap" }}
                    variant="body2">
                    {item?.name}
                  </Typography>
                }>
                <RenderItems items={item.children} />
              </TreeItem>
            );
          } else {
            return (
              <TreeItem
                key={item.id}
                nodeId={String(item.id)}
                onClick={() => {
                  // 展开
                  setExpanded((oldExpanded) => {
                    const index = oldExpanded.indexOf(String(item.id));
                    if (index > -1) {
                      return oldExpanded.slice(0, index);
                    }
                    return [...oldExpanded, String(item.id)];
                  });

                  if (!item || !item.name || !String(item.id)) return;
                  setEquipmentId(item.id);
                  setEquipmentItem(item.name);
                  setAnchorEl(null);
                  changeFn(item);
                }}
                label={
                  <Typography
                    sx={{ p: 1.2, whiteSpace: "wrap" }}
                    variant="body2">
                    {item?.name}
                  </Typography>
                }
              />
            );
          }
        })}
      </>
    );
  };

  return (
    <>
      <Measure bounds>
        {({
          measureRef,
          contentRect: {
            bounds: { width },
          },
        }) => (
          <Stack
            ref={measureRef}
            direction={labelpostion == "left" ? "row" : "column"}
            sx={{
              alignItems: labelpostion == "left" ? "flex-start" : "",
              width: "100%",
            }}
            spacing={1}>
            {label && (
              <InputLabel
                style={{
                  color: "#474b4fcc",
                  fontSize: "14px",
                }}
                htmlFor={"zkInput_" + name}>
                {label} {props.required && <RequirePoint></RequirePoint>}
              </InputLabel>
            )}
            <Stack
              sx={{
                flexGrow: 1,
                width: "100%",
                borderRadius: "15px",
              }}>
              <TextField
                error={error}
                placeholder={placeholder}
                variant="outlined"
                required={false}
                label={""}
                name="equipmentItem"
                id="equipmentItem"
                // defaultValue={equipmentItem}
                value={equipmentItem}
                className="w-100"
                size={size}
                disabled={disabled}
                inputProps={{ readOnly: true }}
                onClick={handleClick}
                fullWidth
                sx={{
                  "& .MuiOutlinedInput-input": {
                    height: "35px",
                    borderRadius: "25px",
                    fontSize: "14px",
                  },

                  "& .MuiInputBase-root ": {
                    lineHeight: "0.6rem",
                    height: pxToRem(46),
                  },
                }}
                InputProps={{
                  endAdornment:
                    equipmentItem && isClear ? (
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          setEquipmentItem("");
                          setEquipmentId("");
                          setExpanded([]);
                          onClear();
                        }}>
                        <DeleteIcon />
                      </IconButton>
                    ) : (
                      <ExpandMoreOutlinedIcon
                        style={{ color: "#CECECE" }}></ExpandMoreOutlinedIcon>
                    ),
                }}
              />
              {((formik?.touched[name] && formik?.errors[name]) || error) && (
                <FormHelperText
                  error
                  id={`standard-weight-helper-text-${name}`}>
                  {formik?.errors[name] || error}
                </FormHelperText>
              )}
            </Stack>

            <StyledPopover
              id={id}
              open={open}
              anchorEl={anchorEl}
              onClose={handleClose}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "left",
              }}
              __width={width}>
              {data?.length > 0 ? (
                <StyledTreeView
                  defaultSelected={equipmentId}
                  selected={equipmentId}
                  aria-label="tree-view"
                  defaultCollapseIcon={<ExpandMoreIcon sx={{ fontSize: 40 }} />}
                  defaultExpandIcon={
                    dir === "ltr" ? (
                      <ChevronRightIcon sx={{ fontSize: 40 }} />
                    ) : (
                      <ChevronLeftIcon sx={{ fontSize: 40 }} />
                    )
                  }
                  expanded={expanded}>
                  <RenderItems items={data == null ? [] : data} />
                </StyledTreeView>
              ) : (
                <Typography p={1} color="text.secondary">
                  {emptyLabel}
                </Typography>
              )}
            </StyledPopover>
          </Stack>
        )}
      </Measure>
    </>
  );
});
export default ZKTreeSelect;

ZKTreeSelect.propTypes = {
  data: PropTypes.object,
  label: PropTypes.string,
  optionValue: PropTypes.string,
  optionLabel: PropTypes.string,
  onChange: PropTypes.func,
  onClear: PropTypes.func,
  dir: "ltr" | "rtl",
  emptyLabel: PropTypes.string,
  defaultValue: PropTypes.object,
};
