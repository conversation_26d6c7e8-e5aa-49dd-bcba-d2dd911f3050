import request from "@/utils/request";

const baseProfixURI = `${import.meta.env.VITE_APICODE}/map`;

/**
 *  百度地点查询检索
 * <AUTHOR>
 * @date 2022-12-14 15:29
 */
export const getAddress = (query, region) => {
  return request({
    url: `${baseProfixURI}/query/${query}/${region}`,
    method: "get",
  });
};

/**
 * 谷歌地图查询检索
 *
 */

//获取国家
export const getCounty = () => {
  return request({
    url: `/globalArea/country`,
    method: "get",
  });
};

//获取省份
export const getGoogleProvince = (value) => {
  if (!value) {
    return Promise.resolve({ data: [] }); // 如果参数不存在，返回一个空数据的 Promise
  }
  return request({
    url: `/globalArea/province/${value}`,
    method: "get",
  });
};

//获取城市
export const getGoogleCity = (value) => {
  if (!value) {
    return Promise.resolve({ data: [] }); // 如果参数不存在，返回一个空数据的 Promise
  }
  return request({
    url: `/globalArea/city/${value}`,
    method: "get",
  });
};

//谷歌地图模糊搜索
export const postGoogleData = () => {
  return request({
    url: `/globalArea/treeList`,
    method: "post",
  });
};
