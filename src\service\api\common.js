import request from "@/utils/request";

const baseProfixURI = `${import.meta.env.VITE_APICODE}`;

/**
 *  获取系统详情
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const getSystemInfo = (params) => {
  return request({
    url: `${baseProfixURI}/system_info`,
    method: "get",
    params: params,
  });
};




/**
 *  获取应用列表
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */
export const getAppList = (params) => {
  return request({
    url: `${baseProfixURI}/auth/application/query/list`,
    method: "get",
    params: params,
  });
};


