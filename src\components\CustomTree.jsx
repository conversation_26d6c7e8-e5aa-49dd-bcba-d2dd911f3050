import * as React from "react";
import clsx from "clsx";
import { styled, useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import ViewIcon from "@/assets/Icons/Viewicon.svg?react";
import EditorIcon from "@/assets/Icons/EditorIcon.svg?react";
import DeleteIcon from "@/assets/Icons/DeteleIcon.svg?react";
import EmptyIcon from "@/assets/Images/<EMAIL>";
import { SimpleTreeView } from "@mui/x-tree-view/SimpleTreeView";
import {
  TreeItem2Content,
  TreeItem2IconContainer,
  TreeItem2Root,
  TreeItem2GroupTransition,
} from "@mui/x-tree-view/TreeItem2";
import { useTreeItem2 } from "@mui/x-tree-view/useTreeItem2";
import { TreeItem2Provider } from "@mui/x-tree-view/TreeItem2Provider";
import { TreeItem2Icon } from "@mui/x-tree-view/TreeItem2Icon";
import SvgIcon from "@mui/material/SvgIcon";
import AuthButton from "@/components/AuthButton.jsx";
const CustomTreeItemRoot = styled(TreeItem2Root)(({ theme }) => ({
  color: theme.palette.text.secondary,
}));

const CustomTreeItemContent = styled(TreeItem2Content)(({ theme }) => ({
  marginBottom: theme.spacing(0.3),
  color: theme.palette.text.secondary,
  borderRadius: "1px",
  paddingRight: theme.spacing(5),
  paddingLeft: theme.spacing(5),
  fontWeight: theme.typography.fontWeightMedium,
  "&.expanded": {
    fontWeight: theme.typography.fontWeightRegular,
  },
  "&:hover": {
    backgroundColor: theme.palette.action.hover,
  },
  "&.focused, &.selected, &.selected.focused": {
    backgroundColor: `var(--tree-view-bg-color, ${theme.palette.action.selected})`,
    color: "var(--tree-view-color)",
  },
}));

const CustomTreeItemIconContainer = styled(TreeItem2IconContainer)(
  ({ theme }) => ({
    marginRight: theme.spacing(1),
  })
);

const CustomTreeItemGroupTransition = styled(TreeItem2GroupTransition)(
  ({ theme }) => ({
    marginLeft: 0,
    [`& .content`]: {
      paddingLeft: theme.spacing(8),
    },
  })
);

const CustomTreeItem = React.forwardRef(function CustomTreeItem(props, ref) {
  const theme = useTheme();
  const {
    id,
    itemId,
    label,
    disabled,
    children,
    bgColor,
    color,
    labelIcon: LabelIcon,
    labelInfo,
    colorForDarkMode,
    bgColorForDarkMode,
    handlerView = () => {},
    handlerDetele = () => {},
    handlerEditor = () => {},
    buttonAuth,
    data,
    ...other
  } = props;

  //

  const {
    getRootProps,
    getContentProps,
    getIconContainerProps,
    getLabelProps,
    getGroupTransitionProps,
    status,
  } = useTreeItem2({ id, itemId, children, label, disabled, rootRef: ref });

  const style = {
    "--tree-view-color":
      theme.palette.mode !== "dark" ? color : colorForDarkMode,
    "--tree-view-bg-color":
      theme.palette.mode !== "dark" ? bgColor : bgColorForDarkMode,
  };

  const iconStyle = {
    fontSize: 16,
    width: 20,
    height: 20,
    color: "#8a8a8a",
  };

  const actionBtnStyle = {
    minWidth: "40px",
  };

  const viewIconStyle = {
    fontSize: 26,
    width: 26,
    height: 26,
    color: "#8a8a8a",
  };
  const renderActionButton = (type, Icon, handler) => {
    const authKey = type.toLowerCase();
    const showKey = `isShow${type}`;

    // 对于编辑按钮，检查 weight 是否为 0
    if (type == "Editor" && data.weight == 0) {
      return null;
    }

    // 只要 isShow 为 true 就显示按钮，权限控制交给 AuthButton 组件
    if (buttonAuth[showKey]) {
      return (
        <AuthButton button={buttonAuth[authKey]}>
          <Button
            sx={actionBtnStyle}
            onClick={(event) => {
              event.stopPropagation();
              handler(itemId, data);
            }}>
            <SvgIcon
              sx={type == "View" ? viewIconStyle : iconStyle}
              component={Icon}
              inheritViewBox
            />
          </Button>
        </AuthButton>
      );
    }
    return null;
  };

  return (
    <TreeItem2Provider itemId={itemId}>
      <CustomTreeItemRoot {...getRootProps({ ...other, style })}>
        <CustomTreeItemContent
          {...getContentProps({
            className: clsx("content", {
              expanded: status.expanded,
              selected: status.selected,
              focused: status.focused,
            }),
          })}>
          <CustomTreeItemIconContainer {...getIconContainerProps()}>
            <TreeItem2Icon status={status} />
          </CustomTreeItemIconContainer>
          <Box
            sx={{
              display: "flex",
              flexGrow: 1,
              alignItems: "center",
              p: 1,
            }}>
            <Box component={LabelIcon} color="inherit" sx={{ mr: 1 }} />

            <Typography
              {...getLabelProps({
                variant: "h6",
                sx: { display: "flex", fontWeight: "inherit", flexGrow: 1 },
              })}
            />

            <Box sx={{ display: "flex", alignItems: "center" }}>
              {renderActionButton("Editor", EditorIcon, handlerEditor)}
              {renderActionButton("View", ViewIcon, handlerView)}
              {renderActionButton("Delete", DeleteIcon, handlerDetele)}
            </Box>

            <Grid>
              <AuthButton button={buttonAuth.update}>
                <Button
                  sx={actionBtnStyle}
                  onClick={(event) => {
                    event.stopPropagation(); // 阻止事件冒泡
                    handlerEditor(itemId);
                  }}>
                  <SvgIcon
                    sx={{ ...iconStyle }}
                    component={EditorIcon}
                    inheritViewBox
                  />
                </Button>
              </AuthButton>

              <AuthButton button={buttonAuth.view}>
                <Button
                  sx={actionBtnStyle}
                  onClick={(event) => {
                    event.stopPropagation(); // 阻止事件冒泡
                    handlerView(itemId);
                  }}>
                  <SvgIcon
                    sx={{
                      fontSize: 26,
                      width: 26,
                      height: 26,
                      color: "#8a8a8a",
                    }}
                    component={ViewIcon}
                    inheritViewBox
                  />
                </Button>
              </AuthButton>

              <AuthButton button={buttonAuth.detele}>
                <Button
                  sx={actionBtnStyle}
                  onClick={(event) => {
                    event.stopPropagation(); // 阻止事件冒泡
                    handlerDetele(itemId);
                  }}>
                  <SvgIcon
                    sx={{ ...iconStyle }}
                    component={DeleteIcon}
                    inheritViewBox
                  />
                </Button>
              </AuthButton>
            </Grid>
          </Box>
        </CustomTreeItemContent>
        {children && (
          <CustomTreeItemGroupTransition {...getGroupTransitionProps()} />
        )}
      </CustomTreeItemRoot>
    </TreeItem2Provider>
  );
});

function EndIcon() {
  return <div style={{ width: 24 }} />;
}

function CustomTree(props) {
  const {
    treeData,
    itemId,
    handlerEditor,
    handlerDetele,
    handlerView = () => {},
    buttonAuth,
  } = props;

  // 递归渲染树节点
  const renderTree = (nodes) =>
    nodes?.map((node) => (
      <CustomTreeItem
        key={node.id}
        itemId={node.id}
        label={node.name}
        data={node} // 传递整个 node 对象
        handlerEditor={handlerEditor}
        handlerDetele={handlerDetele}
        handlerView={handlerView}
        buttonAuth={buttonAuth}>
        {node.children && renderTree(node.children)}
      </CustomTreeItem>
    ));

  return (
    <>
      {treeData && treeData.length > 0 ? (
        <SimpleTreeView
          aria-label="gmail"
          defaultExpandedItems={["3"]}
          defaultSelectedItems="5"
          slots={{
            endIcon: EndIcon,
          }}
          sx={{ flexGrow: 1 }}>
          {renderTree(treeData)}
        </SimpleTreeView>
      ) : (
        <DefalueIcon />
      )}
    </>
  );
}

export default CustomTree;

const DefalueIcon = () => {
  const { t } = useTranslation();
  return (
    <Grid
      container
      alignItems="center"
      justifyContent="center"
      sx={{ height: "300px", marginTop: "80px" }} // 自定义高度
    >
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center">
        <img
          src={EmptyIcon}
          alt="No Data"
          style={{ width: "120px", marginBottom: "8px" }}
        />
        <Typography variant="body1" color="textSecondary">
          {t("No Content found.")}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t("Click on the '+' button to add Content")}
        </Typography>
      </Box>
    </Grid>
  );
};
