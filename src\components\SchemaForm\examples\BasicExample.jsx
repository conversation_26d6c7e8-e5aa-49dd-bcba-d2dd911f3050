import React, { useState } from 'react';
import { Box, Button, Typography, Paper } from '@mui/material';
import { SchemaForm<PERSON>enderer } from '../index';

/**
 * 基础Schema表单示例
 */
const BasicExample = () => {
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});

  // 基础表单Schema配置
  const basicSchema = {
    type: 'object',
    title: '用户信息表单',
    properties: {
      // 基础信息组
      basicInfo: {
        layout: {
          type: 'group',
          title: '基础信息',
          description: '请填写您的基础信息'
        },
        fields: [
          {
            name: 'username',
            type: 'string',
            title: '用户名',
            required: true,
            minLength: 3,
            maxLength: 20,
            pattern: '^[a-zA-Z0-9_]+$'
          },
          {
            name: 'email',
            type: 'string',
            format: 'email',
            title: '邮箱地址',
            required: true
          },
          {
            name: 'password',
            type: 'password',
            title: '密码',
            required: true,
            minLength: 6
          }
        ]
      },
      
      // 个人信息组
      personalInfo: {
        layout: {
          type: 'grid',
          columns: 2,
          title: '个人信息'
        },
        fields: [
          {
            name: 'firstName',
            type: 'string',
            title: '名字',
            required: true
          },
          {
            name: 'lastName',
            type: 'string',
            title: '姓氏',
            required: true
          },
          {
            name: 'age',
            type: 'integer',
            title: '年龄',
            minimum: 18,
            maximum: 100
          },
          {
            name: 'gender',
            type: 'string',
            title: '性别',
            widget: 'radio',
            enum: ['male', 'female', 'other'],
            enumNames: ['男', '女', '其他']
          }
        ]
      },
      
      // 联系信息
      contactInfo: {
        layout: {
          type: 'card',
          title: '联系信息',
          collapsible: true
        },
        fields: [
          {
            name: 'phone',
            type: 'tel',
            title: '手机号码',
            pattern: '^1[3-9]\\d{9}$'
          },
          {
            name: 'address',
            type: 'string',
            widget: 'textarea',
            title: '地址',
            rows: 3
          },
          {
            name: 'country',
            type: 'string',
            title: '国家',
            widget: 'select',
            enum: ['CN', 'US', 'UK', 'JP'],
            enumNames: ['中国', '美国', '英国', '日本'],
            default: 'CN'
          }
        ]
      },
      
      // 偏好设置
      preferences: {
        layout: {
          type: 'group',
          title: '偏好设置'
        },
        fields: [
          {
            name: 'newsletter',
            type: 'boolean',
            title: '订阅邮件通知',
            default: false
          },
          {
            name: 'language',
            type: 'string',
            title: '首选语言',
            widget: 'select',
            enum: ['zh-CN', 'en-US', 'ja-JP'],
            enumNames: ['中文', 'English', '日本語'],
            default: 'zh-CN'
          },
          {
            name: 'interests',
            type: 'array',
            title: '兴趣爱好',
            widget: 'select',
            multiple: true,
            items: {
              type: 'string',
              enum: ['technology', 'sports', 'music', 'travel', 'reading'],
              enumNames: ['科技', '运动', '音乐', '旅行', '阅读']
            }
          }
        ]
      }
    }
  };

  // UI Schema配置
  const uiSchema = {
    username: {
      placeholder: '请输入用户名',
      help: '用户名只能包含字母、数字和下划线'
    },
    password: {
      showPasswordToggle: true,
      placeholder: '请输入密码'
    },
    email: {
      placeholder: '请输入邮箱地址'
    },
    address: {
      placeholder: '请输入详细地址'
    },
    interests: {
      placeholder: '选择您的兴趣爱好'
    }
  };

  // 条件逻辑Schema
  const conditionalSchema = {
    ...basicSchema,
    properties: {
      ...basicSchema.properties,
      // 添加条件逻辑示例
      advancedSettings: {
        layout: {
          type: 'group',
          title: '高级设置'
        },
        fields: [
          {
            name: 'isVip',
            type: 'boolean',
            title: '是否VIP用户',
            default: false
          },
          {
            name: 'vipLevel',
            type: 'string',
            title: 'VIP等级',
            widget: 'select',
            enum: ['bronze', 'silver', 'gold', 'platinum'],
            enumNames: ['青铜', '白银', '黄金', '铂金'],
            // 条件显示：只有当isVip为true时才显示
            if: {
              field: 'isVip',
              operator: 'equals',
              value: true
            }
          },
          {
            name: 'specialRequests',
            type: 'string',
            widget: 'textarea',
            title: '特殊需求',
            rows: 2,
            // 条件必填：VIP用户必须填写特殊需求
            requiredIf: {
              field: 'isVip',
              operator: 'equals',
              value: true
            }
          }
        ]
      }
    }
  };

  const handleChange = (newData) => {
    setFormData(newData);
    console.log('Form data changed:', newData);
  };

  const handleValidate = (newErrors) => {
    setErrors(newErrors);
    console.log('Validation errors:', newErrors);
  };

  const handleSubmit = () => {
    console.log('Submitting form data:', formData);
    alert('表单提交成功！请查看控制台输出。');
  };

  const handleReset = () => {
    setFormData({});
    setErrors({});
  };

  return (
    <Box sx={{ maxWidth: 800, margin: '0 auto', padding: 3 }}>
      <Typography variant="h4" gutterBottom>
        Schema表单示例
      </Typography>
      
      <Typography variant="body1" paragraph>
        这是一个基于JSON Schema的配置化表单示例，展示了新的Schema驱动配置风格的特点：
      </Typography>
      
      <Box component="ul" sx={{ mb: 3 }}>
        <li>声明式配置：通过JSON Schema定义表单结构</li>
        <li>类型安全：基于Schema的类型验证</li>
        <li>布局灵活：支持多种布局组件（Group、Grid、Card等）</li>
        <li>条件逻辑：支持字段的条件显示、必填、禁用等</li>
        <li>可扩展性：插件化的字段注册机制</li>
        <li>国际化：内置多语言支持</li>
      </Box>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <SchemaFormRenderer
          schema={conditionalSchema}
          uiSchema={uiSchema}
          formData={formData}
          onChange={handleChange}
          onValidate={handleValidate}
        />
        
        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={Object.keys(errors).length > 0}
          >
            提交表单
          </Button>
          <Button
            variant="outlined"
            onClick={handleReset}
          >
            重置表单
          </Button>
        </Box>
      </Paper>

      <Paper elevation={1} sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          当前表单数据：
        </Typography>
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px',
          overflow: 'auto',
          fontSize: '12px'
        }}>
          {JSON.stringify(formData, null, 2)}
        </pre>
        
        {Object.keys(errors).length > 0 && (
          <>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              验证错误：
            </Typography>
            <pre style={{ 
              background: '#ffebee', 
              padding: '10px', 
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '12px',
              color: '#c62828'
            }}>
              {JSON.stringify(errors, null, 2)}
            </pre>
          </>
        )}
      </Paper>
    </Box>
  );
};

export default BasicExample;
