// 看板
const messageLogRoute = [
  {
    path: "/menuItem/viewCompany",
    component: () => import("@l/MenuItem/ViewCompany"),
    meta: {
      title: "ViewCompany",
      i18n: "view_company",
      id: "89698975898812158896",
    },
  },

  {
    path: "/menuItem/userprofile",
    component: () => import("@l/MenuItem/UserProfileMenu"),
    meta: {
      title: "UserProfileMenu",
      i18n: "userProfile_menu",
      id: "5186975898812158896",
    },
  },

  {
    path: "/menuItem/mySubsctiption",
    component: () => import("@l/MenuItem/MySubscription"),
    meta: {
      title: "MySubscription",
      i18n: "MySubscription",
      id: "5186965464562158896",
    },
  },
];
export default messageLogRoute;
