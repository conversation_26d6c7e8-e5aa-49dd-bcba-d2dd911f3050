/* eslint-disable react/prop-types */
import React, { Fragment } from "react";
import Cell from "./Cell";

export default function Row(props) {
  const { prefixCls, layout, bordered, column, colon, children = [] } = props;
  function handleCell(isHead) {
    return children.map((child, index) => (
      <Cell
        {...child.props}
        prefixCls={prefixCls}
        key={index}
        column={column}
        tagName={isHead ? "th" : "td"}
        isLastCell={children.length - 1 === index}
        layout={layout}
        colon={colon}
        bordered={bordered}
      >
        {isHead ? child.props.label : child.props.children}
      </Cell>
    ));
  }
  const cls = prefixCls ? `${prefixCls}-row` : "";
  return (
    <Fragment>
      {layout === "vertical" && <tr className={cls}>{handleCell(true)}</tr>}
      <tr className={cls}>{handleCell()}</tr>
    </Fragment>
  );
}
