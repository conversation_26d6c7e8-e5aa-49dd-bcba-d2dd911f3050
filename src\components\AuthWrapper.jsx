import { Grid } from "@mui/material";
import PropTypes from "prop-types";
import bg from "@/assets/Images/BroundImage/GlobalBg.png";
const AuthWrapper = ({ children }) => {
  return (
    <Grid
      sx={{
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        backgroundSize: "cover",
        backgroundImage: `url(${bg})`,
      }}>
      <Grid
        sx={{
          flexGrow: 1,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "transparent",
        }}>
        {children}
      </Grid>
    </Grid>
  );
};

AuthWrapper.propTypes = {
  children: PropTypes.node,
};

export default AuthWrapper;
