import { Grid } from "@mui/material";
import PropTypes from "prop-types";
import { useMediaQuery } from "@mui/material";
import { pxToRem } from "@/utils/zkUtils";
const LoginBox = ({ children }) => {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("sm"));
  return (
    <Grid
      sx={{
        width: pxToRem(600),
        minHeight: pxToRem(400),
        background: "linear-gradient(162deg, #ffffff, #E6E6EB)",
        boxShadow: "0px 3px 6px #0000000D",
        border: "1px solid #E0E0E0",
        borderRadius: "30px",
        padding: "30px",
        maxWidth: isMobile ? "100%" : "50%",
        maxHeight: "80%",
        display: "flex",
        flexDirection: "column",
      }}>
      {children}
    </Grid>
  );
};

LoginBox.propTypes = {
  children: PropTypes.node,
};

export default LoginBox;
