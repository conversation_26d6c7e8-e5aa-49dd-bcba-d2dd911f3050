import i18n from "i18next";
const screenStatus = [
  {
    label: i18n.t("common.common_offline"),
    value: "1",
    color: "error",
  },
  {
    label: i18n.t("common.common_online"),
    value: "0",
    color: "success",
  },
];

const adbSwitchTab = [
  {
    label: i18n.t("common.event_enable"),
    value: "1",
  },
  {
    label: i18n.t("common.event_disable"),
    value: "0",
  },
];

const deviceTypies = [
  {
    label: "LCD",
    value: "0",
    color: "success",
  },
  {
    label: "LED",
    value: "1",
    color: "error",
  },
];

export { screenStatus, adbSwitchTab, deviceTypies };
