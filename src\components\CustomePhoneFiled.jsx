import { Box, Divider } from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import cn from "react-phone-input-2/lang/cn.json";
import es from "react-phone-input-2/lang/es.json";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/material.css";

import { isEmptyString } from "../utils/zkUtils";
const CustomePhoneFiled = (props) => {
  const { t } = useTranslation();

  return (
    <Box variant="standard" style={{ width: "100%" }}>
      <InputLabel shrink htmlFor="bootstrap-input" style={{ fontSize: "18px" }}>
        {props.label}
        {props.required ? <span style={{ color: "red" }}>*</span> : null}
      </InputLabel>
      <TextField
        {...props}
        autoComplete="off"
        fullWidth={true}
        sx={{
          "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall": {
            fontSize: "14px",
          },
          "& .MuiInputBase-root ": {
            lineHeight: "0.6rem",
          },
        }}
        type={props.type}
        value={props.value}
        helperText={props.helperText}
        error={props.error}
        name={props.name}
        label={""}
        onChange={(e) => props.handleChange(e)}
        InputProps={{
          startAdornment: (
            <>
              <PhoneInput
                disabled={props.disabled}
                autoFormat={true}
                countryCodeEditable={true}
                enableSearch={true}
                searchPlaceholder={"Search"}
                searchNotFound={"No Options Found"}
                onChange={(value, data) => {
                  props.handleCountryCode(data);
                }}
                localization={"cn"}
                inputProps={{
                  disabled: true,
                  margin: "0px 10px",
                }}
                value={props.countryCode}
                placeholder={t("国家编码")}
                specialLabel={true}
                isValid={isEmptyString(props.error)}
                style={{ width: "auto" }}
                inputStyle={{
                  background: props.disabled ? "var(--tw-bg-gray-50)" : "",
                  marginBottom: "0px",
                  lineHeight: "0.6rem",
                  border: "none",
                  width: "150px",
                }}
              />

              <Divider
                orientation="vertical"
                flexItem
                style={{ marginRight: "20px" }}
              />
            </>
          ),
        }}
      />
    </Box>
  );
};

export default CustomePhoneFiled;
