import React from "react";
import { Grid, Link as MuiLink, TextField } from "@mui/material";
import SearchForm from "@c/SearchForm";
import { useFormik } from "formik";
import { useState } from "react";
import ZktecoTable from "@c/ZktecoTable";
import { getDictList } from "@/service/api/dict.js";
import LayoutList from "@/layout/components/LayoutList.jsx";
import DictTag from "@c/DictTag";
import useDict from "@/hooks/useDict.js";
import { pxToRem } from "@u/zkUtils";
import CustomInput from "@c/CustInput.jsx";
import AddDict from "./AddDictData";

const index = () => {
  const { t } = useTranslation();
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const dictData = useDict(["common_status", "site_timezone_type"]);
  const [type, setType] = useState(null);
  const [open, setOpen] = useState(false);
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name", //access nested data with dot notation
        header: t("字典类型名称"),
      },
      {
        accessorKey: "code", //access nested data with dot notation
        header: t("字典类型编码"),
        Cell: ({ renderedCellValue, row }) => (
          <MuiLink variant="body2">{renderedCellValue}</MuiLink>
        ),
      },
      // {
      //   accessorKey: "remark", //access nested data with dot notation
      //   header: t("描述"),
      // },
      {
        accessorKey: "status", //access nested data with dot notation
        header: t("状态"),
        Cell: ({ renderedCellValue }) => {
          return (
            <DictTag
              dicts={dictData.current["common_status"]}
              value={renderedCellValue}
              fieldName={{
                value: "value",
                title: "label",
                color: "color",
              }}
            />
          );
        },
      },
    ],
    []
  );
  // 构建查询参数
  const buildParams = (values = {}) => ({
    page: pagination.pageIndex + 1,
    pageSize: pagination.pageSize,
    label: values.label || "",
    dictTypeCode: values.dictTypeCode || "", // 字典类型
  });

  // 获取表格数据
  const getTableData = async (searchValues = {}) => {
    setIsLoading(true);
    try {
      const params = buildParams(searchValues);
      const { data } = await getDictList(params);
      setData(data?.data || []);
      setRowCount(data?.total || 0);
    } catch (error) {
      setIsError(true);
      console.error("获取字典数据失败:", error);
    } finally {
      setIsLoading(false);
      setIsRefetching(false);
    }
  };

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      label: "",
      dictTypeCode: "",
    },
    onSubmit: (values) => {
      setPagination((prev) => ({ ...prev, pageIndex: 0 })); // 重置页码
      getTableData(values);
    },
  });

  // 监听分页变化
  useEffect(() => {
    getTableData(queryFormik.values);
  }, [pagination.pageIndex, pagination.pageSize]);
  return (
    <>
      <LayoutList
        title={"字典数据"}
        header={
          <SearchForm formik={queryFormik} reloadData={() => getTableData()}>
            <CustomInput
              value={queryFormik.values.name}
              onChange={queryFormik.handleChange}
              onBlur={queryFormik.handleBlur}
              size="small"
              type="text"
              width={pxToRem(264)}
              name="name"
              fullWidth
              placeholder={t("字典类型")}
            />
          </SearchForm>
        }
        content={
          <ZktecoTable
            showAdd={"system:dict_data:save"}
            showTopBar={false}
            // renderToolbarInternalActions={({ table }) => {
            //   return null;
            // }}

            addCallback={() => {
              setOpen(true);
              setType("add");
            }}
            data={data}
            columns={columns}
            loadDada={getTableData} // 刷新 方法
            // 分页回调函数
            onPaginationChange={(newPagination) => {
              setPagination(newPagination);
            }}
            // 列数
            rowCount={rowCount}
            getRowId={(originalRow) => {
              return originalRow.devSn;
            }}
            state={{
              // 加载状态
              isLoading,
              // 分页参数
              pagination,
              rowSelection: true,
            }}
            totalRecords={rowCount || 0} // 如果 data 为空时避免报错
            rowsPerPage={pagination.pageSize}
            currentPage={pagination.pageIndex}
            onPageChange={(pageIndex) => {
              setPagination((prev) => ({
                ...prev,
                pageIndex, // 更新页码
              }));
            }}
            isShowAction={{
              isShowEditor: true,
            }}
            handlerEditor={async (row) => {
              setOpen(true);
              setType("editor");
              // const res = await getDictTypeByName(row.name);
            }}
            onPageSizeChange={(pageSize) => {
              setPagination({
                pageIndex: 0, // 重置页码
                pageSize, // 更新每页行数
              });
            }}></ZktecoTable>
        }
      />

      <AddDict
        open={open}
        setOpen={setOpen}
        type={type}
        getTableData={getTableData}></AddDict>
    </>
  );
};
export default index;
