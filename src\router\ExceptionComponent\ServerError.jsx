import React from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Container,
  Paper,
  useTheme,
  useMediaQuery,
  Alert,
  <PERSON>ert<PERSON><PERSON>le,
  Chip,
  LinearProgress,
} from "@mui/material";
import { styled, keyframes } from "@mui/material/styles";
import { Error, Refresh, Home, BugReport, Schedule } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";

const pulse = keyframes`
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
`;

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

// 主容器
const MainContainer = styled(Box)(({ theme }) => ({
  minHeight: "100vh",
  background: `linear-gradient(135deg, 
    ${theme.palette.error.light}15 0%, 
    ${theme.palette.background.default} 100%)`,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: theme.spacing(2),
}));

// 内容卡片
const ContentCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(6, 4),
  borderRadius: theme.spacing(3),
  textAlign: "center",
  maxWidth: 600,
  width: "100%",
  animation: `${fadeIn} 0.8s ease-out`,
}));

// 错误图标
const ErrorIcon = styled(Error)(({ theme }) => ({
  fontSize: "6rem",
  color: theme.palette.error.main,
  marginBottom: theme.spacing(2),
  animation: `${pulse} 2s ease-in-out infinite`,
}));

const ServerError = ({
  title = "服务器错误",
  message = "服务器遇到了一个错误，无法完成您的请求",
  errorCode = "500",
  estimatedFixTime,
  showRetryCountdown = false,
  onRetry,
  ...props
}) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [retryCountdown, setRetryCountdown] = React.useState(30);

  // 自动重试倒计时
  React.useEffect(() => {
    if (showRetryCountdown && retryCountdown > 0) {
      const timer = setTimeout(() => {
        setRetryCountdown(retryCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (showRetryCountdown && retryCountdown === 0) {
      handleRetry();
    }
  }, [retryCountdown, showRetryCountdown]);

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    navigate("/");
  };

  const troubleshootingSteps = [
    "稍等几分钟后重新尝试",
    "检查网络连接是否正常",
    "清除浏览器缓存和Cookie",
    "尝试使用其他浏览器",
    "联系技术支持团队",
  ];

  return (
    <MainContainer {...props}>
      <Container
        maxWidth="md"
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}>
        <ContentCard elevation={3}>
          {/* 错误代码 */}
          <Chip
            label={`错误代码: ${errorCode}`}
            color="error"
            variant="outlined"
            sx={{ mb: 2 }}
          />

          {/* 主图标 */}
          <ErrorIcon />

          {/* 标题 */}
          <Typography
            variant={isMobile ? "h4" : "h3"}
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 600,
              color: "text.primary",
              mb: 2,
            }}>
            {title}
          </Typography>

          {/* 描述 */}
          <Typography
            variant="h6"
            color="text.secondary"
            paragraph
            sx={{
              mb: 4,
              lineHeight: 1.6,
              maxWidth: 500,
              mx: "auto",
            }}>
            {message}
          </Typography>

          {/* 预计修复时间 */}
          {estimatedFixTime && (
            <Alert severity="info" sx={{ mb: 3 }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Schedule />
                <Typography variant="body2">
                  预计修复时间: {estimatedFixTime}
                </Typography>
              </Box>
            </Alert>
          )}

          {/* 自动重试倒计时 */}
          {showRetryCountdown && retryCountdown > 0 && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {retryCountdown} 秒后自动重试
              </Typography>
              <LinearProgress
                variant="determinate"
                value={((30 - retryCountdown) / 30) * 100}
                sx={{ borderRadius: 1 }}
              />
            </Box>
          )}

          {/* 操作按钮 */}
          <Box
            sx={{
              display: "flex",
              gap: 2,
              justifyContent: "center",
              flexWrap: "wrap",
              mb: 4,
            }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<Refresh />}
              onClick={handleRetry}
              sx={{
                borderRadius: 3,
                px: 4,
                py: 1.5,
              }}>
              重新加载
            </Button>

            <Button
              variant="outlined"
              size="large"
              startIcon={<Home />}
              onClick={handleGoHome}
              sx={{
                borderRadius: 3,
                px: 4,
                py: 1.5,
              }}>
              返回首页
            </Button>
          </Box>

          {/* 故障排除建议 */}
          <Alert severity="info" sx={{ textAlign: "left", mb: 3 }}>
            <AlertTitle>故障排除建议</AlertTitle>
            <Box component="ol" sx={{ pl: 2, m: 0 }}>
              {troubleshootingSteps.map((step, index) => (
                <Typography
                  key={index}
                  component="li"
                  variant="body2"
                  sx={{ mb: 0.5 }}>
                  {step}
                </Typography>
              ))}
            </Box>
          </Alert>

          {/* 技术信息 */}
          <Box
            sx={{ mt: 4, p: 2, backgroundColor: "grey.50", borderRadius: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              技术信息
            </Typography>
            <Typography variant="body2" color="text.secondary">
              时间: {new Date().toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              请求ID: {Math.random().toString(36).substr(2, 9).toUpperCase()}
            </Typography>
          </Box>

          {/* 帮助信息 */}
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mt: 4,
              opacity: 0.7,
              fontSize: "0.875rem",
            }}>
            如果问题持续存在，请联系技术支持并提供上述技术信息
          </Typography>
        </ContentCard>
      </Container>
    </MainContainer>
  );
};

export default ServerError;
