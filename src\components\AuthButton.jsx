/* eslint-disable react/prop-types */
import React, { Component, Fragment } from "react";
import PropTypes from "prop-types";
import { useStatePermission } from "@/hooks/user";
const AuthButton = (props) => {
  const { button = "", children } = props;

  // 检查用户是否具有其中任何一个权限
  const permission = useStatePermission();
  // 将逗号分隔的字符串转换为数组

  if (button == true) {
    return children;
  } else if (button == false) {
    return null;
  } else if (permission?.includes("*:*:*")) {
    return children;
  } else {
    const buttonArray = button?.split(",")?.map((item) => item.trim());
    const hasPermission = buttonArray?.some(
      (btn) => permission?.includes("*:*:*") || permission?.includes(btn)
    );

    return hasPermission ? children : null;
  }
};

// 校验数据类型
AuthButton.propTypes = {
  button: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
};
export default AuthButton;
