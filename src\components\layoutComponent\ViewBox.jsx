import React from "react";
import TitleBar from "./TitleBar";
import { Grid, Paper } from "@mui/material";
import { useTranslation } from "react-i18next";
import AnimateButton from "@c/@extended/AnimateButton";
import LoadingButton from "@mui/lab/LoadingButton";

export default function RightViewLayout(props) {
  const { t } = useTranslation();
  const {
    handleCancle = () => {},
    handleSubmit = () => {},
    loading,
    text = t("common.common_confirm"),
    isShowSaveButton = true,
    isShowSave = true,
  } = props;
  return (
    <React.Fragment>
      <Grid
        sx={{
          display: "flex",
          flexDirection: "column",
          height: "100%",
          position: "relative",
        }}>
        <Grid
          sx={{
            height: "80px",
            backgroundColor: "#ffffff",
            border: "1px solid #ffffff",
            boxShadow: "0px 2px 6px #00000029",
            borderRadius: "6px",
            display: "flex",
            justifyContent: "center",
          }}>
          <TitleBar
            navigateBack={props.navigateBack}
            title={props.title ? props.title : ""}
            actions={props.actions}>
            {props.searchProps}
          </TitleBar>
        </Grid>

        <Grid
          item
          sx={{
            backgroundColor: "#ffffff",
            border: "1px solid #ffffff",
            boxShadow: "0px 2px 6px #00000029",
            borderRadius: "10px",
            overflowY: "auto",
            overflowX: "hidden",
            position: "relative",
            mt: 4,
            pt: 5,
            pl: 8,
            pr: 8,
          }}>
          {props.children}

          {isShowSaveButton && (
            <Grid
              item
              xs={12}
              mt={9}
              mb={2}

              // position={"fixed"}
              // bottom={"30px"}
              // right={"70px"}
            >
              <Stack
                direction="row"
                justifyContent="flex-end"
                alignItems="flex-end"
                spacing={2}>
                <Button
                  variant="outlined"
                  onClick={handleCancle}
                  style={{
                    width: "182px",
                    height: "60px",
                    borderRadius: "10px",
                    opacity: 1,
                    border: "1px solid #E3E3E3",
                  }}
                  disableElevation
                  color="info">
                  {t("common.common_edit_cancel")}
                </Button>
                {isShowSave && (
                  <AnimateButton>
                    <LoadingButton
                      loading={loading}
                      disableElevation
                      disabled={loading}
                      fullWidth
                      type="submit"
                      variant="contained"
                      onClick={handleSubmit}
                      style={{
                        width: "182px",
                        height: "60px",
                        borderRadius: "10px",
                        opacity: 1,
                        background:
                          "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                      }}>
                      {text}
                    </LoadingButton>
                  </AnimateButton>
                )}
              </Stack>
            </Grid>
          )}
        </Grid>
      </Grid>
    </React.Fragment>
  );
}
