import React, { useState, useMemo, useEffect, useCallback, useRef } from "react";
import {
  TextField,
  Popover,
  Checkbox,
  Typography,
  IconButton,
  InputAdornment,
  Box,
  OutlinedInput,
  Divider,
  Chip,
  Button,
  FormHelperText,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Collapse,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  ListItemButton,
  Avatar,
  useTheme,
  alpha,
} from "@mui/material";
import { FixedSizeList as VirtualList } from 'react-window';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  KeyboardArrowDown as ArrowDropDownIcon,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  InsertDriveFile as FileIcon,
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  IndeterminateCheckBox as IndeterminateCheckBoxIcon,
} from "@mui/icons-material";
import { styled } from '@mui/material/styles';

// 显示策略常量
const SHOW_ALL = 'SHOW_ALL';
const SHOW_PARENT = 'SHOW_PARENT';
const SHOW_CHILD = 'SHOW_CHILD';

// Material-UI 风格的样式组件
const StyledTreeContainer = styled(Box)(({ theme }) => ({
  '& .tree-node': {
    borderRadius: theme.shape.borderRadius,
    margin: '2px 0',
    transition: theme.transitions.create(['background-color', 'box-shadow'], {
      duration: theme.transitions.duration.short,
    }),
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.04),
    },
    '&.selected': {
      backgroundColor: alpha(theme.palette.primary.main, 0.08),
      '&:hover': {
        backgroundColor: alpha(theme.palette.primary.main, 0.12),
      },
    },
  },
}));

const StyledPopover = styled(Popover)(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: theme.spacing(2),
    boxShadow: theme.shadows[8],
    border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
    overflow: 'hidden',
  },
}));

/**
 * TreeSelect 组件 - 兼容 Ant Design TreeSelect API
 */
const TreeSelect = ({
  // 基础属性
  value,
  defaultValue,
  onChange,
  onSelect,
  onSearch,
  onTreeExpand,
  onDropdownVisibleChange, // 兼容旧版本
  onOpenChange,
  onPopupScroll,

  // 数据相关
  treeData = [],
  treeDataSimpleMode = false,
  fieldNames = { label: 'title', value: 'value', children: 'children' },

  // 显示相关
  placeholder = "请选择",
  notFoundContent = "暂无数据",
  size = "middle",
  variant = "outlined",
  disabled = false,

  // 下拉框相关
  open,
  defaultOpen = false,
  getPopupContainer,
  popupClassName,
  dropdownStyle, // 兼容旧版本
  styles,
  classNames,
  popupMatchSelectWidth = true,
  popupRender,
  dropdownRender, // 兼容旧版本
  placement = "bottomLeft",
  listHeight = 300, // 增加默认高度
  virtual = true,
  virtualItemSize = 40, // 虚拟滚动项目高度
  virtualThreshold = 100, // 启用虚拟滚动的阈值

  // 搜索相关
  showSearch,
  searchValue,
  filterTreeNode,
  treeNodeFilterProp = "value",

  // 选择相关
  multiple = false,
  treeCheckable = false,
  treeCheckStrictly = false,
  showCheckedStrategy = SHOW_CHILD,
  labelInValue = false,
  maxCount,
  maxTagCount,
  maxTagPlaceholder,
  maxTagTextLength,

  // 清除相关
  allowClear = false,
  autoClearSearchValue = true,

  // 展开相关
  treeDefaultExpandAll = false,
  treeDefaultExpandedKeys = [],
  treeExpandedKeys,
  treeExpandAction = false,
  treeLoadedKeys = [],

  // 异步加载
  loadData,

  // 样式相关
  treeLine = false,
  treeIcon = false,
  switcherIcon,
  suffixIcon,
  prefix,

  // 节点渲染
  treeNodeLabelProp = "title",
  treeTitleRender,
  tagRender,

  // 状态
  status,

  // 其他
  ...restProps
}) => {
  // 状态管理
  const [internalOpen, setInternalOpen] = useState(defaultOpen);
  const [internalValue, setInternalValue] = useState(defaultValue || (multiple ? [] : undefined));
  const [searchTerm, setSearchTerm] = useState(searchValue || "");
  const [expandedKeys, setExpandedKeys] = useState(
    treeExpandedKeys || (treeDefaultExpandAll ? [] : treeDefaultExpandedKeys)
  );
  const [loadingKeys, setLoadingKeys] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);

  const inputRef = useRef(null);
  const theme = useTheme();

  // 受控状态处理
  const isControlledOpen = open !== undefined;
  const isControlledValue = value !== undefined;
  const isControlledSearch = searchValue !== undefined;
  const isControlledExpanded = treeExpandedKeys !== undefined;

  const currentOpen = isControlledOpen ? open : internalOpen;
  const currentValue = isControlledValue ? value : internalValue;
  const currentSearch = isControlledSearch ? searchValue : searchTerm;
  const currentExpanded = isControlledExpanded ? treeExpandedKeys : expandedKeys;

  // 字段名映射
  const { label: labelField, value: valueField, children: childrenField } = fieldNames;

  // 处理简单模式数据
  const processedTreeData = useMemo(() => {
    if (!treeDataSimpleMode) return treeData;

    // 简单模式：将扁平数据转换为树形结构
    const { id = 'id', pId = 'pId', rootPId = null } =
      typeof treeDataSimpleMode === 'object' ? treeDataSimpleMode : {};

    const map = {};
    const roots = [];

    treeData.forEach(item => {
      map[item[id]] = { ...item, [childrenField]: [] };
    });

    treeData.forEach(item => {
      const node = map[item[id]];
      const parentId = item[pId];

      if (parentId === rootPId || !map[parentId]) {
        roots.push(node);
      } else {
        map[parentId][childrenField].push(node);
      }
    });

    return roots;
  }, [treeData, treeDataSimpleMode, childrenField]);

  // 获取所有节点的扁平列表
  const flattenNodes = useMemo(() => {
    const nodes = [];
    const traverse = (nodeList, level = 0) => {
      nodeList.forEach(node => {
        nodes.push({ ...node, level });
        if (node[childrenField]) {
          traverse(node[childrenField], level + 1);
        }
      });
    };
    traverse(processedTreeData);
    return nodes;
  }, [processedTreeData, childrenField]);

  // 搜索过滤
  const filteredTreeData = useMemo(() => {
    if (!currentSearch) return processedTreeData;

    const filterNode = (node) => {
      const nodeValue = node[treeNodeFilterProp] || '';
      const nodeLabel = node[labelField] || '';

      if (typeof filterTreeNode === 'function') {
        return filterTreeNode(currentSearch, node);
      }

      const matches = nodeValue.toLowerCase().includes(currentSearch.toLowerCase()) ||
                     nodeLabel.toLowerCase().includes(currentSearch.toLowerCase());

      if (matches) return true;

      if (node[childrenField]) {
        return node[childrenField].some(child => filterNode(child));
      }

      return false;
    };

    const mapNode = (node) => {
      if (node[childrenField]) {
        const filteredChildren = node[childrenField]
          .filter(child => filterNode(child))
          .map(child => mapNode(child));
        return { ...node, [childrenField]: filteredChildren };
      }
      return node;
    };

    return processedTreeData
      .filter(node => filterNode(node))
      .map(node => mapNode(node));
  }, [processedTreeData, currentSearch, filterTreeNode, treeNodeFilterProp, labelField, childrenField]);

  // 获取可见的扁平节点列表（考虑展开状态）
  const visibleFlatNodes = useMemo(() => {
    const nodes = [];
    const traverse = (nodeList, level = 0) => {
      nodeList.forEach(node => {
        const nodeValue = node[valueField];
        nodes.push({ ...node, level, nodeId: nodeValue });

        // 只有当节点展开时才显示子节点
        if (node[childrenField] && currentExpanded.includes(nodeValue)) {
          traverse(node[childrenField], level + 1);
        }
      });
    };
    traverse(filteredTreeData);
    return nodes;
  }, [filteredTreeData, currentExpanded, valueField, childrenField]);

  // 判断是否需要使用虚拟滚动
  const shouldUseVirtual = virtual && visibleFlatNodes.length > virtualThreshold;

  // 获取节点后代
  const getNodeDescendants = useCallback((node) => {
    const descendants = [];
    const traverse = (currentNode) => {
      if (currentNode[childrenField]) {
        currentNode[childrenField].forEach(child => {
          descendants.push(child[valueField]);
          traverse(child);
        });
      }
    };
    traverse(node);
    return descendants;
  }, [childrenField, valueField]);

  // 获取节点祖先
  const getNodeAncestors = useCallback((nodeValue) => {
    const ancestors = [];
    const findParent = (nodes, targetValue, path = []) => {
      for (const node of nodes) {
        const currentPath = [...path, node[valueField]];
        if (node[valueField] === targetValue) {
          ancestors.push(...path);
          return true;
        }
        if (node[childrenField] && findParent(node[childrenField], targetValue, currentPath)) {
          return true;
        }
      }
      return false;
    };
    findParent(processedTreeData, nodeValue);
    return ancestors;
  }, [processedTreeData, valueField, childrenField]);

  // 处理异步加载
  const handleLoadData = useCallback(async (node) => {
    if (!loadData || loadingKeys.includes(node[valueField])) return;

    setLoadingKeys(prev => [...prev, node[valueField]]);

    try {
      await loadData(node);
    } finally {
      setLoadingKeys(prev => prev.filter(key => key !== node[valueField]));
    }
  }, [loadData, loadingKeys, valueField]);

  // 处理打开状态变化
  const handleOpenChange = useCallback((newOpen) => {
    if (!isControlledOpen) {
      setInternalOpen(newOpen);
    }

    if (onOpenChange) {
      onOpenChange(newOpen);
    }

    // 兼容旧版本
    if (onDropdownVisibleChange) {
      onDropdownVisibleChange(newOpen);
    }
  }, [isControlledOpen, onOpenChange, onDropdownVisibleChange]);

  // 处理搜索
  const handleSearch = useCallback((searchValue) => {
    if (!isControlledSearch) {
      setSearchTerm(searchValue);
    }

    if (onSearch) {
      onSearch(searchValue);
    }
  }, [isControlledSearch, onSearch]);

  // 处理值变化
  const handleValueChange = useCallback((newValue, triggerNode, extra = {}) => {
    if (!isControlledValue) {
      setInternalValue(newValue);
    }

    if (onChange) {
      if (labelInValue) {
        const valueWithLabel = Array.isArray(newValue)
          ? newValue.map(val => {
              const node = flattenNodes.find(n => n[valueField] === val);
              return {
                value: val,
                label: node ? node[labelField] : val,
                halfChecked: extra.halfCheckedKeys?.includes(val) || false
              };
            })
          : newValue ? (() => {
              const node = flattenNodes.find(n => n[valueField] === newValue);
              return {
                value: newValue,
                label: node ? node[labelField] : newValue,
                halfChecked: false
              };
            })() : undefined;
        onChange(valueWithLabel, triggerNode, extra);
      } else {
        onChange(newValue, triggerNode, extra);
      }
    }
  }, [isControlledValue, onChange, labelInValue, flattenNodes, valueField, labelField]);

  // 处理节点选择
  const handleNodeSelect = useCallback(async (nodeValue, node, isChecked) => {
    let newValue;
    const currentValues = Array.isArray(currentValue) ? currentValue : (currentValue ? [currentValue] : []);

    if (multiple || treeCheckable) {
      if (isChecked) {
        // 添加选中
        if (treeCheckStrictly) {
          // 严格模式：不关联父子节点
          newValue = [...currentValues, nodeValue];
        } else {
          // 关联模式：处理父子节点联动
          const descendants = getNodeDescendants(node);
          const ancestors = getNodeAncestors(nodeValue);
          newValue = [...new Set([...currentValues, nodeValue, ...descendants])];

          // 检查是否需要选中祖先节点
          ancestors.forEach(ancestorValue => {
            const ancestorNode = flattenNodes.find(n => n[valueField] === ancestorValue);
            if (ancestorNode && ancestorNode[childrenField]) {
              const allChildrenSelected = ancestorNode[childrenField].every(child =>
                newValue.includes(child[valueField])
              );
              if (allChildrenSelected && !newValue.includes(ancestorValue)) {
                newValue.push(ancestorValue);
              }
            }
          });
        }
      } else {
        // 取消选中
        if (treeCheckStrictly) {
          newValue = currentValues.filter(v => v !== nodeValue);
        } else {
          const descendants = getNodeDescendants(node);
          newValue = currentValues.filter(v => v !== nodeValue && !descendants.includes(v));

          // 取消祖先节点选中
          const ancestors = getNodeAncestors(nodeValue);
          newValue = newValue.filter(v => !ancestors.includes(v));
        }
      }

      // 应用 maxCount 限制
      if (maxCount && newValue.length > maxCount) {
        return;
      }
    } else {
      // 单选模式
      newValue = nodeValue;
      handleOpenChange(false);
    }

    handleValueChange(newValue, node, { checked: isChecked });

    if (onSelect) {
      onSelect(nodeValue, node, { selected: isChecked });
    }
  }, [currentValue, multiple, treeCheckable, treeCheckStrictly, maxCount, flattenNodes, valueField, childrenField, handleValueChange, handleOpenChange, onSelect, getNodeDescendants, getNodeAncestors]);

  // 虚拟滚动的节点渲染组件
  const VirtualTreeNode = useCallback(({ index, style }) => {
    const node = visibleFlatNodes[index];
    if (!node) return null;

    const nodeValue = node[valueField];
    const nodeLabel = treeTitleRender ? treeTitleRender(node) : node[labelField];
    const isSelected = Array.isArray(currentValue)
      ? currentValue.includes(nodeValue)
      : currentValue === nodeValue;
    const isLoading = loadingKeys.includes(nodeValue);
    const hasChildren = node[childrenField] && node[childrenField].length > 0;
    const isLeaf = node.isLeaf || (!hasChildren && !loadData);
    const isExpanded = currentExpanded.includes(nodeValue);

    const handleNodeClick = async (e) => {
      e.stopPropagation();

      if (!isLeaf && loadData && !hasChildren) {
        await handleLoadData(node);
      }

      if (treeExpandAction === 'click' || treeExpandAction === 'doubleClick') {
        const newExpanded = isExpanded
          ? currentExpanded.filter(key => key !== nodeValue)
          : [...currentExpanded, nodeValue];

        if (!isControlledExpanded) {
          setExpandedKeys(newExpanded);
        }
        if (onTreeExpand) {
          onTreeExpand(newExpanded);
        }
      }

      handleNodeSelect(nodeValue, node, !isSelected);
    };

    const handleExpandClick = (e) => {
      e.stopPropagation();
      const newExpanded = isExpanded
        ? currentExpanded.filter(key => key !== nodeValue)
        : [...currentExpanded, nodeValue];

      if (!isControlledExpanded) {
        setExpandedKeys(newExpanded);
      }
      if (onTreeExpand) {
        onTreeExpand(newExpanded);
      }
    };

    return (
      <div style={style}>
        <ListItemButton
          className={`tree-node ${isSelected ? 'selected' : ''}`}
          onClick={handleNodeClick}
          sx={{
            pl: node.level * 3 + 1,
            py: 0.5,
            minHeight: virtualItemSize,
            borderRadius: 1,
            mb: 0.5,
            backgroundColor: isSelected
              ? alpha(theme.palette.primary.main, 0.08)
              : 'transparent',
            '&:hover': {
              backgroundColor: isSelected
                ? alpha(theme.palette.primary.main, 0.12)
                : alpha(theme.palette.primary.main, 0.04),
            },
          }}
          disabled={node.disabled}
        >
          {/* 展开/收起图标 */}
          {!isLeaf && (
            <IconButton
              size="small"
              onClick={handleExpandClick}
              sx={{
                mr: 1,
                p: 0.5,
                color: theme.palette.text.secondary,
              }}
            >
              {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
            </IconButton>
          )}

          {/* 占位符，保持对齐 */}
          {isLeaf && (
            <Box sx={{ width: 32, mr: 1 }} />
          )}

          {/* 复选框 */}
          {(multiple || treeCheckable) && (
            <Checkbox
              checked={isSelected}
              size="small"
              onClick={(e) => e.stopPropagation()}
              onChange={(e) => {
                handleNodeSelect(nodeValue, node, e.target.checked);
              }}
              disabled={node.disabled || node.disableCheckbox}
              sx={{ mr: 1, p: 0.5 }}
              icon={<CheckBoxOutlineBlankIcon />}
              checkedIcon={<CheckBoxIcon />}
              indeterminateIcon={<IndeterminateCheckBoxIcon />}
            />
          )}

          {/* 文件夹/文件图标 */}
          {treeIcon && (
            <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
              {typeof treeIcon === 'boolean' ? (
                hasChildren ? (
                  isExpanded ? <FolderOpenIcon color="primary" /> : <FolderIcon color="action" />
                ) : (
                  <FileIcon color="action" />
                )
              ) : (
                treeIcon
              )}
            </Box>
          )}

          {/* 节点标签 */}
          <ListItemText
            primary={nodeLabel}
            primaryTypographyProps={{
              variant: 'body2',
              sx: {
                fontWeight: isSelected ? 500 : 400,
                color: isSelected ? 'primary.main' : 'text.primary',
              },
            }}
          />

          {/* 加载指示器 */}
          {isLoading && (
            <CircularProgress size={16} sx={{ ml: 1 }} />
          )}
        </ListItemButton>
      </div>
    );
  }, [
    visibleFlatNodes, valueField, labelField, childrenField, currentValue, currentExpanded,
    loadingKeys, treeTitleRender, treeCheckable, multiple, treeIcon, treeExpandAction,
    isControlledExpanded, onTreeExpand, handleNodeSelect, handleLoadData, virtualItemSize,
    theme, loadData
  ]);

  // 渲染树节点 - 使用 Material-UI 风格
  const renderTreeNode = useCallback((node, level = 0) => {
    const nodeValue = node[valueField];
    const nodeLabel = treeTitleRender ? treeTitleRender(node) : node[labelField];
    const isSelected = Array.isArray(currentValue)
      ? currentValue.includes(nodeValue)
      : currentValue === nodeValue;
    const isLoading = loadingKeys.includes(nodeValue);
    const hasChildren = node[childrenField] && node[childrenField].length > 0;
    const isLeaf = node.isLeaf || (!hasChildren && !loadData);
    const isExpanded = currentExpanded.includes(nodeValue);

    const handleNodeClick = async (e) => {
      e.stopPropagation();

      if (!isLeaf && loadData && !hasChildren) {
        await handleLoadData(node);
      }

      if (treeExpandAction === 'click' || treeExpandAction === 'doubleClick') {
        const newExpanded = isExpanded
          ? currentExpanded.filter(key => key !== nodeValue)
          : [...currentExpanded, nodeValue];

        if (!isControlledExpanded) {
          setExpandedKeys(newExpanded);
        }
        if (onTreeExpand) {
          onTreeExpand(newExpanded);
        }
      }

      handleNodeSelect(nodeValue, node, !isSelected);
    };

    const handleExpandClick = (e) => {
      e.stopPropagation();
      const newExpanded = isExpanded
        ? currentExpanded.filter(key => key !== nodeValue)
        : [...currentExpanded, nodeValue];

      if (!isControlledExpanded) {
        setExpandedKeys(newExpanded);
      }
      if (onTreeExpand) {
        onTreeExpand(newExpanded);
      }
    };

    return (
      <Box key={nodeValue}>
        <ListItemButton
          className={`tree-node ${isSelected ? 'selected' : ''}`}
          onClick={handleNodeClick}
          sx={{
            pl: level * 3 + 1,
            py: 0.5,
            minHeight: 40,
            borderRadius: 1,
            mb: 0.5,
            backgroundColor: isSelected
              ? alpha(theme.palette.primary.main, 0.08)
              : 'transparent',
            '&:hover': {
              backgroundColor: isSelected
                ? alpha(theme.palette.primary.main, 0.12)
                : alpha(theme.palette.primary.main, 0.04),
            },
          }}
          disabled={node.disabled}
        >
          {/* 展开/收起图标 */}
          {!isLeaf && (
            <IconButton
              size="small"
              onClick={handleExpandClick}
              sx={{
                mr: 1,
                p: 0.5,
                color: theme.palette.text.secondary,
              }}
            >
              {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
            </IconButton>
          )}

          {/* 占位符，保持对齐 */}
          {isLeaf && (
            <Box sx={{ width: 32, mr: 1 }} />
          )}

          {/* 复选框 */}
          {(multiple || treeCheckable) && (
            <Checkbox
              checked={isSelected}
              size="small"
              onClick={(e) => e.stopPropagation()}
              onChange={(e) => {
                handleNodeSelect(nodeValue, node, e.target.checked);
              }}
              disabled={node.disabled || node.disableCheckbox}
              sx={{ mr: 1, p: 0.5 }}
              icon={<CheckBoxOutlineBlankIcon />}
              checkedIcon={<CheckBoxIcon />}
              indeterminateIcon={<IndeterminateCheckBoxIcon />}
            />
          )}

          {/* 文件夹/文件图标 */}
          {treeIcon && (
            <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
              {typeof treeIcon === 'boolean' ? (
                hasChildren ? (
                  isExpanded ? <FolderOpenIcon color="primary" /> : <FolderIcon color="action" />
                ) : (
                  <FileIcon color="action" />
                )
              ) : (
                treeIcon
              )}
            </Box>
          )}

          {/* 节点标签 */}
          <ListItemText
            primary={nodeLabel}
            primaryTypographyProps={{
              variant: 'body2',
              sx: {
                fontWeight: isSelected ? 500 : 400,
                color: isSelected ? 'primary.main' : 'text.primary',
              },
            }}
          />

          {/* 加载指示器 */}
          {isLoading && (
            <CircularProgress size={16} sx={{ ml: 1 }} />
          )}
        </ListItemButton>

        {/* 子节点 */}
        {hasChildren && isExpanded && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <Box>
              {node[childrenField].map(child => renderTreeNode(child, level + 1))}
            </Box>
          </Collapse>
        )}
      </Box>
    );
  }, [
    valueField, labelField, childrenField, currentValue, currentExpanded, loadingKeys,
    treeTitleRender, treeCheckable, multiple, treeIcon, treeExpandAction,
    isControlledExpanded, onTreeExpand, handleNodeSelect, handleLoadData, theme, loadData
  ]);

  // 获取显示文本
  const getDisplayText = useCallback(() => {
    if (!currentValue) return "";

    const values = Array.isArray(currentValue) ? currentValue : [currentValue];
    if (values.length === 0) return "";

    const getNodeLabel = (val) => {
      if (labelInValue && typeof val === 'object') {
        return val.label;
      }
      const node = flattenNodes.find(n => n[valueField] === val);
      return node ? node[treeNodeLabelProp] : val;
    };

    if (!multiple) {
      return getNodeLabel(values[0]);
    }

    // 多选显示逻辑
    if (maxTagCount && values.length > maxTagCount) {
      const displayValues = values.slice(0, maxTagCount);
      const omittedCount = values.length - maxTagCount;
      const displayText = displayValues.map(getNodeLabel).join(", ");

      if (maxTagPlaceholder) {
        const placeholder = typeof maxTagPlaceholder === 'function'
          ? maxTagPlaceholder(values.slice(maxTagCount))
          : maxTagPlaceholder;
        return `${displayText}, ${placeholder}`;
      }

      return `${displayText} +${omittedCount}`;
    }

    return values.map(getNodeLabel).join(", ");
  }, [currentValue, multiple, labelInValue, flattenNodes, valueField, treeNodeLabelProp, maxTagCount, maxTagPlaceholder]);

  return (
    <Box>
      {/* 输入框 */}
      <TextField
        ref={inputRef}
        value={getDisplayText()}
        placeholder={placeholder}
        size={size}
        variant={variant}
        disabled={disabled}
        error={status === 'error'}
        fullWidth
        onClick={(e) => {
          if (!disabled) {
            setAnchorEl(e.currentTarget);
            handleOpenChange(true);
          }
        }}
        InputProps={{
          readOnly: true,
          style: { cursor: disabled ? 'default' : 'pointer' },
          startAdornment: prefix && (
            <InputAdornment position="start">
              {prefix}
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              {allowClear && currentValue && !disabled && (
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleValueChange(multiple ? [] : undefined);
                  }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              )}
              {suffixIcon || <ArrowDropDownIcon />}
            </InputAdornment>
          ),
        }}
        {...restProps}
      />

      {/* 下拉框 */}
      <StyledPopover
        open={currentOpen}
        anchorEl={anchorEl}
        onClose={() => {
          setAnchorEl(null);
          handleOpenChange(false);
        }}
        anchorOrigin={{
          vertical: placement.includes('top') ? 'top' : 'bottom',
          horizontal: placement.includes('Right') ? 'right' : 'left',
        }}
        transformOrigin={{
          vertical: placement.includes('top') ? 'bottom' : 'top',
          horizontal: placement.includes('Right') ? 'right' : 'left',
        }}
        slotProps={{
          paper: {
            style: {
              width: popupMatchSelectWidth ? anchorEl?.offsetWidth : 'auto',
              maxHeight: listHeight + 100,
              ...dropdownStyle,
              ...styles?.popup?.root,
            },
            className: `${popupClassName || ''} ${classNames?.popup?.root || ''}`,
            onScroll: onPopupScroll,
          },
        }}
      >
        <Box>
          {/* 搜索框 */}
          {(showSearch || (multiple && showSearch !== false)) && (
            <Box sx={{ p: 2, pb: 1 }}>
              <TextField
                size="small"
                fullWidth
                placeholder="搜索节点..."
                value={currentSearch}
                onChange={(e) => handleSearch(e.target.value)}
                variant="outlined"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.background.default, 0.5),
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.background.default, 0.8),
                    },
                    '&.Mui-focused': {
                      backgroundColor: theme.palette.background.paper,
                    },
                  },
                }}
              />
            </Box>
          )}

          <Divider />

          {/* 树形结构 */}
          <Box
            sx={{
              height: listHeight,
              overflow: 'hidden',
            }}
          >
            {visibleFlatNodes.length > 0 ? (
              <StyledTreeContainer>
                {shouldUseVirtual ? (
                  // 虚拟滚动模式
                  <VirtualList
                    height={listHeight}
                    itemCount={visibleFlatNodes.length}
                    itemSize={virtualItemSize}
                    width="100%"
                    overscanCount={5}
                  >
                    {VirtualTreeNode}
                  </VirtualList>
                ) : (
                  // 普通滚动模式
                  <Box sx={{ maxHeight: listHeight, overflow: 'auto' }}>
                    <List dense disablePadding>
                      {visibleFlatNodes.map((node) => (
                        <div key={node.nodeId}>
                          {renderTreeNode(node, node.level)}
                        </div>
                      ))}
                    </List>
                  </Box>
                )}
              </StyledTreeContainer>
            ) : (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  {notFoundContent}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </StyledPopover>

      {/* 状态提示 */}
      {status === 'warning' && (
        <FormHelperText sx={{ color: 'warning.main' }}>
          警告状态
        </FormHelperText>
      )}
    </Box>
  );
};

// 静态属性
TreeSelect.SHOW_ALL = SHOW_ALL;
TreeSelect.SHOW_PARENT = SHOW_PARENT;
TreeSelect.SHOW_CHILD = SHOW_CHILD;

export default TreeSelect;
