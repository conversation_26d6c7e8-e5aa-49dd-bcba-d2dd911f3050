/**
 *  对象判空
 * <AUTHOR>
 * @date 2022-12-19 10:04
 */
export const isEmptyObj = (obj) => {
    return Object.keys(obj).length === 0;
};
/**
 *  删除空属性
 * <AUTHOR>
 * @date 2023-06-01 13：46
 */
export const removeEmpty = (obj) => {
    for (const [key, value] of Object.entries(obj)) {
        if (value === null || value === '' || value === undefined) {
            Reflect.deleteProperty(obj, key);
        }
    }
};
