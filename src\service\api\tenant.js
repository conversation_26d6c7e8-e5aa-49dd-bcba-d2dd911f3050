import request from "@/utils/request";

const baseProfixURI = `${import.meta.env.VITE_APICODE}/tenant`;

export const getTenantList = (params) => {
  return request({
    url: `${baseProfixURI}/query/page`,
    method: "get",
    params: params,
  });
};

export const savetenant = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "post",
    data: params,
  });
};

export const deltenant = (id) => {
  return request({
    url: `${baseProfixURI}/` + id,
    method: "delete",
  });
};

/**
 *  修改租户
 * <AUTHOR>
 * @date 2023-04-03 15:15
 */
export const updatetenant = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "put",
    data: params,
  });
};

/**
 *
 * @returns 获取租户详情
 */

export const getTenantDetail = (id) => {
  return request({
    url: `${baseProfixURI}/query/` + id,
    method: "GET",
  });
};

/**
 * 切换租户接口
 */

export const changeTenantList = (params) => {
  return request({
    url: `${baseProfixURI}/query/list`,
    method: "get",
    params: params,
  });
};

/**
 * 租户用户列表
 */

export const getTenantUserList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/branch_employee/query/page`,
    method: "get",
    params: params,
  });
};

/**
 * 新增租户用户
 */

export const addTenantUser = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/branch_employee`,
    method: "POST",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

/**
 * 修改租户用户
 */

export const editTenantUser = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/branch_employee`,
    method: "PUT",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

/**
 *
 * @returns 获取租户用户详情
 */

export const getTenantUserDetail = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/branch_employee/query/` + id,
    method: "GET",
  });
};

/**
 * 删除租户用户
 */

export const deleteTenantUser = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/branch_employee/` + id,
    method: "DELETE",
  });
};
