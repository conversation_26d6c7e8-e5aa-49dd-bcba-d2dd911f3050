import React from "react";
import { useTranslation } from "react-i18next";
import { Button, Grid } from "@mui/material";
import UploadProfile from "@/assets/Icons/UploadProfile.svg";
import { pxToRem } from "@/utils/zkUtils.js";
import { toast } from "react-toastify";
function CustomUpload(props) {
  const { t } = useTranslation();
  const { imageUrl, handleUpload, setImageUrl } = props;

  const fileInputRef = useRef();

  const handleAvatarClick = () => {
    fileInputRef.current.click(); // 触发隐藏的文件输入框
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (allowedTypes.includes(file.type)) {
        if (file.size <= maxSize) {
          handleUpload(file);
        } else {
          toast.error(t("File size exceeds 5MB limit"));
        }
      } else {
        // 显示错误消息
        toast.error(t("common.file_type_error"));
      }
    }
  };

  // Handle removing the uploaded image
  const handleRemoveImage = () => {
    setImageUrl(null); // Clear the image URL
    fileInputRef.current.value = ""; // Reset file input value
  };

  return (
    <React.Fragment>
      <Grid container>
        <Grid item>
          <Avatar
            className="avatar radial-button"
            alt="加载失败"
            sx={{ width: pxToRem(140), height: pxToRem(140) }}
            src={imageUrl || UploadProfile}></Avatar>

          <input
            type="file"
            accept=".jpg,.png,.jpeg"
            style={{ display: "none" }} // 隐藏文件输入框
            ref={fileInputRef} // 绑定引用
            onChange={handleFileChange} // 文件变化时触发
          />
        </Grid>

        <Grid
          item
          sx={{
            height: pxToRem(40),
            ml: 5,
            mt: 3,
          }}>
          <Typography
            sx={{
              font: `normal normal normal 12px/14px Proxima Nova`,
              color: "#474B4F",
              opacity: 0.5,
            }}>
            {t("common.common_allowed")}
          </Typography>
          <Typography
            sx={{
              font: `normal normal normal 12px/14px Proxima Nova`,
              color: "#474B4F",
              opacity: 0.5,
              mt: 2,
            }}>
            {t("common.common_maximum")}
          </Typography>

          <Grid container mt={4}>
            <Button
              sx={{
                width: pxToRem(100),
                height: pxToRem(36),
                borderRadius: "5px",
                color: "#FFFFFF",
                fontSize: "14px",
                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
              }}
              onClick={handleAvatarClick}>
              {t("common.common_upload")}
            </Button>
            <Button
              sx={{
                width: pxToRem(100),
                height: pxToRem(36),
                border: "1px solid #E3E3E3",
                borderRadius: "5px",
                fontSize: "14px",
                color: "#000",
                ml: 2,
              }}
              onClick={handleRemoveImage}>
              {t("common.common_remove")}
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default CustomUpload;
