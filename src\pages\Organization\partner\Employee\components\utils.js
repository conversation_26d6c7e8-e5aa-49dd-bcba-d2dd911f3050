import { getParnerDetailUser } from "@s/api/partner";
export const getBranchDetail = (
  id,
  setDetailData,
  setImageUrl,
  setSelectRole
) => {
  getParnerDetailUser(id).then((res) => {
    if (res?.code == "00000000") {
      let data = res?.data;
      setImageUrl(data?.photo);
      setDetailData(data);
      setSelectRole(data?.roles || []);
    }
  });
};

export const getFormConfig = (t, type, permissionList) => {
  let formConfig = [
    {
      name: "firstName",
      label: t("partner_user.firstName"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner_user.firstName_required"),
        },
      ],
    },
    {
      name: "lastName",
      label: t("partner_user.lastName"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner_user.lastName_required"),
        },
      ],
    },

    {
      name: "email",
      label: t("partner_user.email"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner_user.email_required"),
        },
        {
          type: "email",
          message: t("邮箱格式有误"),
        },
      ],
    },
    {
      codename: "countryCode", // 对应区号字段名
      name: "phone", // 对应电话号码字段名
      label: t("partner_user.phone"),
      type: "mobile",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner_user.mobile_required"),
        },
        {
          type: "matches",
          matches: /^\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}$/,
          message: t("号码格式有误"),
        },
      ],
    },

    {
      name: "password",
      label: t("partner_user.password"),
      type: "password",
      viewPwd: true,
      required: type !== "editor" ? true : false,
      display: type !== "editor" ? true : false,
      validation:
        type !== "editor"
          ? [
            {
              type: "string",
              message: "",
            },
            {
              type: "required",
              message: t("partner_user.password_required"),
            },
            {
              type: "matches",
              matches: /^(?=(?:.*[A-Z]){1})(?=(?:.*[a-z]){1})(?=(?:.*\d){1})(?=(?:.*[^\w\s]){1})(?!.*[\s\u{1F300}-\u{1F9FF}\u{2600}-\u{27BF}\u{1F1E6}-\u{1F1FF}]).{8,64}$/u,
              message: t("common.common_confirm_password_format"),
            },
          ]
          : null,
    },

    {
      name: "confirmPassword",
      label: t("partner_user.confirmPassword"),
      type: "password",
      viewPwd: true,
      display: type !== "editor" ? true : false,
      required: type !== "editor" ? true : false,
      validation:
        type !== "editor"
          ? [
            {
              type: "string",
              message: "",
            },
            {
              type: "required",
              message: t("partner_user.confirmPassword_required"),
            },
            {
              type: "secondConfirm",
              ref: "password",
              message: t("partner_user.password_mismatch"),
            },
          ]
          : null,
    },

    {
      name: "dataScopeId",
      label: t("partner_user.dataScopeId"),
      type: "autoComplate",
      options: permissionList,
      typevalue: 5,
      placeholder: t("partner_user.followAuthorizationLevel"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner_user.principal_required"),
        },
      ],
    },
  ];

  return formConfig;
};
