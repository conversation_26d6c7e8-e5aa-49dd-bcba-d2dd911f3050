import request from "@/utils/request";

const baseProfixURI = `${import.meta.env.VITE_APICODE}/area`;

/**
 *  查询区域树形列表
 * <AUTHOR>
 * @date 2023-04-03 14:55
 */
export const treeList = (params) => {
  return request({
    url: `${baseProfixURI}/tree`,
    method: "get",
    params: params,
  });
};

/**
 *  保存区域
 * <AUTHOR>
 * @date 2023-04-03 14:56
 */
export const save = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "post",
    data: params,
  });
};

/**
 *  查询区域下拉树
 * <AUTHOR>
 * @date 2023-04-03 14:56
 */
export const getTreeSelect = (params) => {
  return request({
    url: `${baseProfixURI}/tree/select`,
    method: "get",
    params: params,
  });
};

export const getTreeSelectByMerchant = (merhcnatId) => {
  return request({
    url: `${baseProfixURI}/tree/select/${merhcnatId}`,
    method: "get",
  });
};

/**
 *  删除区域
 * <AUTHOR>
 * @param id 区域ID
 * @date 2023-04-03 15:05
 */
export const del = (id) => {
  return request({
    url: `${baseProfixURI}/` + id,
    method: "delete",
  });
};

/**
 *  通过ID查询区域信息
 * <AUTHOR>
 * @date 2023-04-03 15:10
 */
export const getById = (id) => {
  return request({
    url: `${baseProfixURI}/query/` + id,
    method: "get",
  });
};

/**
 *  修改区域
 * <AUTHOR>
 * @date 2023-04-03 15:15
 */
export const update = (params) => {
  return request({
    url: `${baseProfixURI}`,
    method: "put",
    data: params,
  });
};

/**
 *  找出自己以及下级的区域集合
 * @param 创建门店参数
 * <AUTHOR>
 * @date 2023-04-07 16:10
 */
export const listAreaByUserTree = (data) => {
  return request({
    url: `${baseProfixURI}/listAreaByUserTree/` + data,
    method: "post",
  });
};

/**
 * 获取所有区域列表集合
 * <AUTHOR>
 */
export const listAllArea = () => {
  return request({
    url: `${baseProfixURI}/listAllArea`,
    method: "post",
  });
};

/**
 *  获取当前用户的区域
 * <AUTHOR>
 */
export const getCurrentArea = () => {
  return request({
    url: `${baseProfixURI}/getCurrentArea`,
    method: "get",
  });
};

/**
 *  获取当前区域的加速信息
 */
export const getAreaAccelerate = (areaId) => {
  return request({
    url: `${baseProfixURI}/area-accelerate`,
    method: "get",
    params: { areaId },
  });
};

/**
 *  获取当前区域的加速信息
 */
export const updateAreaAccelerate = (data) => {
  return request({
    url: `${baseProfixURI}/area-accelerate`,
    method: "put",
    data,
  });
};
