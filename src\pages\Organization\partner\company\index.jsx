import React from "react";
import TableList from "./components/TableList";
import LayoutList from "@l/components/LayoutList";
import CustomDelete from "@c/Toast/CustomDelete";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import { getParnerList, deleteParner } from "@s/api/partner";
import { useTableRequest } from "../../utils.js";
const index = () => {
  const { t } = useTranslation();

  const [open, setOpen] = useState(false); // 删除Branch 弹窗
  const [tenantId, setTenantId] = useState(null);
  const [serchName, setSeachName] = useState("");

  const {
    data,
    isLoading,
    isRefetching,
    isError,
    rowCount,
    pagination,
    setPagination,
    fetchData,
    search,
    reset,
  } = useTableRequest(getParnerList);

  // 删除
  const handlerDetele = async () => {
    try {
      const res = await deleteParner(tenantId);
      toast.success(res?.message);
      fetchData();
    } catch {
      toast.error(t("common.deleteFailed"));
    } finally {
      setOpen(false);
    }
  };

  // 搜索
  const handlerSeacher = () => {
    search({ name: serchName });
  };

  // 清空
  const handleClear = () => {
    setSeachName("");
    reset();
  };

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
        setTenantId={setTenantId}
        setOpen={setOpen}
        getTableData={fetchData}></TableList>
    );
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("partner.title")}
        isSearch={true}
        onClick={handlerSeacher}
        serchName={serchName}
        setSeachName={setSeachName}
        onClear={handleClear} //
        content={rederTable()}></LayoutList>

      <CustomDelete
        open={open}
        setOpen={setOpen}
        handlerDetele={handlerDetele}
        content={t("partner.delete_content")}
        onContent={t("partner.delete_content")}
        noDelete={false}></CustomDelete>
    </React.Fragment>
  );
};

export default index;
