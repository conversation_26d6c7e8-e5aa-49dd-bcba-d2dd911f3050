/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react/prop-types */
import React from "react";
import DescriptionsItem from "./DescriptionsItem";
import Row from "./Row";
import "./style/index.less";

export * from "./DescriptionsItem";

const generateChildrenRows = (children, column) => {
  const rows = [];
  let columns = null;
  let leftSpans;

  children.forEach((node, index) => {
    let itemNode = node;

    if (!columns) {
      leftSpans = column;
      columns = [];
      rows.push(columns);
    }

    // Always set last span to align the end of Descriptions
    const lastItem = index === children.length - 1;
    if (lastItem) {
      itemNode = React.cloneElement(itemNode, {
        span: leftSpans,
      });
    }
    // Calculate left fill span
    const { span = 1 } = itemNode.props;
    columns.push(itemNode);
    leftSpans -= span;

    if (leftSpans <= 0) {
      columns = null;
    }
  });

  return rows;
};

function InternalDescriptions(props, ref) {
  const {
    prefixCls = "w-descriptions",
    className,
    title,
    bordered,
    column = 3,
    size,
    colon = true,
    children,
    layout = "horizontal",
    ...other
  } = props;
  const cls = [
    prefixCls,
    className,
    prefixCls && layout ? `${prefixCls}-${layout}` : null,
    bordered ? `${prefixCls}-bordered` : null,
    size ? `${prefixCls}-${size}` : null,
  ]
    .filter(Boolean)
    .join(" ")
    .trim();

  const cloneChildren = React.Children.toArray(children);
  const childs = generateChildrenRows(cloneChildren, column);

  return (
    <div className={cls} ref={ref}>
      <table {...other}>
        {title && <caption className={`${prefixCls}-title`}>{title}</caption>}
        <tbody className={`${prefixCls}-tbody`}>
          {childs.map((child, index) => (
            <Row
              key={index}
              prefixCls={prefixCls}
              bordered={bordered}
              colon={colon}
              column={column}
              layout={layout}
            >
              {child}
            </Row>
          ))}
        </tbody>
      </table>
    </div>
  );
}

const Descriptions = React.forwardRef(InternalDescriptions);

Descriptions.Item = DescriptionsItem;

export default Descriptions;
