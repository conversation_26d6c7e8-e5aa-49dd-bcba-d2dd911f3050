import request from "@/utils/request";

const baseProfixURI = "/v1/outlet_type";

// 商品列表
export const getProductList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/product_type/query/page`,
    method: "get",
    params: params,
  });
};

// 头部 查询接口
export const queryProduct = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/product_type`,
    method: "get",
    data: params,
  });
};

// 批量删除
export const deleteProduct = (ids) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/product_type/${ids}`,
    method: "delete",
  });
};

//   新增
export const saveProduct = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/product_type`,
    method: "post",
    data: params,
  });
};
