import React, { forwardRef } from "react";
import PropTypes from "prop-types";
import Dot from "@/components/@extended/Dot";
import { Stack, Typography } from "@mui/material";

/**
 * 字典翻译组件
 * @param {Object} props - 组件属性
 * @param {Array} props.dicts - 字典数据数组
 * @param {string} [props.type="dot"] - 显示类型，可选值："dot" 或 "text"
 * @param {string} props.value - 当前值
 * @param {Object} [props.fieldName] - 字段名映射
 * @param {string} [props.defaultValue="-"] - 默认值
 * @returns {React.ReactElement}
 */
const DictTag = forwardRef(
  (
    {
      dicts = [],
      type = "dot",
      value,
      fieldName = {
        value: "value",
        title: "title",
        listClass: "listClass",
      },
      defaultValue = "-",
    },
    ref
  ) => {
    const getColor = (listClass) => {
      const colorMap = {
        success: "success",
        processing: "primary",
        error: "error",
        warning: "warning",
      };
      return colorMap[listClass] || "secondary";
    };

    const findMatchingDict = () => {
      return dicts.find(
        (dict) => dict[fieldName.value]?.toString() == value?.toString()
      );
    };

    const renderContent = () => {
      const matchingDict = findMatchingDict();

      if (!matchingDict) {
        return defaultValue !== "" ? (
          <Typography>{defaultValue}</Typography>
        ) : null;
      }

      if (type === "text") {
        return <Typography>{matchingDict[fieldName.title]}</Typography>;
      }

      return (
        <Stack direction="row" spacing={1} alignItems="center">
          {matchingDict[fieldName.listClass] && (
            <Dot color={getColor(matchingDict[fieldName.listClass])} />
          )}
          <Typography>{matchingDict[fieldName.title]}</Typography>
        </Stack>
      );
    };

    return <>{renderContent()}</>;
  }
);

DictTag.propTypes = {
  dicts: PropTypes.array,
  type: PropTypes.oneOf(["dot", "text"]),
  value: PropTypes.string,
  fieldName: PropTypes.shape({
    value: PropTypes.string,
    title: PropTypes.string,
    listClass: PropTypes.string,
  }),
  defaultValue: PropTypes.string,
};

export default DictTag;
