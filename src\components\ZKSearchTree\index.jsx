import React, { forwardRef, useState, useEffect } from "react";
import TreeSelect from "rc-tree-select";
import NoContentIcon from "@/assets/Icons/NoContent.svg?react";
import "./index.css";
import { Data } from "./data";

const { TreeNode } = TreeSelect;
const ZkSearchTree = forwardRef((props) => {
  const {
    value,
    onChange = () => {},
    treeData = Data,
    placeholder = "请选择",
    allowClear = false,
    multiple = false,
    treeDefaultExpandAll = true,
  } = props;

  const [dropdownVisible, setDropdownVisible] = useState(false);

  const handleChange = (newValue, label, extra) => {
    if (onChange) {
      onChange(newValue, label, extra);
    }
    if (!multiple) {
      setDropdownVisible(false);
    }
  };

  const renderTreeNodes = (data) =>
    data.map((item) => {
      if (item.children) {
        return (
          <TreeNode title={item.title} key={item.key} value={item.value}>
            {renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode {...item} />;
    });
  return (
    <div className="zk-tree-select-container">
      <TreeSelect
        showArrow={true}
        showSearch={true}
        allowClear={allowClear}
        open={dropdownVisible}
        treeLine={true} // ✅ 开启树形折叠箭头图标
        multiple={multiple}
        value={value}
        onChange={handleChange}
        treeData={treeData}
        onDropdownVisibleChange={(visible) => setDropdownVisible(visible)}
        treeDefaultExpandAll={treeDefaultExpandAll}
        placeholder={placeholder}
        treeNodeFilterProp="title"
        className="custom-tree-select"
        treeIcon={true}
        // suffixIcon={<NoContentIcon />}
        // onPopupScroll={onPopupScroll}
        dropdownStyle={{
          maxHeight: 400,
          overflow: "auto",
          minWidth: 300,
          zIndex: 1500,
          borderRadius: "4px",
        }}
        notFoundContent={
          <div className="no-match-result">
            <span className="no-match-icon">
              <NoContentIcon></NoContentIcon>
            </span>
            <span className="no-match-text">无匹配结果</span>
          </div>
        }></TreeSelect>
    </div>
  );
});

export default ZkSearchTree;
