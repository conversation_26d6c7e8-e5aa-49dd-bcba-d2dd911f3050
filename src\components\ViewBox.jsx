// 文件名: ViewBox.jsx
import React from "react";
import { Grid, Typography, Divider } from "@mui/material";

function ViewBox({ title, content }) {
  const titleStyle = {
    font: `normal normal normal 14px/18px Proxima Nova`,
    color: "#474B4F",
    opacity: 0.5,
  };

  const textStyle = {
    fontSize: "16px",
    color: "#474B4F",
    marginTop: "10px",
  };

  return (
    <Grid item>
      <Typography style={titleStyle}>{title}</Typography>
      <Typography style={textStyle}>{content || "-"}</Typography>
      <Divider
        orientation="horizontal"
        style={{
          marginBottom: "20px",
          width: "200px",
          marginTop: "20px",
        }}
      />
    </Grid>
  );
}

export default ViewBox;
