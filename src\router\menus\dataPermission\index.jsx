// 看板
const dataPermissionRoute = [
  {
    path: "/data/permission",
    component: () => import("@p/dataPermission/index"),
    meta: {
      title: "Data Permission",
      i18n: "data_permission",
      id: "3164898081434351577346",
    },
  },

  {
    path: "/add/data/permission",
    component: () => import("@/pages/dataPermission/AddDataPermission"),
    meta: {
      title: "Add Data Permission",
      i18n: "add_data_permission",
      id: "3164454544d34351577346",
    },
  },
];
export default dataPermissionRoute;
