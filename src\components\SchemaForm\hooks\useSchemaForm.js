import { useState, useCallback, useEffect, useMemo } from 'react';
import { setNestedValue, getNestedValue, generateDefaultValues } from '../utils/schemaUtils';

/**
 * Schema表单状态管理Hook
 * @param {Object} options 配置选项
 * @param {Object} options.schema Schema配置
 * @param {Object} options.initialData 初始数据
 * @param {Object} options.validationRules 验证规则
 * @param {Function} options.onChange 数据变化回调
 * @param {Function} options.onValidate 验证回调
 */
export const useSchemaForm = ({
  schema,
  initialData = {},
  validationRules = {},
  onChange,
  onValidate
}) => {
  // 生成默认值
  const defaultValues = useMemo(() => {
    return generateDefaultValues(schema);
  }, [schema]);

  // 合并初始数据和默认值
  const initialFormData = useMemo(() => {
    return { ...defaultValues, ...initialData };
  }, [defaultValues, initialData]);

  // 表单数据状态
  const [data, setData] = useState(initialFormData);
  
  // 错误状态
  const [errors, setErrors] = useState({});
  
  // 触摸状态（用于控制错误显示时机）
  const [touched, setTouched] = useState({});
  
  // 验证状态
  const [isValidating, setIsValidating] = useState(false);

  // 计算表单是否有效
  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0;
  }, [errors]);

  // 更新字段值
  const updateField = useCallback((path, value) => {
    setData(prevData => {
      const newData = setNestedValue(prevData, path, value);
      
      // 触发onChange回调
      onChange?.(newData);
      
      return newData;
    });
    
    // 标记字段为已触摸
    setTouched(prevTouched => 
      setNestedValue(prevTouched, path, true)
    );
  }, [onChange]);

  // 验证单个字段
  const validateField = useCallback((path) => {
    const fieldValue = getNestedValue(data, path);
    const fieldSchema = getFieldSchema(schema, path);
    
    if (!fieldSchema) return;

    const fieldErrors = [];
    
    // 必填验证
    if (fieldSchema.required && (fieldValue === undefined || fieldValue === null || fieldValue === '')) {
      fieldErrors.push(`${fieldSchema.title || path} is required`);
    }
    
    // 类型验证
    if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
      switch (fieldSchema.type) {
        case 'string':
          if (typeof fieldValue !== 'string') {
            fieldErrors.push(`${fieldSchema.title || path} must be a string`);
          } else {
            // 长度验证
            if (fieldSchema.minLength && fieldValue.length < fieldSchema.minLength) {
              fieldErrors.push(`${fieldSchema.title || path} must be at least ${fieldSchema.minLength} characters`);
            }
            if (fieldSchema.maxLength && fieldValue.length > fieldSchema.maxLength) {
              fieldErrors.push(`${fieldSchema.title || path} must be no more than ${fieldSchema.maxLength} characters`);
            }
            
            // 模式验证
            if (fieldSchema.pattern) {
              const regex = new RegExp(fieldSchema.pattern);
              if (!regex.test(fieldValue)) {
                fieldErrors.push(`${fieldSchema.title || path} format is invalid`);
              }
            }
            
            // 格式验证
            if (fieldSchema.format) {
              const formatError = validateFormat(fieldValue, fieldSchema.format, fieldSchema.title || path);
              if (formatError) {
                fieldErrors.push(formatError);
              }
            }
          }
          break;
          
        case 'number':
        case 'integer':
          const numValue = Number(fieldValue);
          if (isNaN(numValue)) {
            fieldErrors.push(`${fieldSchema.title || path} must be a number`);
          } else {
            if (fieldSchema.type === 'integer' && !Number.isInteger(numValue)) {
              fieldErrors.push(`${fieldSchema.title || path} must be an integer`);
            }
            if (fieldSchema.minimum !== undefined && numValue < fieldSchema.minimum) {
              fieldErrors.push(`${fieldSchema.title || path} must be at least ${fieldSchema.minimum}`);
            }
            if (fieldSchema.maximum !== undefined && numValue > fieldSchema.maximum) {
              fieldErrors.push(`${fieldSchema.title || path} must be no more than ${fieldSchema.maximum}`);
            }
          }
          break;
          
        case 'boolean':
          if (typeof fieldValue !== 'boolean') {
            fieldErrors.push(`${fieldSchema.title || path} must be a boolean`);
          }
          break;
          
        case 'array':
          if (!Array.isArray(fieldValue)) {
            fieldErrors.push(`${fieldSchema.title || path} must be an array`);
          } else {
            if (fieldSchema.minItems && fieldValue.length < fieldSchema.minItems) {
              fieldErrors.push(`${fieldSchema.title || path} must have at least ${fieldSchema.minItems} items`);
            }
            if (fieldSchema.maxItems && fieldValue.length > fieldSchema.maxItems) {
              fieldErrors.push(`${fieldSchema.title || path} must have no more than ${fieldSchema.maxItems} items`);
            }
          }
          break;
      }
    }
    
    // 自定义验证规则
    if (validationRules[path]) {
      const customError = validationRules[path](fieldValue, data, fieldSchema);
      if (customError) {
        fieldErrors.push(customError);
      }
    }
    
    // 更新错误状态
    setErrors(prevErrors => {
      const newErrors = { ...prevErrors };
      if (fieldErrors.length > 0) {
        newErrors[path] = fieldErrors[0]; // 只显示第一个错误
      } else {
        delete newErrors[path];
      }
      return newErrors;
    });
    
    return fieldErrors.length === 0;
  }, [data, schema, validationRules]);

  // 验证整个表单
  const validateForm = useCallback(() => {
    setIsValidating(true);
    
    const allPaths = getAllFieldPaths(schema);
    const validationPromises = allPaths.map(path => validateField(path));
    
    Promise.all(validationPromises).then(results => {
      const isFormValid = results.every(result => result);
      setIsValidating(false);
      
      // 触发验证回调
      onValidate?.(errors, isFormValid);
      
      return isFormValid;
    });
  }, [schema, validateField, errors, onValidate]);

  // 重置表单
  const resetForm = useCallback(() => {
    setData(initialFormData);
    setErrors({});
    setTouched({});
  }, [initialFormData]);

  // 设置字段错误
  const setFieldError = useCallback((path, error) => {
    setErrors(prevErrors => {
      const newErrors = { ...prevErrors };
      if (error) {
        newErrors[path] = error;
      } else {
        delete newErrors[path];
      }
      return newErrors;
    });
  }, []);

  // 当初始数据变化时更新表单数据
  useEffect(() => {
    if (initialData !== data) {
      setData({ ...defaultValues, ...initialData });
    }
  }, [initialData, defaultValues]);

  return {
    data,
    errors,
    touched,
    isValid,
    isValidating,
    updateField,
    validateField,
    validateForm,
    resetForm,
    setFieldError
  };
};

/**
 * 获取字段Schema
 * @param {Object} schema 完整Schema
 * @param {string} path 字段路径
 * @returns {Object|null}
 */
const getFieldSchema = (schema, path) => {
  if (!schema || !path) return null;
  
  const keys = path.split('.');
  let current = schema;
  
  for (const key of keys) {
    if (current.properties && current.properties[key]) {
      current = current.properties[key];
    } else if (current.fields) {
      current = current.fields.find(field => field.name === key);
      if (!current) return null;
    } else {
      return null;
    }
  }
  
  return current;
};

/**
 * 获取所有字段路径
 * @param {Object} schema Schema配置
 * @param {string} basePath 基础路径
 * @returns {Array<string>}
 */
const getAllFieldPaths = (schema, basePath = '') => {
  const paths = [];
  
  if (!schema || typeof schema !== 'object') return paths;
  
  // 处理properties
  if (schema.properties) {
    Object.keys(schema.properties).forEach(key => {
      const fieldPath = basePath ? `${basePath}.${key}` : key;
      const fieldSchema = schema.properties[key];
      
      if (fieldSchema.type === 'object' && fieldSchema.properties) {
        paths.push(...getAllFieldPaths(fieldSchema, fieldPath));
      } else {
        paths.push(fieldPath);
      }
    });
  }
  
  // 处理fields
  if (schema.fields) {
    schema.fields.forEach(field => {
      if (field.name) {
        const fieldPath = basePath ? `${basePath}.${field.name}` : field.name;
        
        if (field.type === 'object' && field.properties) {
          paths.push(...getAllFieldPaths(field, fieldPath));
        } else {
          paths.push(fieldPath);
        }
      }
    });
  }
  
  return paths;
};

/**
 * 验证格式
 * @param {string} value 值
 * @param {string} format 格式
 * @param {string} fieldName 字段名
 * @returns {string|null}
 */
const validateFormat = (value, format, fieldName) => {
  const formats = {
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: `${fieldName} must be a valid email address`
    },
    url: {
      pattern: /^https?:\/\/.+/,
      message: `${fieldName} must be a valid URL`
    },
    tel: {
      pattern: /^[\d\s\-\+\(\)]+$/,
      message: `${fieldName} must be a valid phone number`
    }
  };
  
  const formatRule = formats[format];
  if (formatRule && !formatRule.pattern.test(value)) {
    return formatRule.message;
  }
  
  return null;
};
