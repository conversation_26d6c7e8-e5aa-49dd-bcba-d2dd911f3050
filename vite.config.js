// vite.config.ts
import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import AutoImport from "unplugin-auto-import/vite";
import svgr from "vite-plugin-svgr";
import { setupUnPluginIcon } from "./build/icon.js";
import { visualizer } from "rollup-plugin-visualizer";
import ViteRestart from "vite-plugin-restart";
import { createHtmlPlugin } from "vite-plugin-html";
import { VitePWA } from "vite-plugin-pwa";
import tailwindcss from "tailwindcss";
import autoprefixer from "autoprefixer";
let localEnv;
try {
  localEnv = (await import("./local-env.js")).default;
} catch (error) {
  /* empty */
}

export default defineConfig((configEnv) => {
  const viteEnv = loadEnv(configEnv.mode, process.cwd());

  return {
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
        "@c": resolve(__dirname, "./src/components"),
        "@p": resolve(__dirname, "./src/pages"),
        "@l": resolve(__dirname, "./src/layout"),
        "@r": resolve(__dirname, "./src/router"),
        "@s": resolve(__dirname, "./src/service"),
        "@u": resolve(__dirname, "./src/utils"),
        "@a": resolve(__dirname, "./src/assets"),
      },
    },
    base: "/",
    define: {
      process: {
        argv: "[]", // 定义 process.argv 为一个空数组
      }, // 确保 process 是一个空对象
      "process.env": JSON.stringify(process.env), // 将 process.env 替换为实际值
      global: {}, // 确保 global 是一个空对象（如果需要）
      //"console.info": "() => {}",
    },
    css: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
    },
    plugins: [
      react(),
      setupUnPluginIcon(viteEnv),

      // 打包体积分析
      visualizer({
        open: true,
        filename: "visualizer.html", //分析图生成的文件名
      }),

      createHtmlPlugin({
        inject: {
          data: {
            title: viteEnv.VITE_GLOB_APP_TITLE,
          },
        },
      }),
      // 自动重启Vite 服务
      ViteRestart({
        restart: ["vite.config.[jt]s"],
      }),
      svgr({
        svgrOptions: {},
      }),

      AutoImport({
        // targets to transform
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
          /\.md$/, // .md
        ],

        // global imports to register
        imports: [
          "react",
          "react-router-dom",
          "react-i18next",
          {
            "@mui/material": [
              "Accordion",
              "AccordionActions",
              "AccordionDetails",
              "AccordionSummary",
              "Alert",
              "AlertTitle",
              "AppBar",
              "Autocomplete",
              "Avatar",
              "AvatarGroup",
              "Backdrop",
              "Badge",
              "BottomNavigation",
              "BottomNavigationAction",
              "Box",
              "Breadcrumbs",
              "Button",
              "ButtonBase",
              "ButtonGroup",
              "Card",
              "CardActionArea",
              "CardActions",
              "CardContent",
              "CardHeader",
              "CardMedia",
              "Checkbox",
              "Chip",
              "CircularProgress",
              "ClickAwayListener",
              "Collapse",
              "Container",
              "CssBaseline",
              "darkScrollbar",
              "Dialog",
              "DialogActions",
              "DialogContent",
              "DialogContentText",
              "DialogTitle",
              "Divider",
              "Drawer",
              "Fab",
              "Fade",
              "FilledInput",
              "FormControl",
              "FormControlLabel",
              "FormGroup",
              "FormHelperText",
              "FormLabel",
              "generateUtilityClass",
              "generateUtilityClasses",
              "GlobalStyles",
              "Grid",
              "Grow",
              "Hidden",
              "Icon",
              "IconButton",
              "ImageList",
              "ImageListItem",
              "ImageListItemBar",
              "Input",
              "InputAdornment",
              "InputBase",
              "InputLabel",
              "LinearProgress",
              "List",
              "ListItem",
              "ListItemAvatar",
              "ListItemButton",
              "ListItemIcon",
              "ListItemSecondaryAction",
              "ListItemText",
              "ListSubheader",
              "Menu",
              "MenuItem",
              "MenuList",
              "MobileStepper",
              "Modal",
              "NativeSelect",
              "NoSsr",
              "OutlinedInput",
              "Pagination",
              "PaginationItem",
              "Paper",
              "Popover",
              "Popper",
              "Portal",
              "Radio",
              "RadioGroup",
              "Rating",
              "ScopedCssBaseline",
              "Select",
              "Skeleton",
              "Slide",
              "Slider",
              "Snackbar",
              "SnackbarContent",
              "SpeedDial",
              "SpeedDialAction",
              "SpeedDialIcon",
              "Stack",
              "Step",
              "StepButton",
              "StepConnector",
              "StepContent",
              "StepIcon",
              "StepLabel",
              "Stepper",
              "SvgIcon",
              "SwipeableDrawer",
              "Switch",
              "Tab",
              "Table",
              "TableBody",
              "TableCell",
              "TableContainer",
              "TableFooter",
              "TableHead",
              "TablePagination",
              "TableRow",
              "TableSortLabel",
              "Tabs",
              "TabScrollButton",
              "TextareaAutosize",
              "TextField",
              "ToggleButton",
              "ToggleButtonGroup",
              "Toolbar",
              "Tooltip",
              "Typography",
              "Unstable_Grid2",
              "useAutocomplete",
              "useMediaQuery",
              "useScrollTrigger",
              "Zoom",
            ],

            dayjs: [["default", "dayjs"]],
          },
        ],
        // Array of strings of regexes that contains imports meant to be filtered out.
        ignore: ["useMouse", "useFetch"],

        // Enable auto import by filename for default module exports under directories
        defaultExportByFilename: false,

        // Auto import for module exports under directories
        // by default it only scan one level of modules under the directory
        dirs: [
          // './hooks',
          // './composables' // only root modules
          // './composables/**', // all nested modules
          // ...
        ],

        // Filepath to generate corresponding .d.ts file.
        // Defaults to './auto-imports.d.ts' when `typescript` is installed locally.
        // Set `false` to disable.
        dts: "./auto-imports.d.ts",

        // Array of strings of regexes that contains imports meant to be ignored during
        // the declaration file generation. You may find this useful when you need to provide
        // a custom signature for a function.
        ignoreDts: ["ignoredFunction", /^ignore_/],

        // Auto import inside Vue template
        // see https://github.com/unjs/unimport/pull/15 and https://github.com/unjs/unimport/pull/72
        vueTemplate: false,

        // Auto import directives inside Vue template
        // see https://github.com/unjs/unimport/pull/374
        vueDirectives: undefined,

        // Custom resolvers, compatible with `unplugin-vue-components`
        // see https://github.com/antfu/unplugin-auto-import/pull/23/
        resolvers: [
          /* ... */
        ],

        // Include auto-imported packages in Vite's `optimizeDeps` options
        // Recommend to enable
        viteOptimizeDeps: true,

        // Inject the imports at the end of other imports
        injectAtEnd: true,

        // Generate corresponding .eslintrc-auto-import.json file.
        // eslint globals Docs - https://eslint.org/docs/user-guide/configuring/language-options#specifying-globals
        eslintrc: {
          enabled: false, // Default `false`
          // provide path ending with `.mjs` or `.cjs` to generate the file with the respective format
          filepath: "./.eslintrc-auto-import.json", // Default `./.eslintrc-auto-import.json`
          globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },

        // Generate corresponding .biomelintrc-auto-import.json file.
        // biomejs extends Docs - https://biomejs.dev/guides/how-biome-works/#the-extends-option
        biomelintrc: {
          enabled: false, // Default `false`
          filepath: "./.biomelintrc-auto-import.json", // Default `./.biomelintrc-auto-import.json`
        },

        // Save unimport items into a JSON file for other tools to consume
        dumpUnimportItems: "./auto-imports.json", // Default `false`
      }),

      VitePWA({
        registerType: "autoUpdate",
        workbox: {
          globPatterns: ["**/*.{js,css,html,ico,png,svg}"], //缓存相关静态资源
          maximumFileSizeToCacheInBytes: 5000000,
          runtimeCaching: [
            {
              urlPattern:
                /^https:\/\/minervaiot-storage-service-china-prod\.s3\.cn-north-1\.amazonaws\.com\.cn\/public\/ScreenDirect\/Web\/Font\/v1\/.*/,
              handler: "CacheFirst",
              options: {
                cacheName: "ScreenDirect_font_CacheFirst",
                expiration: {
                  maxEntries: 500,
                  maxAgeSeconds: 60 * 60 * 24 * 365,
                },
                cacheableResponse: {
                  statuses: [200],
                },
              },
            },
            {
              urlPattern: /^^https:\/\/api\.map\.baidu\.com.*/,
              handler: "CacheFirst",
              options: {
                cacheName: "baidu_map_CacheFirst",
                expiration: {
                  maxEntries: 500,
                  maxAgeSeconds: 60 * 60 * 24 * 30,
                },
                cacheableResponse: {
                  statuses: [200],
                },
              },
            },
          ],
        },
      }),
    ],

    server: {
      port: 8080,
      host: "0.0.0.0",
      cors: true,
      proxy: {
        "/dev": {
          // 推荐不要直接修改下面的地址，查看同级目录下的local-env.js.sample文件介绍
          // target: localEnv?.proxyTarget || "http://**********:9090",
          // target: localEnv?.proxyTarget || "http://***********:9090",
          // target: localEnv?.proxyTarget || "http://**********:9090",
          target: localEnv?.proxyTarget || "http://**********:9090",
          // target: "http://***********:9090",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev/, ""),
        },
      },
    },

    esbuild: {
      loader: "jsx",
    },

    optimizeDeps: {
      esbuildOptions: {
        loader: {
          ".js": "jsx",
        },
      },
    },

    extensions: [".js", ".json"],

    build: {
      outDir: "dist",
      // esbuild 打包更快，但是不能去除 console.log，去除 console 使用 terser 模式
      minify: "esbuild",
      sourcemap: true,
      //vite.config.js
      chunkSizeWarningLimit: 5000,
      // minify: "terser",
      // terserOptions: {
      //   compress: {
      //     drop_console: viteEnv.VITE_DROP_CONSOLE,
      //     drop_debugger: true,
      //   },
      // },
      rollupOptions: {
        output: {
          // 打包时进行分包
          manualChunks(id) {
            if (id.includes("node_modules")) {
              if (id.includes("crypto-js")) {
                return "crypto-js";
              }

              if (id.includes("lodash-es")) {
                return "lodash-es";
              }

              if (id.includes("@ant-design")) {
                return "@ant-design";
              }

              // if (id.includes("@mui/material")) {
              //   return "@mui/material";
              // }

              if (id.includes("react-google-maps")) {
                return "react-google-maps";
              }

              if (id.includes("react-bmapgl")) {
                return "react-bmapgl";
              }

              return "vendor";
            }
          },
          // Static resource classification and packaging
          chunkFileNames: "assets/js/[name]-[hash].js",
          entryFileNames: "assets/js/[name]-[hash].js",
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
        },
      },
    },
  };
});
