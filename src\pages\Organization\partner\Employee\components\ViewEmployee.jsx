import React from "react";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import { Avatar } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import { getParnerDetailUser } from "@s/api/partner";
import InfoBox from "@c/ViewBox.jsx";
function ViewEmployee(props) {
  // 国际化
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [data, setData] = useState([]);
  useEffect(() => {
    if (state?.type == "view" || state?.type == "editor") {
      getParnerDetailUser(state?.id).then((res) => {
        setData(res?.data);
      });
    }
  }, []);

  return (
    <React.Fragment>
      <RightViewLayout
        title={t("partner_user.view_title")}
        navigateBack={"/partner/employee/list"}
        handleCancle={() => {
          navigate("/partner/employee/list");
        }}
        isShowSave={false}>
        <Grid container direction={"column"} gap={4}>
          <Avatar
            className="avatar radial-button"
            alt="ZK"
            src={data?.photo}
            sx={{
              width: "160px",
              height: "160px",
            }}></Avatar>

          <InfoBox
            title={t("branch_user.firstName")}
            content={data?.firstName}
          />

          <InfoBox title={t("branch_user.lastName")} content={data?.lastName} />

          <InfoBox title={t("partner_user.email")} content={data?.email} />

          <InfoBox
            title={t("common.common_mobile")}
            content={
              `${data?.countryCode}`
                ? `+ ${data?.countryCode} ${data?.phone}`
                : null
            }
          />

          <InfoBox title={t("principal.address")} content={data?.remark} />
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default ViewEmployee;
