import {
  Input<PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Autocomplete,
  TextField,
  FormHelperText,
} from "@mui/material";
import RequirePoint from "./RequirePoint";
import { useEffect, useState } from "react";

function CmpAutocomplete(props) {
  const {
    formik = null,
    placeholder = "",
    handleBlur,
    label,
    name,
    error,
    disabled = false,
    isClear = true,
    labelpostion,
    spacing = 1,
    width,
    height = "40px",
    inputType,
    options,
    fontSize = "18px",
    readonly,
    typevalue = "0",
    onchangType,
    ...orther
  } = props;

  const [data, setData] = useState(null);

  useEffect(() => {
    const item = options.find((item) => {
      return item.id == formik.values[name];
    });

    setData(item);
    formik.setFieldValue(name, formik.values[name]);
  }, [formik.values[name]]);

  return (
    <Stack>
      <Stack
        direction={labelpostion === "left" ? "row" : "column"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={spacing}></Stack>
      <InputLabel
        shrink
        htmlFor={"CmpAutoComPlete_" + name}
        style={{
          marginTop: labelpostion === "left" ? "12px" : "",
          width: width,
          fontSize: fontSize,
        }}>
        {props.required && <RequirePoint></RequirePoint>}
        {label}
      </InputLabel>
      <Stack
        sx={{
          flexGrow: 1,
          width: "100%",
        }}>
        <Autocomplete
          disablePortal
          id={props.id}
          fullWidth
          options={options || []}
          onChange={(event, newValue) => {
            setData(newValue);
            formik.setFieldValue(name, newValue?.value);
          }}
          value={data}
          disabled={disabled}
          name={name}
          isOptionEqualToValue={(option, value) =>
            option.value ? option.value : ""
          }
          getOptionLabel={(option) =>
            typevalue === "1"
              ? option?.value
                ? option?.value
                : ""
              : option?.name
              ? option?.name
              : ""
          }
          renderInput={(params) => (
            <TextField
              {...params}
              size="small"
              readonly={readonly}
              placeholder={placeholder}
              disableMaskedInput
              sx={{
                "& .MuiOutlinedInput-root": {
                  height: "50px",
                },
                borderRadius: "7px",
              }}
            />
          )}
          {...orther}
        />
        {((formik?.touched[name] && formik?.errors[name]) || error) && (
          <FormHelperText error id={`standard-weight-helper-text-${name}`}>
            {formik?.errors[name] || error}
          </FormHelperText>
        )}
      </Stack>
    </Stack>
  );
}

export default CmpAutocomplete;
