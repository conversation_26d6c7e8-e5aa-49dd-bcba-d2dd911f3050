import React from "react";
import { useTranslation } from "react-i18next";
import { Grid } from "@mui/material";

function UploadAvatar(props) {
  const { t } = useTranslation();
  const { imageUrl, handleUpload } = props;

  const fileInputRef = useRef();

  const handleAvatarClick = () => {
    fileInputRef.current.click(); // 触发隐藏的文件输入框
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      handleUpload(file); // 调用上传逻辑
    }
  };

  return (
    <React.Fragment>
      <Grid
        container
        sx={{
          justifyContent: "center", // 水平居中
          alignItems: "center", // 垂直居中
          textAlign: "center",
          flexDirection: "column",
        }}>
        <Grid item>
          <Avatar
            className="avatar radial-button"
            alt="加载失败"
            sx={{ width: "110px", height: "110px", borderRadius: "8px" }}
            src={imageUrl}
            onClick={handleAvatarClick} // 点击时触发隐藏文件上传
          ></Avatar>

          <input
            type="file"
            accept=".jpg,.png,.jpeg"
            style={{ display: "none" }} // 隐藏文件输入框
            ref={fileInputRef} // 绑定引用
            onChange={handleFileChange} // 文件变化时触发
          />
        </Grid>

        <Grid item>
          <Typography
            sx={{
              fontSize: "12px",
              opacity: "0.8",
              mt: 1,
            }}>
            {t("*Maximum size 3 MB")}
          </Typography>
          <Typography
            sx={{
              fontSize: "12px",
              opacity: "0.8",
            }}>
            {t("*Allowed only ")}
          </Typography>
          <Typography
            sx={{
              fontSize: "12px",
              opacity: "0.8",
            }}>
            {t("JPEG, JPG, PNG")}
          </Typography>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default UploadAvatar;
