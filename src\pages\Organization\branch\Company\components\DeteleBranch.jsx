import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Boots<PERSON>p<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BootstrapDialogTitle,
} from "@c/dialog";
import { useTranslation } from "react-i18next";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@/assets/Icons/CloseIcon.svg?react";

function DeteleBranch(props) {
  const { open, setOpen, handlerDetele } = props;
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <BootstrapDialog
        open={open}
        onClose={() => setOpen(false)}
        aria-describedby="alert-dialog-slide-description"
        sx={{
          "& .MuiPaper-root": {
            borderRadius: "10px",
          },
        }}>
        <BootstrapDialogTitle>
          <Typography
            sx={{
              font: `normal normal bold 18px/22px Proxima Nova`,
              color: "#474B4F",
              pl: 2,
            }}>
            {t("branch.delete_branch")}
          </Typography>

          <IconButton
            aria-label="close"
            onClick={() => setOpen(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              width: 40,
              height: 40,
              color: (theme) => theme.palette.grey[500],
            }}>
            <CloseIcon
              style={{
                width: 26,
                height: 26,
              }}
            />
          </IconButton>
        </BootstrapDialogTitle>
        <BootstrapContent
          dividers={true}
          sx={{
            borderBottom: "none",
            fontSize: "18px",
            width: "450px",
          }}>
          <Grid
            sx={{
              font: "normal normal normal 14px/18px Proxima Nova",
              color: "#474B4F",
              lineHeight: "30px",
              pl: 2,
            }}>
            <span
              dangerouslySetInnerHTML={{
                __html: t("branch.delete_content"),
              }}></span>
          </Grid>
        </BootstrapContent>
        <BootstrapActions
          dividers={true}
          sx={{
            justifyContent: "center",
          }}>
          <Grid item xs={6} sm={4} md={3} mt={2} mb={2}>
            <Stack direction="row" spacing={2}>
              <Button
                variant="outlined"
                onClick={() => setOpen(false)}
                style={{
                  width: "180px",
                  height: "60px",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                }}
                disableElevation
                color="info"
                size="medium">
                {t("common.common_edit_cancel")}
              </Button>
              <Button
                disableElevation
                type="submit"
                variant="contained"
                size="medium"
                style={{
                  width: "180px",
                  height: "60px",
                  borderRadius: "8px",
                  opacity: 1,
                  background:
                    "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                }}
                onClick={() => handlerDetele()}>
                {t("common.common_delete")}
              </Button>
            </Stack>
          </Grid>
        </BootstrapActions>
      </BootstrapDialog>
    </React.Fragment>
  );
}

export default DeteleBranch;
