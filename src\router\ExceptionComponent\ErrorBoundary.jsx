import React from "react";
import { Box, Typography, Button, Paper, Alert } from "@mui/material";
import { styled } from "@mui/material/styles";
import { ErrorOutline, Refresh } from "@mui/icons-material";

const ErrorContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  margin: theme.spacing(2),
  textAlign: "center",
  backgroundColor: theme.palette.error.light,
  color: theme.palette.error.contrastText,
  width: "calc(100% - 32px)", // 减去margin的宽度
  maxWidth: "800px", // 限制最大宽度
  marginLeft: "auto",
  marginRight: "auto",
  borderRadius: theme.spacing(2),
  boxShadow: theme.shadows[3],
}));

const ErrorIcon = styled(ErrorOutline)(({ theme }) => ({
  fontSize: "4rem",
  marginBottom: theme.spacing(2),
  color: theme.palette.error.main,
}));

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error) {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // 你同样可以将错误日志上报给服务器
    console.error("ErrorBoundary caught an error:", error, errorInfo);

    this.setState({
      error: error,
      errorInfo: errorInfo,
    });

    // 这里可以添加错误上报逻辑
    this.logErrorToService(error, errorInfo);
  }

  logErrorToService = (error, errorInfo) => {
    // 模拟错误上报
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    console.log("Error logged:", errorData);

    // 实际项目中可以发送到错误监控服务
    // fetch('/api/errors', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(errorData)
    // });
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback } = this.props;

      // 如果提供了自定义的fallback组件，使用它
      if (Fallback) {
        return (
          <Fallback
            error={this.state.error}
            errorInfo={this.state.errorInfo}
            onRetry={this.handleRetry}
            onReload={this.handleReload}
          />
        );
      }

      // 默认的错误UI
      return (
        <ErrorContainer elevation={3}>
          <ErrorIcon />
          <Typography variant="h4" gutterBottom>
            哎呀！出现了一些问题
          </Typography>
          <Typography variant="body1" paragraph>
            应用程序遇到了意外错误。我们已经记录了这个问题，请稍后再试。
          </Typography>

          <Box
            sx={{ mt: 3, display: "flex", gap: 2, justifyContent: "center" }}>
            <Button
              variant="contained"
              startIcon={<Refresh />}
              onClick={this.handleRetry}
              color="primary">
              重试
            </Button>
            <Button
              variant="outlined"
              onClick={this.handleReload}
              color="primary">
              刷新页面
            </Button>
          </Box>

          {/* 开发环境下显示错误详情 */}
          {process.env.NODE_ENV === "development" && this.state.error && (
            <Alert severity="error" sx={{ mt: 3, textAlign: "left" }}>
              <Typography variant="h6" gutterBottom>
                错误详情 (仅开发环境显示):
              </Typography>
              <Typography
                variant="body2"
                component="pre"
                sx={{ whiteSpace: "pre-wrap" }}>
                {this.state.error.toString()}
              </Typography>
              {this.state.errorInfo && (
                <Typography
                  variant="body2"
                  component="pre"
                  sx={{ whiteSpace: "pre-wrap", mt: 1 }}>
                  {this.state.errorInfo.componentStack}
                </Typography>
              )}
            </Alert>
          )}
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
