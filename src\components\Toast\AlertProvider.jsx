import React, { createContext, useContext, useState, useCallback } from "react";
import Alert from "@mui/material/Alert";
import { Snackbar } from "@mui/material";

const AlertContext = createContext();

export const AlertProvider = ({ children }) => {
  const [alertState, setAlertState] = useState({
    open: false,
    message: "",
    severity: "info", // 可选值: "success", "info", "warning", "error"
  });

  const showAlert = useCallback((message, severity = "info") => {
    setAlertState({ open: true, message, severity });
  }, []);

  const closeAlert = useCallback(() => {
    setAlertState((prev) => ({ ...prev, open: false }));
  }, []);

  return (
    <AlertContext.Provider value={{ showAlert }}>
      {children}
      <Snackbar
        open={alertState.open}
        autoHideDuration={2000}
        onClose={closeAlert}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}>
        <Alert
          onClose={closeAlert}
          severity={alertState.severity}
          sx={{ width: "200px" }}>
          {alertState.message}
        </Alert>
      </Snackbar>
    </AlertContext.Provider>
  );
};

export const useAlert = () => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error("useAlert must be used within an AlertProvider");
  }
  return context;
};
