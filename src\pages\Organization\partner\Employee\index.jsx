import React from "react";
import TableList from "./components/TableList";
import LayoutList from "@l/components/LayoutList";
import CustomDelete from "@c/Toast/CustomDelete";
import { getParnerListUser, deleteParnerUser } from "@s/api/partner";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import { useTableRequest, getDepartmentId } from "../../utils.js"; // 引入

const index = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [tenantId, setTenantId] = useState(null);
  const [serchName, setSeachName] = useState("");
  const departmentId = getDepartmentId();
  const {
    data,
    isLoading,
    isRefetching,
    isError,
    rowCount,
    pagination,
    setPagination,
    fetchData,
    search,
    reset,
  } = useTableRequest(getParnerListUser, { departmentId: departmentId });

  // 删除
  const handlerDetele = async () => {
    try {
      const res = await deleteParnerUser(tenantId);
      toast.success(res?.message);
      fetchData();
    } catch {
      toast.error(t("common.deleteFailed"));
    } finally {
      setOpen(false);
    }
  };

  // 搜索
  const handlerSeacher = () => {
    search({ name: serchName, departmentId: departmentId });
  };

  // 清空
  const handleClear = () => {
    setSeachName("");
    reset({ departmentId: departmentId });
  };

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
        setTenantId={setTenantId}
        setOpen={setOpen}
        getTableData={fetchData}></TableList>
    );
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("partner_user.title")}
        isSearch={true}
        serchName={serchName}
        onClick={handlerSeacher}
        setSeachName={setSeachName}
        onClear={handleClear} // 添加 onClear 属性
        content={rederTable()}></LayoutList>

      <CustomDelete
        open={open}
        setOpen={setOpen}
        handlerDetele={handlerDetele}
        content={t("partner_user.sure_delete")}
        onContent={t("partner_user.delete_no_delete")}
        noDelete={false}></CustomDelete>
    </React.Fragment>
  );
};

export default index;
