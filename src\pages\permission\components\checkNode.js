// 获取所有节点方法
const getAllChildIds = (nodeId, tree) => {
  const node = findNodeById(tree, nodeId);
  if (!node || !node.children) return [];
  return node.children.reduce(
    (acc, child) => [...acc, child.id, ...getAllChildIds(child.id, tree)],
    []
  );
};

// 根据id 获取对应的对象
const findNodeById = (tree, nodeId) => {
  for (let node of tree) {
    if (node.id === nodeId) return node;
    if (node.children) {
      const found = findNodeById(node.children, nodeId);
      if (found) return found;
    }
  }
  return null;
};

// 获取所有父节点方法
const getAllParentIds = (nodeId, tree) => {
  for (let node of tree) {
    if (node.id === nodeId) return [nodeId];
    if (node.children) {
      const parentPath = getAllParentIds(nodeId, node.children);
      if (parentPath.length) return [node.id, ...parentPath];
    }
  }
  return [];
};

//  检查主节点的子节点是否都没有被选中的辅助函数
const areAllChildrenUnselected = (nodeId, selectedNodes, tree) => {
  const node = findNodeById(tree, nodeId);

  if (!node || !node.children) return true;
  return node.children.every(
    (child) =>
      !selectedNodes.includes(child.id) &&
      areAllChildrenUnselected(child.id, selectedNodes, tree)
  );
};

export const handlderSelectNode = (id, permissionTree, setSelectedNode) => {
  setSelectedNode((lastNode) => {
    let updatedNode = [...lastNode];
    const childIds = getAllChildIds(id, permissionTree);
    const isCurrentlySelected = updatedNode.includes(id);

    if (isCurrentlySelected) {
      updatedNode = updatedNode.filter(
        (nodeId) => ![id, ...childIds].includes(nodeId)
      );

      const parentIds = getAllParentIds(id, permissionTree);
      parentIds.forEach((parentId) => {
        if (areAllChildrenUnselected(parentId, updatedNode, permissionTree)) {
          if (updatedNode.length === 1) {
            updatedNode = [];
          } else {
            updatedNode = updatedNode.filter((nodeId) => nodeId !== parentId);
          }
        }
      });
    } else {
      updatedNode = [...updatedNode, id, ...childIds];
      const parentIds = getAllParentIds(id, permissionTree);
      updatedNode = [...new Set([...updatedNode, ...parentIds])];
    }

    return updatedNode;
  });
};
