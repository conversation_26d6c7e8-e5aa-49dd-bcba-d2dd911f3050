import React, { useCallback } from 'react';
import {
  FormControl,
  FormControlLabel,
  FormLabel,
  FormHelperText,
  Radio,
  RadioGroup,
  Box,
  Typography
} from '@mui/material';
import { useTranslation } from 'react-i18next';

/**
 * Schema驱动的单选按钮组件
 */
const SchemaRadio = ({
  name,
  path,
  schema,
  uiSchema = {},
  value = '',
  error,
  touched,
  disabled = false,
  readonly = false,
  required = false,
  onChange,
  onBlur,
  onValidate,
  formData,
  registry,
  ...props
}) => {
  const { t } = useTranslation();

  // 从schema中提取配置
  const {
    title,
    description,
    enum: enumValues,
    enumNames,
    oneOf,
    anyOf
  } = schema;

  // 从uiSchema中提取UI配置
  const {
    label = title,
    help = description,
    inline = false,
    color = 'primary',
    size = 'medium',
    labelPlacement = 'end',
    ...uiProps
  } = uiSchema;

  // 构建选项列表
  const options = React.useMemo(() => {
    let opts = [];

    // 从enum构建选项
    if (enumValues) {
      opts = enumValues.map((val, index) => ({
        value: val,
        label: enumNames?.[index] || val,
        disabled: false
      }));
    }
    
    // 从oneOf构建选项
    else if (oneOf) {
      opts = oneOf.map(option => ({
        value: option.const || option.enum?.[0],
        label: option.title || option.const || option.enum?.[0],
        disabled: option.disabled || false
      }));
    }
    
    // 从anyOf构建选项
    else if (anyOf) {
      opts = anyOf.map(option => ({
        value: option.const || option.enum?.[0],
        label: option.title || option.const || option.enum?.[0],
        disabled: option.disabled || false
      }));
    }

    return opts;
  }, [enumValues, enumNames, oneOf, anyOf]);

  // 处理值变化
  const handleChange = useCallback((event) => {
    const newValue = event.target.value;
    onChange?.(newValue);
  }, [onChange]);

  // 处理失焦
  const handleBlur = useCallback((event) => {
    onBlur?.(event);
    
    // 执行验证
    if (onValidate) {
      const errors = [];
      
      // 必填验证
      if (required && (value === undefined || value === null || value === '')) {
        errors.push(t('validation.required', { field: label || name }));
      }
      
      // 选项验证
      if (value && options.length > 0) {
        const validValues = options.map(opt => opt.value);
        if (!validValues.includes(value)) {
          errors.push(t('validation.invalidOption', { field: label || name }));
        }
      }
      
      onValidate(errors.length > 0 ? errors[0] : null);
    }
  }, [onBlur, onValidate, value, required, options, label, name, t]);

  // 如果没有选项，显示警告
  if (!options || options.length === 0) {
    return (
      <Box className="schema-form-field">
        <Typography color="error" variant="body2">
          No options available for radio group: {label || name}
        </Typography>
      </Box>
    );
  }

  return (
    <Box className="schema-form-field">
      <FormControl
        component="fieldset"
        error={Boolean(error && touched)}
        disabled={disabled}
        required={required}
      >
        {label && (
          <FormLabel component="legend">
            {label}
          </FormLabel>
        )}
        
        <RadioGroup
          name={name}
          value={value || ''}
          onChange={handleChange}
          onBlur={handleBlur}
          row={inline}
          {...uiProps}
          {...props}
        >
          {options.map((option) => (
            <FormControlLabel
              key={option.value}
              value={option.value}
              control={
                <Radio
                  color={color}
                  size={size}
                  disabled={disabled || readonly || option.disabled}
                />
              }
              label={option.label}
              labelPlacement={labelPlacement}
              disabled={disabled || option.disabled}
            />
          ))}
        </RadioGroup>
        
        {/* 帮助文本和错误信息 */}
        {((error && touched) || help) && (
          <FormHelperText>
            {(error && touched) ? error : (
              help && (
                <Typography variant="caption" color="text.secondary">
                  {help}
                </Typography>
              )
            )}
          </FormHelperText>
        )}
      </FormControl>
    </Box>
  );
};

export default SchemaRadio;
