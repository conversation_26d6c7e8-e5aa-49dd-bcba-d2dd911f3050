.rc-tree-select {
  width: 100%;
  position: relative;
}

.zk-tree-select-container {
  position: relative;
  width: 100%;
}

.rc-tree-select-selection-search-input {
  width: 100%;
  height: 41px;
  border: 1px solid #d9d9d9 !important;
  border-radius: 4px;
  padding: 0 30px 0 11px;
}

.rc-tree-select-selection-item {
  position: absolute;
  top: 50%;
  left: 11px;
  transform: translateY(-50%);
  pointer-events: none;
}

/* 添加以下样式 */
.rc-tree-select-selection-search-input:focus {
  border-color: rgb(112, 228, 112) !important;
  /* 聚焦时边框变绿 */
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  outline: none;
  /* 移除默认的聚焦轮廓 */
}

.rc-tree-select-focused .rc-tree-select-selector {
  border-color: rgb(112, 228, 112) !important;
  /* 聚焦时边框变绿 */
}

.rc-tree-select-focused .rc-tree-select-selection-search-input {
  border-color: rgb(112, 228, 112) !important;
  /* 聚焦时边框变绿 */
}

/* 添加以下样式 */
.rc-tree-select-selection-placeholder {
  color: #bfbfbf;
  /* placeholder 颜色 */
  position: absolute;
  top: 50%;
  left: 11px;
  /* 与输入框左内边距对齐 */
  right: 11px;
  /* 确保文字不会超出右侧边界 */
  transform: translateY(-50%);
  transition: all 0.3s;
  pointer-events: none;
  /* 防止 placeholder 影响输入 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 40px;
}

/* 当有值时隐藏 placeholder */
.rc-tree-select-selection-item .rc-tree-select-selection-placeholder {
  display: none;
}

/* 添加以下样式 */
.rc-tree-select-clear {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  background: #fff;
  color: #999;
  font-size: 20px;
  cursor: pointer;
}

/* 确保下拉箭头不会与清除图标重叠 */
.rc-tree-select-arrow {
  right: 25px !important;
}

/* 当有值时显示清除图标 */
.rc-tree-select-allow-clear.rc-tree-select-show-arrow
  .rc-tree-select-selection-item {
  padding-right: 45px;
  /* 为清除图标和下拉箭头留出空间 */
}

.rc-tree-select {
  position: relative;
  width: 100%;
}

.rc-tree-select-dropdown {
  position: absolute;

  left: 0;
  width: 100%;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1050;
}

.rc-tree-select-tree {
  padding: 5px;
}

.rc-tree-select-tree-treenode {
  padding: 5px 0;
}

.rc-tree-select-tree-node-content-wrapper {
  padding: 3px 5px;
}

.rc-tree-select-tree-node-content-wrapper:hover {
  background-color: #f5f5f5;
}

/* 确保下拉框可见 */
.rc-tree-select-dropdown-hidden {
  display: block !important;
}

/* 下拉箭头样式 */
.rc-tree-select-arrow {
  position: absolute;
  right: 11px;
  top: 50%;
  transform: translateY(-50%);
}

/* 当有值时显示清除图标 */
.rc-tree-select-allow-clear.rc-tree-select-show-arrow
  .rc-tree-select-selection-item {
  padding-right: 45px;
}

/* 隐藏多余的插号 */
.rc-tree-select-selection-item {
  opacity: 0;
}

/* 确保输入的文字可见 */
.rc-tree-select-selection-search-input {
  opacity: 1 !important;
}

/* .box{
    opacity: 1;
    max-height: 224px;
    width: 100%;
    transition: opacity 289ms cubic-bezier(0.4, 0, 0.2, 1), transform 192ms cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12);
    transform-origin: 234px 0px;  
    overflow-y: auto;
    overflow-x: hidden;
    outline: 0;
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}
   */

.no-match-result {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 36px;
  color: #999;
  font-size: 14px;
  background-color: rgba(246, 247, 251, 0.8);
}

.no-match-icon {
  font-size: 20px;
  margin-right: 8px;
}

.no-match-text {
  font-weight: 500;
}
