import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>p<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BootstrapDialogTitle,
} from "@c/dialog";
import { useTranslation } from "react-i18next";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@/assets/Icons/CloseIcon.svg?react";

function ZKDialog(props) {
  const { open, setOpen, title, children, formik, sx } = props;
  const { t } = useTranslation();

  return (
    <React.Fragment>
      <form noValidate onSubmit={formik.handleSubmit}>
        <BootstrapDialog
          open={open}
          onClose={() => setOpen(false)}
          aria-describedby="alert-dialog-slide-description">
          <BootstrapDialogTitle
            sx={{
              borderRadius: "10px",
            }}>
            <Typography
              sx={{
                font: `normal normal bold 18px/24px Proxima Nova`,
                color: "#474B4F",
              }}>
              {title}
            </Typography>

            <IconButton
              aria-label="close"
              onClick={() => setOpen(false)}
              sx={{
                position: "absolute",
                right: 8,
                top: 8,
                color: (theme) => theme.palette.grey[500],
              }}>
              <CloseIcon />
            </IconButton>
          </BootstrapDialogTitle>
          <BootstrapContent sx={sx}>{children}</BootstrapContent>
          <BootstrapActions>
            <Button
              type="submit"
              style={{
                width: "100%",
                height: "48px",
                borderRadius: "10px",
                color: "#FFFFFF",
                textAlign: "center",
                font: `normal normal medium 14px/18px Proxima Nova`,
                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
              }}
              onClick={() => formik.handleSubmit()}>
              {t("common.common_change_password")}
            </Button>
          </BootstrapActions>
        </BootstrapDialog>
      </form>
    </React.Fragment>
  );
}

export default ZKDialog;
