import React, { useEffect, useState } from "react";
import DictTag from "@c/DictTag";
import { getDeviceList } from "@s/api/device.js";
import ZktecoTable from "@c/ZktecoTable";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { debounce } from "lodash-es";
import useDict from "@/hooks/useDict.js";
import ZkTooltip from "@c/ZkTooltip";
import { getDeviceDetail } from "@s/api/device.js";
import DialogDevice from "./DialogDevice";
import CMSDevice from "./CMSDevice";
function TableList({ selectedValue, setDeleteOpen, setCurrentId }) {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const dicts = useDict(["dev_device_status"]);
  const [data, setData] = useState([]);
  const [open, setOpen] = useState(false);
  const [cmsOpen, setCmsOpen] = useState(false);

  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [detailData, setDetailData] = useState([]);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  // 构建参数
  const buildParams = (value) => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      applicationCode: value,
    };

    return params;
  };

  // 获取数据
  const getTableData = useCallback(
    debounce((value) => {
      if (!data.length) {
        setIsLoading(true);
      } else {
        setIsRefetching(true);
      }

      getDeviceList(buildParams(value))
        .then((res) => {
          // 设置数据
          setData(res.data.data);
          // 设置总记录数
          setRowCount(res.data.total);
          setIsLoading(false);
          setIsRefetching(false);
        })
        .catch((err) => {
          setIsError(true);
          setIsLoading(false);
          setIsRefetching(false);
        });
    }, 50),
    [selectedValue, open, cmsOpen]
  );

  useEffect(() => {
    getTableData(selectedValue);
  }, [selectedValue, pagination.pageIndex, pagination.pageSize, open, cmsOpen]);

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("device.device_name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "sn",
        header: t("device.sn"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "deviceModel",
        header: t("device.model"),
        disableFilters: true,
        enableGlobalFilter: false,
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "outletName",
        header: t("outlets.outlet"),
        disableFilters: true,
        enableGlobalFilter: false,
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "status",
        header: t("device.online_status"),
        disableFilters: true,
        enableGlobalFilter: false,
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <div>
              <DictTag
                dicts={dicts.current?.dev_device_status}
                fieldName={{
                  value: "value",
                  title: "label",
                  listClass: "listClass",
                }}
                value={row.original.status}
              />
            </div>
          );
        },
      },
    ],
    []
  );

  const isShowAction = {
    isShowView: "dev:device:query",
    isShowEditor: "dev:device:update",
    isShowDetele: "dev:device:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/view/device", {
          state: {
            id: data?.id,
            type: "view",
            category: selectedValue,
            applicationCode: selectedValue,
          },
        }),
      handlerEditor: (data) => {
        getDeviceDetail(data?.id).then(({ data }) => {
          setDetailData(data);
        });
        if (selectedValue == "SD") {
          setCmsOpen(true);
        } else {
          setOpen(true);
        }

        sessionStorage.setItem("APP_TYPE", selectedValue);
      },

      Detele: (data) => {
        setDeleteOpen(true);
        setCurrentId(data?.id);
      },
    }),
    [selectedValue, setDeleteOpen, setCurrentId]
  );

  const onAdd = useCallback(
    (data) => {
      console.log("onAdd called", data, selectedValue);
      navigate("/add/device", {
        state: {
          id: data?.id,
          type: "add",
          category: selectedValue,
          applicationCode: selectedValue,
        },
      });
    },
    [selectedValue]
  );
  return (
    <React.Fragment>
      <ZktecoTable
        columns={columns}
        data={data}
        rowCount={rowCount}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        loadDada={() => getTableData(selectedValue)}
        paginationProps={{
          currentPage: pagination.pageIndex,
          rowsPerPage: pagination.pageSize,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
        }}
        topActions={{
          showAdd: "dev:device:save",
          onAdd: onAdd,
        }}
        actionHandlers={actionHandlers}
        isShowAction={isShowAction}
      />

      <DialogDevice
        open={open}
        setOpen={setOpen}
        preDeviceInfo={detailData}
        data={detailData}
        selectSence={detailData?.mainOrSub}
        category={selectedValue}
        applicationCode={selectedValue}></DialogDevice>

      <CMSDevice
        open={cmsOpen}
        setOpen={setCmsOpen}
        data={detailData}
        selectSence={detailData?.mainOrSub}
        preDeviceInfo={detailData}
        category={selectedValue}
        applicationCode={selectedValue}></CMSDevice>
    </React.Fragment>
  );
}

export default TableList;
