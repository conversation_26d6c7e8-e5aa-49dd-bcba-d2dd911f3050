{"globals": {"Accordion": true, "AccordionActions": true, "AccordionDetails": true, "AccordionSummary": true, "Alert": true, "AlertTitle": true, "AppBar": true, "Autocomplete": true, "Avatar": true, "AvatarGroup": true, "Backdrop": true, "Badge": true, "BottomNavigation": true, "BottomNavigationAction": true, "Box": true, "Breadcrumbs": true, "Button": true, "ButtonBase": true, "ButtonGroup": true, "Card": true, "CardActionArea": true, "CardActions": true, "CardContent": true, "CardHeader": true, "CardMedia": true, "Checkbox": true, "Chip": true, "CircularProgress": true, "ClickAwayListener": true, "Collapse": true, "Container": true, "CssBaseline": true, "Dialog": true, "DialogActions": true, "DialogContent": true, "DialogContentText": true, "DialogTitle": true, "Divider": true, "Drawer": true, "Fab": true, "Fade": true, "FilledInput": true, "FormControl": true, "FormControlLabel": true, "FormGroup": true, "FormHelperText": true, "FormLabel": true, "GlobalStyles": true, "Grid": true, "Grow": true, "Hidden": true, "Icon": true, "IconButton": true, "ImageList": true, "ImageListItem": true, "ImageListItemBar": true, "Input": true, "InputAdornment": true, "InputBase": true, "InputLabel": true, "LinearProgress": true, "Link": true, "List": true, "ListItem": true, "ListItemAvatar": true, "ListItemButton": true, "ListItemIcon": true, "ListItemSecondaryAction": true, "ListItemText": true, "ListSubheader": true, "Menu": true, "MenuItem": true, "MenuList": true, "MobileStepper": true, "Modal": true, "NativeSelect": true, "NavLink": true, "Navigate": true, "NoSsr": true, "Outlet": true, "OutlinedInput": true, "Pagination": true, "PaginationItem": true, "Paper": true, "Popover": true, "Popper": true, "Portal": true, "Radio": true, "RadioGroup": true, "Rating": true, "Route": true, "Routes": true, "ScopedCssBaseline": true, "Select": true, "Skeleton": true, "Slide": true, "Slider": true, "Snackbar": true, "SnackbarContent": true, "SpeedDial": true, "SpeedDialAction": true, "SpeedDialIcon": true, "Stack": true, "Step": true, "StepButton": true, "StepConnector": true, "StepContent": true, "StepIcon": true, "StepLabel": true, "Stepper": true, "SvgIcon": true, "SwipeableDrawer": true, "Switch": true, "Tab": true, "TabScrollButton": true, "Table": true, "TableBody": true, "TableCell": true, "TableContainer": true, "TableFooter": true, "TableHead": true, "TablePagination": true, "TableRow": true, "TableSortLabel": true, "Tabs": true, "TextField": true, "TextareaAutosize": true, "ToggleButton": true, "ToggleButtonGroup": true, "Toolbar": true, "Tooltip": true, "Typography": true, "Unstable_Grid2": true, "Zoom": true, "createRef": true, "darkScrollbar": true, "dayjs": true, "forwardRef": true, "generateUtilityClass": true, "generateUtilityClasses": true, "lazy": true, "memo": true, "startTransition": true, "useAutocomplete": true, "useCallback": true, "useContext": true, "useDebugValue": true, "useDeferredValue": true, "useEffect": true, "useHref": true, "useId": true, "useImperativeHandle": true, "useInRouterContext": true, "useInsertionEffect": true, "useLayoutEffect": true, "useLinkClickHandler": true, "useLocation": true, "useMediaQuery": true, "useMemo": true, "useNavigate": true, "useNavigationType": true, "useOutlet": true, "useOutletContext": true, "useParams": true, "useReducer": true, "useRef": true, "useResolvedPath": true, "useRoutes": true, "useScrollTrigger": true, "useSearchParams": true, "useState": true, "useSyncExternalStore": true, "useTransition": true, "useTranslation": true}}