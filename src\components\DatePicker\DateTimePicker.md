# DateTimePicker 组件

一个既可以选择年月日又能选择时分秒的时间组件，完全兼容 MUI 设计风格。

## 特性

- 📅 左侧日期选择面板（年月日）
- ⏰ 右侧时间选择面板（时分秒）
- 🎯 完全兼容 MUI 设计风格
- ✅ 支持确认/取消操作
- 🧹 支持清空功能
- 📱 响应式设计
- 🎭 主题集成
- 🌍 国际化支持（中文）

## 基础用法

```jsx
import React, { useState } from 'react';
import DateTimePicker from './DateTimePicker';
import dayjs from 'dayjs';

function App() {
  const [value, setValue] = useState();

  return (
    <DateTimePicker
      value={value}
      onChange={setValue}
      placeholder="请选择日期时间"
    />
  );
}
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | dayjs \| null | - | 当前选中的日期时间 |
| defaultValue | dayjs \| null | - | 默认选中的日期时间 |
| onChange | (value: dayjs \| null, dateString: string) => void | - | 时间发生变化的回调 |
| onOk | () => void | - | 点击确定按钮的回调 |
| onOpenChange | (open: boolean) => void | - | 弹出日历和关闭日历的回调 |
| placeholder | string | "请选择日期时间" | 输入框提示文字 |
| size | 'small' \| 'medium' \| 'large' | 'medium' | 输入框大小 |
| variant | 'outlined' \| 'filled' \| 'standard' | 'outlined' | 输入框变体 |
| disabled | boolean | false | 禁用状态 |
| allowClear | boolean | true | 是否显示清除按钮 |
| autoFocus | boolean | false | 自动获取焦点 |
| format | string | "YYYY-MM-DD HH:mm:ss" | 展示的时间格式 |
| open | boolean | - | 控制弹层是否展开 |
| defaultOpen | boolean | false | 默认弹层是否展开 |
| placement | string | "bottomLeft" | 弹出位置 |
| disabledDate | (date: dayjs) => boolean | - | 不可选择的日期 |
| minDate | dayjs | - | 最小可选日期 |
| maxDate | dayjs | - | 最大可选日期 |
| needConfirm | boolean | true | 是否需要确认按钮 |
| className | string | - | 容器类名 |
| style | CSSProperties | - | 容器样式 |

## 使用示例

### 基础用法

```jsx
<DateTimePicker
  value={value}
  onChange={setValue}
  placeholder="请选择日期时间"
/>
```

### 带默认值

```jsx
<DateTimePicker
  value={value}
  onChange={setValue}
  defaultValue={dayjs()}
  placeholder="请选择日期时间"
/>
```

### 禁用确认模式

```jsx
<DateTimePicker
  value={value}
  onChange={setValue}
  placeholder="请选择日期时间"
  needConfirm={false}
/>
```

### 限制日期范围

```jsx
<DateTimePicker
  value={value}
  onChange={setValue}
  placeholder="请选择日期时间"
  minDate={dayjs().subtract(1, 'month')}
  maxDate={dayjs().add(1, 'month')}
/>
```

### 自定义格式

```jsx
<DateTimePicker
  value={value}
  onChange={setValue}
  placeholder="请选择日期时间"
  format="YYYY年MM月DD日 HH时mm分ss秒"
/>
```

### 禁用状态

```jsx
<DateTimePicker
  value={value}
  onChange={setValue}
  placeholder="请选择日期时间"
  disabled
/>
```

## 样式定制

组件完全兼容 MUI 主题系统，可以通过 MUI 的主题定制功能来自定义样式。

```jsx
import { createTheme, ThemeProvider } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
  },
});

<ThemeProvider theme={theme}>
  <DateTimePicker
    value={value}
    onChange={setValue}
    placeholder="请选择日期时间"
  />
</ThemeProvider>
```

## 注意事项

1. 组件依赖 `@mui/material`、`@mui/x-date-pickers` 和 `dayjs`
2. 需要在应用根部包裹 `LocalizationProvider`（组件内部已处理）
3. 时间面板宽度会根据内容自适应
4. 默认启用确认模式，可通过 `needConfirm={false}` 禁用

## 兼容性

- React 16.8+
- MUI 5.0+
- dayjs 1.11+
