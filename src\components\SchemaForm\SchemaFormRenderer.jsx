import React, { useMemo } from 'react';
import { Box } from '@mui/material';
import SchemaFieldRegistry from './SchemaFieldRegistry';
import SchemaValidator from './SchemaValidator';
import SchemaLayoutEngine from './SchemaLayoutEngine';
import { useSchemaForm } from './hooks/useSchemaForm';
import { validateSchema, processConditionalLogic } from './utils/schemaUtils';

/**
 * Schema驱动的表单渲染器
 * 
 * @param {Object} props
 * @param {Object} props.schema - JSON Schema配置
 * @param {Object} props.formData - 表单数据
 * @param {Function} props.onChange - 数据变化回调
 * @param {Function} props.onValidate - 验证回调
 * @param {Object} props.fieldRegistry - 自定义字段注册表
 * @param {Object} props.layoutEngine - 自定义布局引擎
 * @param {Object} props.validationRules - 自定义验证规则
 * @param {Object} props.uiSchema - UI配置Schema
 * @param {boolean} props.disabled - 是否禁用
 * @param {boolean} props.readonly - 是否只读
 */
const SchemaFormRenderer = ({
  schema,
  formData = {},
  onChange,
  onValidate,
  fieldRegistry,
  layoutEngine,
  validationRules,
  uiSchema = {},
  disabled = false,
  readonly = false,
  className,
  sx,
  ...props
}) => {
  // 使用自定义Hook管理表单状态
  const {
    data,
    errors,
    touched,
    isValid,
    updateField,
    validateField,
    validateForm,
    resetForm
  } = useSchemaForm({
    schema,
    initialData: formData,
    validationRules,
    onChange,
    onValidate
  });

  // 创建字段注册表实例
  const registry = useMemo(() => {
    const defaultRegistry = new SchemaFieldRegistry();
    if (fieldRegistry) {
      return fieldRegistry.merge ? fieldRegistry.merge(defaultRegistry) : fieldRegistry;
    }
    return defaultRegistry;
  }, [fieldRegistry]);

  // 创建布局引擎实例
  const layout = useMemo(() => {
    return layoutEngine || new SchemaLayoutEngine();
  }, [layoutEngine]);

  // 创建验证器实例
  const validator = useMemo(() => {
    return new SchemaValidator(schema, validationRules);
  }, [schema, validationRules]);

  // 处理条件逻辑后的Schema
  const processedSchema = useMemo(() => {
    return processConditionalLogic(schema, data, uiSchema);
  }, [schema, data, uiSchema]);

  // 渲染字段的核心函数
  const renderField = (fieldSchema, fieldPath, parentData = data) => {
    const fieldName = fieldPath.split('.').pop();
    const fieldValue = getNestedValue(parentData, fieldPath);
    const fieldError = getNestedValue(errors, fieldPath);
    const fieldTouched = getNestedValue(touched, fieldPath);
    const fieldUiSchema = getNestedValue(uiSchema, fieldPath) || {};

    // 获取字段组件
    const FieldComponent = registry.getField(fieldSchema.type, fieldSchema.widget);
    
    if (!FieldComponent) {
      console.warn(`No field component found for type: ${fieldSchema.type}, widget: ${fieldSchema.widget}`);
      return null;
    }

    // 字段通用属性
    const fieldProps = {
      name: fieldName,
      path: fieldPath,
      schema: fieldSchema,
      uiSchema: fieldUiSchema,
      value: fieldValue,
      error: fieldError,
      touched: fieldTouched,
      disabled: disabled || fieldSchema.disabled || fieldUiSchema.disabled,
      readonly: readonly || fieldSchema.readonly || fieldUiSchema.readonly,
      required: fieldSchema.required || false,
      onChange: (value) => updateField(fieldPath, value),
      onBlur: () => validateField(fieldPath),
      onValidate: (error) => validator.setFieldError(fieldPath, error),
      formData: data,
      registry,
      ...fieldSchema.props,
      ...fieldUiSchema.props
    };

    return (
      <FieldComponent
        key={fieldPath}
        {...fieldProps}
      />
    );
  };

  // 渲染布局的核心函数
  const renderLayout = (layoutSchema, children, layoutPath = '') => {
    return layout.render(layoutSchema, children, {
      path: layoutPath,
      formData: data,
      errors,
      touched,
      disabled,
      readonly,
      uiSchema
    });
  };

  // 递归渲染Schema
  const renderSchema = (currentSchema, basePath = '') => {
    if (!currentSchema) return null;

    // 处理布局类型
    if (currentSchema.layout) {
      const children = currentSchema.fields?.map((field, index) => {
        const fieldPath = basePath ? `${basePath}.${field.name || index}` : (field.name || index.toString());
        return renderSchema(field, fieldPath);
      }) || [];

      return renderLayout(currentSchema.layout, children, basePath);
    }

    // 处理字段类型
    if (currentSchema.type) {
      return renderField(currentSchema, basePath);
    }

    // 处理对象类型（嵌套结构）
    if (currentSchema.properties) {
      return Object.entries(currentSchema.properties).map(([key, fieldSchema]) => {
        const fieldPath = basePath ? `${basePath}.${key}` : key;
        return renderSchema(fieldSchema, fieldPath);
      });
    }

    // 处理数组类型
    if (currentSchema.items) {
      const arrayValue = getNestedValue(data, basePath) || [];
      return arrayValue.map((item, index) => {
        const itemPath = `${basePath}[${index}]`;
        return renderSchema(currentSchema.items, itemPath);
      });
    }

    return null;
  };

  // 获取嵌套值的工具函数
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => {
      if (key.includes('[') && key.includes(']')) {
        const [arrayKey, indexStr] = key.split('[');
        const index = parseInt(indexStr.replace(']', ''));
        return current?.[arrayKey]?.[index];
      }
      return current?.[key];
    }, obj);
  };

  return (
    <Box
      className={className}
      sx={{
        '& .schema-form-field': {
          marginBottom: 2
        },
        '& .schema-form-group': {
          marginBottom: 3
        },
        ...sx
      }}
      {...props}
    >
      {renderSchema(processedSchema)}
    </Box>
  );
};

export default SchemaFormRenderer;
