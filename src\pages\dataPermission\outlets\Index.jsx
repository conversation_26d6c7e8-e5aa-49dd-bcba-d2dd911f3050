import React from "react";
import BootstrapDialog from "../components/ZkDialog";
import IconsGroup from "../components/IconsGroup";
import LeftTable from "./LeftTable.jsx";
import RightTable from "./RightTable.jsx";
import { useTranslation } from "react-i18next";
import ZkSearch from "./ZkSearch";
const Index = forwardRef((props, ref) => {
  const { open, setOpen, rowId, list } = props;

  const { t } = useTranslation();
  const tableRef = useRef(null);
  const rightTableRef = useRef(null); // 添加 ref
  const [rowSelection, setRowSelection] = useState([]);
  const [selectOutlets, setSelectOutlets] = useState([]);
  const [searchName, setSearchName] = useState(null);

  useEffect(() => {
    if (!list) return;
    const data = list
      .filter((item) => item?.outletId)
      .map((item) => ({
        ...item,
        id: item.outletId,
        name: item.outletName,
        email: item.outletEmail,
      }));
    const dataMap = new Map(data.map((item) => [item.id, item]));

    // 更新 selectOutlets 状态
    setSelectOutlets(dataMap);

    // 创建新的 rowSelection 对象
    const newRowSelection = {};
    data.forEach((item) => {
      newRowSelection[item.id] = true;
    });

    setRowSelection(newRowSelection);
  }, [list]);
  useEffect(() => {
    if (!tableRef.current) return;

    // 获取当前选中的数据
    const selectedData = tableRef.current
      .getSelectedRowModel()
      .rows.map((row) => row.original);

    // 深拷贝 current selectedRowsMap 数据，并合并当前选中的数据
    setSelectOutlets((prevMap) => {
      // 深拷贝现有的 Map 数据
      const newMap = new Map(prevMap);

      // 将当前选中的数据添加到 newMap 中，确保不会覆盖之前的数据
      selectedData.forEach((item) => {
        // 假设每个 item 有唯一的 id，可以作为 key
        newMap.set(item.id, item);
      });

      return newMap;
    });
  }, [rowSelection]);

  useImperativeHandle(ref, () => ({
    selectOutlets,

    deleteOutletsByRetailerId: (retailerId) => {
      setSelectOutlets((prevOutlets) => {
        const newOutlets = { ...prevOutlets };
        delete newOutlets[retailerId];
        return newOutlets;
      });
    },
  }));

  return (
    <React.Fragment>
      <BootstrapDialog
        open={open}
        setOpen={setOpen}
        title={t("datascope.outlet_selection")}
        handlerSubmit={() => setOpen(false)}>
        <Grid container xs={12}>
          <ZkSearch
            rowId={rowId}
            searchName={searchName}
            setSearchName={setSearchName}
            rightTableRef={rightTableRef}></ZkSearch>

          <Grid item xs={12} display={"flex"}>
            <LeftTable
              tableRef={tableRef}
              rowId={rowId}
              ref={rightTableRef}
              rowSelection={rowSelection}
              setRowSelection={setRowSelection}></LeftTable>

            <IconsGroup></IconsGroup>

            <RightTable
              selectOutlets={selectOutlets}
              setRowSelection={setRowSelection}
              setSelectOutlets={setSelectOutlets}></RightTable>
          </Grid>
        </Grid>
      </BootstrapDialog>
    </React.Fragment>
  );
});

export default Index;
