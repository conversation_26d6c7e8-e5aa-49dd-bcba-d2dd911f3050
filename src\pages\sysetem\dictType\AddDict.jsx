import React, { useState } from "react";
import ZKDialog from "@c/ZKDialog";
import { t } from "i18next";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import { toast } from "react-toastify";
import { useFormik } from "formik";
import { createValidation } from "@c/Config/validationUtils.js";
import { saveDictType, updateDictType } from "@/service/api/dictType.js";

function AddDict(props) {
  const { open, setOpen, type, getTableData } = props;

  const [loading, setLoading] = useState(false);

  let formConfig = [
    {
      name: "name",
      label: t("字典类型名称"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("请输入字典类型名称"),
        },
      ],
    },
    {
      name: "code",
      label: t("字典类型编码"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("请输入字典类型编码"),
        },
      ],
    },

    {
      name: "status",
      label: t("字典类型状态"),
      type: "select",
      options: [
        {
          label: t("成功"),
          value: "1",
        },
        {
          label: t("失败"),
          value: "0",
        },
      ],
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("请选择字典类型状态"),
        },
      ],
    },
  ];

  const formik = useFormik({
    initialValues: {
      name: "",
      code: "",
      status: "",
      sort: "1",
    },
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: (values) => {
      try {
        setLoading(true);
        if (type == "editor") {
          updateDictType(values).then((res) => {
            toast.success(res?.message);
            setLoading(false);
            setOpen(false);
            getTableData();
          });
        } else {
          saveDictType(values).then((res) => {
            toast.success(res?.message);
            setLoading(false);
            setOpen(false);
            getTableData();
          });
        }
      } catch (error) {
        setLoading(false);
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <React.Fragment>
      <ZKDialog
        open={open}
        setOpen={setOpen}
        title={type == "editor" ? t("修改数据字典类型") : t("新增数据字典类型")}
        handlerSubmit={formik.handleSubmit}
        loading={loading}
        width={"450px"}>
        <ZkFormik sx={12} formik={formik} formConfig={formConfig}></ZkFormik>
      </ZKDialog>
    </React.Fragment>
  );
}

export default AddDict;
