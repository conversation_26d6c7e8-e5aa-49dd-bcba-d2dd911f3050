import request from "@/utils/request";

/**
 *
 * @param {获取零售商分页列表} params
 * @returns
 */
export const getPrincipaList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/principal/query/page`,
    method: "get",
    params: params,
  });
};

export const addPrincipa = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/principal`,
    method: "post",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const editPrincipa = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/principal`,
    method: "PUT",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const deletePrincipa = (ids) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/principal/${ids}`,
    method: "DELETE",
  });
};

export const getPrincipaDetail = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/principal/query/${id}`,
    method: "get",
  });
};

/**
 * --------------------------------------------------------------------------
 *      获取代理商用户增删改查
 */

export const getPrincipaUserList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/principal_employee/query/page`,
    method: "get",
    params: params,
  });
};

export const addPrincipaUser = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/principal_employee`,
    method: "post",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const editPrincipaUser = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/principal_employee`,
    method: "PUT",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const deletePrincipaUser = (ids) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/principal_employee/${ids}`,
    method: "DELETE",
  });
};

export const getPrincipaDetailUser = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/principal_employee/query/${id}`,
    method: "get",
  });
};

export const switchDepartment = (departmentId) => {
  return request({
    url: `${
      import.meta.env.VITE_APICODE
    }/auth/switch/department/${departmentId}`,
    method: "post",
  });
};
