export const getFormConfig = (t, outletList) => {
  let formConfig = [
    {
      name: "departmentId",
      label: t("product_info.outlet"),
      placeholder: t("outlets.please_select_outlet"),
      type: "select",
      required: true,
      options: outletList,
      labelOptions: { label: "name", value: "id" },
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("outlets.please_select_outlet"),
        },
      ],
    },
    {
      name: "isEmpty",
      label: t("product_info.is_empty"),
      type: "select",
      placeholder: t("product.please_enter"),
      required: true,
      labelOptions: { label: "name", value: "id" },
      options: [
        {
          id: 0,
          name: t("common.common_yes"),
        },
        {
          id: 1,
          name: t("common.common_no"),
        },
      ],
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("product.isEmpty_required"),
        },
      ],
    },
  ];

  return formConfig;
};
