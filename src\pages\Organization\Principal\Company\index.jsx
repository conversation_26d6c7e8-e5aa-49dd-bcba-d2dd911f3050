import CustomDelete from "@c/Toast/CustomDelete";
import LayoutList from "@l/components/LayoutList";
import {
  deletePrincipa,
  getPrincipaList,
  switchDepartment,
} from "@s/api/principal";
import React from "react";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import { useTableRequest } from "../../utils.js";
import TableList from "./components/TableList";
import { removeToken, setToken } from "@/utils/auth";
import { useDispatch } from "react-redux";
import { clearUser } from "@/store/reducers/user";

const index = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const [open, setOpen] = useState(false); // 删除Branch 弹窗
  const [userId, setUserId] = useState(null);
  const [serchName, setSeachName] = useState("");
  const {
    data,
    isLoading,
    isRefetching,
    isError,
    rowCount,
    pagination,
    setPagination,
    fetchData,
    search,
    reset,
  } = useTableRequest(getPrincipaList);

  // 删除
  const handlerDetele = async () => {
    try {
      const res = await deletePrincipa(userId);
      toast.success(res?.message);
      fetchData();
    } catch {
      toast.error(t("common.deleteFailed"));
    } finally {
      setOpen(false);
    }
  };

  // 搜索
  const handlerSeacher = () => {
    search({ name: serchName });
  };

  // 清空
  const handleClear = () => {
    setSeachName("");
    reset();
  };

  const handleSwitchDepartment = (department) => {
    switchDepartment(department).then((res) => {
      dispatch(clearUser());

      // 清除所有 localStorage 数据
      localStorage.clear();

      // 清除 sessionStorage 中的所有数据
      sessionStorage.clear();
      // 清除所有 cookie
      document.cookie.split(";").forEach(function (c) {
        document.cookie = c
          .replace(/^ +/, "")
          .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });
      // 移除token
      removeToken();
      setToken(res.data.access_token);

      let resData = res.data;
      sessionStorage.setItem("tenantCode", resData.tenantCode);
      sessionStorage.setItem("loginRoleKey", resData.roleKey);
      sessionStorage.setItem("OUTLET_PRODUCT", "1");
      window.location.reload();
    });
  };

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
        setUserId={setUserId}
        setOpen={setOpen}
        switchDepartment={handleSwitchDepartment}
        getTableData={fetchData}
      ></TableList>
    );
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("principal.title")}
        isSearch={true}
        onClick={handlerSeacher}
        serchName={serchName}
        setSeachName={setSeachName}
        onClear={handleClear} // 添加 onClear 属性
        content={rederTable()}
      ></LayoutList>

      <CustomDelete
        open={open}
        setOpen={setOpen}
        handlerDetele={handlerDetele}
        content={t("principal.delete_content")}
        onContent={t("principal.delete_content_on")}
        noDelete={false}
      ></CustomDelete>
    </React.Fragment>
  );
};

export default index;
