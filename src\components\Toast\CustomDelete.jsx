import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bootstrap<PERSON>ontent,
  <PERSON>trap<PERSON><PERSON><PERSON>,
  BootstrapDialogTitle,
} from "@c/dialog";
import { useTranslation } from "react-i18next";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@/assets/Icons/CloseIcon.svg?react";
import { Grid } from "@mui/material";

function DeteleBranch(props) {
  const { t } = useTranslation();
  const {
    open,
    setOpen,
    title = t("common.common_delete_confirm"),
    handlerDetele,
    content,
    noDelete,
    onContent,
    component,
  } = props;

  return (
    <React.Fragment>
      <BootstrapDialog
        open={open}
        onClose={() => setOpen(false)}
        aria-describedby="alert-dialog-slide-description"
        sx={{
          "& .MuiPaper-root": {
            borderRadius: "10px",
          },
        }}>
        <BootstrapDialogTitle>
          <Typography
            sx={{
              font: `normal normal bold 18px/22px Proxima Nova`,
              color: "#474B4F",
              pl: 2,
            }}>
            {title}
          </Typography>

          <IconButton
            aria-label="close"
            onClick={() => setOpen(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              width: 40,
              height: 40,
              color: (theme) => theme.palette.grey[500],
            }}>
            <CloseIcon />
          </IconButton>
        </BootstrapDialogTitle>
        <BootstrapContent
          dividers={true}
          sx={{
            borderBottom: "none",
            fontSize: "18px",
          }}>
          {component ? (
            component
          ) : (
            <Grid
              sx={{
                font: "normal normal normal 14px/18px Proxima Nova",
                color: "#474B4F",
                lineHeight: "30px",
                pl: 2,
              }}>
              <span
                dangerouslySetInnerHTML={{
                  __html: noDelete ? onContent : content,
                }}></span>
            </Grid>
          )}
        </BootstrapContent>
        <BootstrapActions
          dividers={true}
          sx={{
            justifyContent: "center",
          }}>
          {noDelete ? (
            <Grid>
              <Button
                disableElevation
                type="submit"
                variant="contained"
                size="medium"
                style={{
                  width: "180px",
                  height: "60px",
                  borderRadius: "8px",
                  opacity: 1,
                  background:
                    "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                }}
                onClick={() => setOpen(false)}>
                {t("Ok")}
              </Button>
            </Grid>
          ) : (
            <Stack direction="row" spacing={2}>
              <Button
                variant="outlined"
                onClick={() => setOpen(false)}
                style={{
                  width: "180px",
                  height: "60px",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                }}
                disableElevation
                color="info"
                size="medium">
                {t("common.common_edit_cancel")}
              </Button>
              <Button
                disableElevation
                type="submit"
                variant="contained"
                size="medium"
                style={{
                  width: "180px",
                  height: "60px",
                  borderRadius: "8px",

                  background:
                    "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                }}
                onClick={() => handlerDetele()}>
                {t("common.common_delete")}
              </Button>
            </Stack>
          )}
        </BootstrapActions>
      </BootstrapDialog>
    </React.Fragment>
  );
}

export default DeteleBranch;
