import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useFormik } from "formik";
import { toast } from "react-toastify";
import { Typo<PERSON>, <PERSON><PERSON> } from "@mui/material";
import { VpnKey } from "@mui/icons-material";

// 组件
import CustomInput from "@c/CustInput.jsx";
import CustomePhoneFiled from "@c/CustomePhoneFiled";
import SubmitButton from "@c/SubmitButton";
import AvatarUploader from "@/components/AvatarUploader";
import PasswordChangeDialog from "@/components/PasswordChange/PasswordChangeDialog";

// 服务和工具
import { RespCode } from "@/enums/RespCode.js";
import {
  changeUserInfo,
  changeUserPassword,
  verifyPassword,
} from "@/service/api/user.js";
import { useStateUserInfo } from "@/hooks/user.js";

function UserProfileMenu() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const userInfo = useStateUserInfo();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [fileUrl, setFileUrl] = useState("");
  const [isRemove, setIsRemove] = useState("0");
  const [dialogOpen, setDialogOpen] = useState(false);

  // 处理头像上传
  const handleUpload = (file) => {
    setFileUrl(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setImageUrl(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  // 表单处理
  const formik = useFormik({
    initialValues: {
      email: "",
      firstName: "",
      lastName: "",
      countryCode: "",
      phone: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        setLoading(true);

        const formData = new FormData();
        formData.append("multipartFile", fileUrl);
        formData.append("firstName", values.firstName);
        formData.append("lastName", values.lastName);
        formData.append("email", values.email);
        formData.append("countryCode", values.countryCode);
        formData.append("phone", values.phone);

        if (isRemove === "1") {
          formData.append("isRemove", isRemove);
        }

        const res = await changeUserInfo(formData);

        if (res?.code === RespCode.SUCCESS) {
          toast.success(t("common.update_success"));
          navigate("/menuItem/userprofile");
        } else {
          toast.error(res?.message || t("common.update_failed"));
        }
      } catch (err) {
        setErrors({ submit: err.message });
        toast.error(t("common.update_failed"));
      } finally {
        setStatus({ success: false });
        setLoading(false);
        setSubmitting(false);
      }
    },
  });

  // 加载用户数据
  useEffect(() => {
    if (userInfo) {
      formik.setFieldValue("firstName", userInfo.firstName || "");
      formik.setFieldValue("lastName", userInfo.lastName || "");
      formik.setFieldValue("email", userInfo.email || "");
      formik.setFieldValue("countryCode", userInfo.countryCode || "");
      formik.setFieldValue("phone", userInfo.phone || "");
      setImageUrl(userInfo.photo || "");
    }
  }, [userInfo]);

  // 处理头像移除
  const handleRemove = () => {
    setIsRemove("1");
  };

  return (
    <div className="w-full h-full flex-col flex gap-2">
      {/* 页面标题 */}
      <div className="sticky top-0 z-50 h-[80px] bg-white backdrop-blur-sm border-b border-neutral-200 shadow-sm rounded-xl">
        <Typography
          variant="h4"
          sx={{
            lineHeight: "80px",
            marginLeft: "30px",
          }}>
          {t("common.my_profile")}
        </Typography>
      </div>

      {/* 主内容区 */}
      <div
        className="flex-1 bg-white shadow-md rounded-2xl"
        style={{
          marginTop: "20px",
        }}>
        <form noValidate onSubmit={formik.handleSubmit}>
          {/* 头像上传区 */}
          <div className="p-6 mb-2">
            <AvatarUploader
              imageUrl={imageUrl}
              setImageUrl={setImageUrl}
              onRemove={handleRemove}
              handleUpload={handleUpload}
            />
          </div>

          {/* 基本信息区 */}
          <div className="p-6 mb-1">
            <h2 className="text-lg font-semibold mb-4">
              {t("common.common_base_info")}
            </h2>

            <div className="grid grid-cols-1 mb-2 md:grid-cols-2 gap-4">
              <CustomInput
                required
                name="firstName"
                formik={formik}
                label={t("branch_user.firstName")}
                placeholder={t("branch_user.enter_firstName")}
              />

              <CustomInput
                required
                name="lastName"
                formik={formik}
                label={t("branch_user.lastName")}
                placeholder={t("branch_user.enter_lastName")}
              />
            </div>

            <div className="mb-2">
              <CustomInput
                required
                className="bg-gray-50"
                name="email"
                formik={formik}
                label={t("common.common_email")}
                disabled={true}
                placeholder={t("common.enter_email")}
              />
            </div>

            <div>
              <CustomePhoneFiled
                label={t("common.common_mobile")}
                placeholder={t("common.enter_mobile")}
                name="phone"
                size="small"
                className="bg-gray-50"
                sx={{ height: "40px" }}
                formik={formik}
                required
                disabled={true}
                countryCode={formik.values.countryCode}
                value={formik.values.phone}
                handleCountryCode={(e) =>
                  formik.setFieldValue("countryCode", e.dialCode)
                }
                handleChange={(e) =>
                  formik.setFieldValue("phone", e.target.value)
                }
              />
            </div>
          </div>

          {/* 安全设置区 */}
          <div className="p-6 mb-1">
            <h2 className="text-lg font-semibold mb-4">
              {t("common.common_security")}
            </h2>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-3">
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<VpnKey />}
                  onClick={() => setDialogOpen(true)}
                  sx={{
                    py: 1.5,
                    px: 3,
                    borderRadius: 2,
                    textTransform: "none",
                    fontSize: "1rem",
                  }}>
                  {t("common.common_change_password")}
                </Button>
              </div>
            </div>
          </div>

          {/* 提交按钮区 */}
          <div className="px-6 py-4 flex justify-end space-x-3">
            <SubmitButton
              formik={formik}
              loading={loading}
              callbackRoute={-1}
            />
          </div>
        </form>
      </div>

      {/* 密码修改对话框 */}
      <PasswordChangeDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        title={t("common.common_change_password")}
        currentPasswordLabel={t("common.current_password")}
        newPasswordLabel={t("common.new_password")}
        confirmPasswordLabel={t("common.common_confirm_password")}
      />
    </div>
  );
}

export default UserProfileMenu;
