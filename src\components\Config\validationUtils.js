import * as Yup from "yup";
export const createValidation = (config, expendObj = {}) => {
  let shapeObj = {};
  config.forEach((item) => {
    let validation = item.validation;
    if (
      validation &&
      (item.conditionalRendering === undefined ||
        (item.conditionalRendering && item.conditionalRendering()))
    ) {
      let temp = Yup;
      validation.forEach((valid) => {
        let type = valid.type;
        if (type === "string" || type === "required") {
          temp = temp[type](valid.message);
        } else if (type === "number") {
          temp = temp.matches(/^\d+(\.\d+)?$/, {
            message: valid.message,
            excludeEmptyString:
              valid.excludeEmptyString === undefined
                ? true
                : valid.excludeEmptyString,
          });
        } else if (type === "positive") {
          temp = temp.positive(valid.message);
        } else if (type === "email") {
          temp = temp.email(valid.message);
        } else if (type === "min" || type === "max") {
          if (valid.value === undefined) {
            console.error("请配置" + temp[type] + "范围的值");
          } else {
            temp = temp[type](valid.value, valid.message);
          }
        } else if (type === "test") {
          temp = temp.test({
            name: valid[type],
            test(value, ctx) {
              return valid.callback(value, ctx);
            },
          });
        } else if (type === "matches") {
          temp = temp.matches(valid.matches, {
            message: valid.message,
            excludeEmptyString:
              valid.excludeEmptyString === undefined
                ? true
                : valid.excludeEmptyString,
          });
        } else if (type === "secondConfirm") {
          temp = temp.oneOf(
            [Yup.ref(valid.ref || "password"), null],
            valid.message
          );
        } else {
          console.error("当前类型还没有配置校验规则");
        }
      });
      shapeObj[item.name] = temp;
    }
  });
  let tempObj = {
    ...shapeObj,
    ...expendObj,
  };

  return Yup.object().shape(tempObj);
};
