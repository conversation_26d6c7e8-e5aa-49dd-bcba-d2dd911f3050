import React, { useState } from 'react';
import {
  Box,
  Typography,
  Collapse,
  IconButton,
  Divider
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess
} from '@mui/icons-material';

/**
 * Schema分组布局组件
 */
const SchemaGroup = ({
  title,
  description,
  children,
  collapsible = false,
  defaultExpanded = true,
  spacing = 2,
  divider = false,
  sx,
  ...props
}) => {
  const [expanded, setExpanded] = useState(defaultExpanded);

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  const content = (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: spacing,
        ...sx
      }}
      {...props}
    >
      {children}
    </Box>
  );

  return (
    <Box className="schema-form-group">
      {(title || description) && (
        <Box sx={{ mb: 2 }}>
          {title && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                mb: description ? 1 : 0
              }}
            >
              <Typography variant="h6" component="h3" sx={{ flexGrow: 1 }}>
                {title}
              </Typography>
              {collapsible && (
                <IconButton
                  size="small"
                  onClick={handleToggle}
                  aria-expanded={expanded}
                  aria-label={expanded ? 'Collapse' : 'Expand'}
                >
                  {expanded ? <ExpandLess /> : <ExpandMore />}
                </IconButton>
              )}
            </Box>
          )}
          
          {description && (
            <Typography
              variant="body2"
              color="text.secondary"
            >
              {description}
            </Typography>
          )}
          
          {divider && <Divider sx={{ mt: 1 }} />}
        </Box>
      )}
      
      {collapsible ? (
        <Collapse in={expanded} timeout="auto" unmountOnExit>
          {content}
        </Collapse>
      ) : (
        content
      )}
    </Box>
  );
};

export default SchemaGroup;
