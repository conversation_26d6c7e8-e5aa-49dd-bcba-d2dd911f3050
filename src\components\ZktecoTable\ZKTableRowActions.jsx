import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, Box } from "@mui/material";
import ViewIcon from "@/assets/Icons/Viewicon.svg?react";
import EditorIcon from "@/assets/Icons/EditorIcon.svg?react";
import UserSettingIcon from "@/assets/Icons/UserSetting.svg?react";
import HandoverIcon from "@/assets/Icons/HandoverIcon.svg?react";
import UnionIcon from "@/assets/Icons/UnionIcon.svg?react";
import DeleteIcon from "@/assets/Icons/DeteleIcon.svg?react";
import SendEmail from "@/assets/Icons/sendEmail.svg?react";
import AuthButton from "../AuthButton";
import { useTranslation } from "react-i18next";

const iconStyle = { fontSize: 12, width: 20, height: 20 };
const actionBtnStyle = { minWidth: "40px" };

const ZKTableRowActions = ({
  row,
  renderRowActions,
  isShowAction = {},
  handlers = {},
}) => {
  const { t } = useTranslation();
  const isDefault = row.original.isDefault == "1";
  const isShowHandover = row.original.hasPassword;
  const actions = [
    {
      show: isDefault ? false : isShowAction.isShowView,
      cb: handlers.handlerView,
      icon: <ViewIcon style={{ width: 26, height: 26 }} />,
      title: "common.common_view",
    },
    {
      show: isDefault ? false : isShowAction.isShowEditor,
      cb: handlers.handlerEditor,
      icon: <EditorIcon style={iconStyle} />,
      title: "common.common_editor",
    },
    {
      show: isDefault ? false : isShowAction.isShowUserSetting,
      cb: handlers.handlerUserSetting,
      icon: <UserSettingIcon style={iconStyle} />,
      title: "common.common_user_setting",
    },
    {
      show: isShowHandover ? isShowAction.isShowHandover : false,
      cb: handlers.handlerHandover,
      icon: <HandoverIcon style={{ width: 18, height: 18 }} />,
      title: "common.common_handover",
    },
    {
      show: isShowAction.isShowUnion,
      cb: handlers.handlerUnion,
      icon: <UnionIcon style={{ width: 18, height: 18 }} />,
      title: "common.common_union",
    },
    {
      show: isDefault ? false : isShowAction.isShowDetele,
      cb: handlers.Detele,
      icon: <DeleteIcon style={iconStyle} />,
      title: "common.common_delete",
    },
    {
      show: isShowAction.isShowSendEamil,
      cb: handlers.handlerSendEmail,
      icon: <SendEmail style={iconStyle} />,
      title: "common.common_send_email",
    },
  ];

  return (
    <Grid sx={{ display: "flex", justifyContent: "center" }}>
      {renderRowActions && <Box>{renderRowActions({ row })}</Box>}
      {actions.map((btn, i) => {
        return (
          <AuthButton key={i} button={btn.show}>
            <Tooltip title={t(btn.title)}>
              <Button sx={actionBtnStyle} onClick={() => btn.cb(row.original)}>
                {btn.icon}
              </Button>
            </Tooltip>
          </AuthButton>
        );
      })}
    </Grid>
  );
};

export default ZKTableRowActions;
