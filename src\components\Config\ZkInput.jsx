import {
  FormHelperText,
  InputLabel,
  OutlinedInput,
  IconButton,
  Stack,
  InputAdornment,
} from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import RequirePoint from "../RequirePoint";
import { useState } from "react";
import SvgIcon from "@c/SvgIcon.jsx";
import { pxToRem } from "@u/zkUtils";
import { useTranslation } from "react-i18next";
const ZkInput = (props) => {
  const { t } = useTranslation();
  const {
    formik = null,
    label,
    placeholder = t("common.common_enter") + `${label}`,
    handleBlur,
    handleChange,
    name,
    error,
    disabled = false,
    isClear = true,
    labelpostion,
    viewPwd = false,
    type = "text",
    ...orther
  } = props;

  const [showPassword, setShowPassword] = useState(false);

  const [inputType, setInputType] = useState(type);

  const blurFn = (e) => {
    if (formik?.handleBlur) {
      formik?.handleBlur(e);
    }

    if (handleBlur) {
      handleBlur(e);
    }
  };

  const changeFn = (e) => {
    if (formik?.handleChange) {
      formik?.handleChange(e);
    }
    if (handleChange) {
      handleChange(e);
    }
  };

  const handleClickShowPassword = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
    setInputType((prevType) => (prevType == "password" ? "text" : "password"));
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
    setShowPassword(false);
    setInputType("password");
  };

  return (
    <Stack spacing={1}>
      <Stack
        direction={labelpostion === "left" ? "row" : "column"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={1}>
        {label && (
          <InputLabel
            style={{
              marginTop: labelpostion === "left" ? "12px" : "",
              color: "#474b4fcc",
              fontSize: "14px",
            }}
            htmlFor={"zkInput_" + name}>
            {label} {props.required && <RequirePoint></RequirePoint>}
          </InputLabel>
        )}

        <Stack
          sx={{
            flexGrow: 1,
            width: "100%",
          }}>
          <OutlinedInput
            id={"zkInput_" + name}
            type={inputType}
            autoComplete="off"
            value={formik?.values[name] || ""}
            name={name}
            onBlur={blurFn}
            onChange={changeFn}
            placeholder={placeholder}
            fullWidth
            error={Boolean(
              (formik?.touched[name] && formik?.errors[name]) || error
            )}
            sx={{
              "& .MuiInputBase-input": {
                fontSize: "14px",
                height: pxToRem(25),
              },
            }}
            endAdornment={
              viewPwd && type === "password" ? (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowPassword}
                    // onMouseDown={handleMouseDownPassword}
                    edge="end"
                    size="large">
                    {showPassword ? (
                      <SvgIcon icon="material-symbols:visibility-outline-rounded"></SvgIcon>
                    ) : (
                      <SvgIcon icon="material-symbols:visibility-off-outline"></SvgIcon>
                    )}
                  </IconButton>
                </InputAdornment>
              ) : isClear ? (
                formik?.values[name] !== "" &&
                formik?.values[name] !== undefined &&
                !disabled && (
                  <IconButton
                    sx={{}}
                    onClick={(e) => {
                      if (formik) {
                        formik.setFieldValue(name, "");
                      }
                      e.stopPropagation();
                    }}>
                    <ClearIcon
                      fontSize="small"
                      sx={{
                        color: "#BEBEBE",
                        cursor: "pointer",
                      }}
                    />
                  </IconButton>
                )
              ) : null
            }
            disabled={disabled}
            {...orther}
          />
          {((formik?.touched[name] && formik?.errors[name]) || error) && (
            <FormHelperText error id={`standard-weight-helper-text-${name}`}>
              {formik?.errors[name] || error}
            </FormHelperText>
          )}
        </Stack>
      </Stack>
    </Stack>
  );
};

export default ZkInput;
