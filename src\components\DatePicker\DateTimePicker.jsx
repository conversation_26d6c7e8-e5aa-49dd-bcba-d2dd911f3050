import React, { useState, useCallback, useRef } from "react";
import {
  TextField,
  Popover,
  Box,
  InputAdornment,
  IconButton,
  Button,
  Stack,
  useTheme,
  alpha,
} from "@mui/material";
import { DateCalendar, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import {
  CalendarToday as CalendarIcon,
  Clear as ClearIcon,
  AccessTime as TimeIcon,
} from "@mui/icons-material";
import { styled } from "@mui/material/styles";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { height, width } from "@mui/system";

// 设置 dayjs 默认语言
dayjs.locale("zh-cn");

// Material-UI 风格的样式组件
const StyledPopover = styled(Popover)(({ theme }) => ({
  "& .MuiPaper-root": {
    borderRadius: theme.spacing(1),
    boxShadow:
      "0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",
    border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
    overflow: "visible",
    minWidth: "auto",
    background: theme.palette.background.paper,
    pointerEvents: "auto",
    zIndex: theme.zIndex.modal,
    width: 500,
  },
}));

const StyledDateCalendar = styled(DateCalendar)(({ theme }) => ({
  width: 280,
  height: "auto",
  margin: 0,
  padding: theme.spacing(1),
  pointerEvents: "auto",
  "& *": {
    pointerEvents: "auto",
  },
  "& .MuiPickersCalendarHeader-root": {
    paddingLeft: theme.spacing(1),
    paddingRight: theme.spacing(1),
    marginTop: 0,
    marginBottom: theme.spacing(1),
  },
  "& .MuiPickersCalendarHeader-label": {
    fontSize: "0.875rem",
    fontWeight: 500,
  },
  "& .MuiDayCalendar-header": {
    paddingLeft: theme.spacing(1),
    paddingRight: theme.spacing(1),
  },
  "& .MuiDayCalendar-weekDayLabel": {
    fontSize: "0.75rem",
    fontWeight: 400,
    color: theme.palette.text.secondary,
    width: 32,
    height: 32,
    margin: 0,
  },
  "& .MuiDayCalendar-slideTransition": {
    minHeight: 240,
  },
  "& .MuiPickersDay-root": {
    fontSize: "0.875rem",
    width: 32,
    height: 32,
    margin: "2px",
    borderRadius: theme.spacing(0.5),
    cursor: "pointer !important",
    position: "relative",
    zIndex: 1,
    pointerEvents: "auto",
    "&:hover": {
      backgroundColor: `${alpha(theme.palette.primary.main, 0.08)} !important`,
    },
    "&.Mui-selected": {
      backgroundColor: `${theme.palette.primary.main} !important`,
      color: `${theme.palette.primary.contrastText} !important`,
      "&:hover": {
        backgroundColor: `${theme.palette.primary.dark} !important`,
      },
    },
    "&.MuiPickersDay-today": {
      border: `1px solid ${theme.palette.primary.main}`,
      backgroundColor: "transparent",
      "&:not(.Mui-selected)": {
        color: theme.palette.primary.main,
      },
    },
  },
}));

const StyledTimePanel = styled(Box)(({ theme }) => ({
  display: "flex",
  borderTop: `1px solid ${theme.palette.divider}`,
  "& .time-column": {
    minWidth: 73, // 设置最小宽度确保内容完整显示
    width: "auto", // 自适应宽度
    height: "100%",
    borderRight: `1px solid ${theme.palette.divider}`,
    "&:last-child": {
      borderRight: "none",
    },
  },
  "& .time-column-header": {
    padding: theme.spacing(1),
    textAlign: "center",
    fontSize: "0.75rem",
    fontWeight: 500,
    color: theme.palette.text.secondary,
    borderBottom: `1px solid ${theme.palette.divider}`,
    backgroundColor: alpha(theme.palette.background.default, 0.5),
  },
  "& .time-list": {
    height: 280,
    overflowY: "auto",
    "&::-webkit-scrollbar": {
      width: 6,
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: alpha(theme.palette.text.secondary, 0.3),
      borderRadius: 3,
    },
  },
  "& .time-item": {
    padding: theme.spacing(0.5, 0.75), // 减少左右内边距
    textAlign: "center",
    fontSize: "0.875rem",
    cursor: "pointer",
    minHeight: 32, // 设置最小高度
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    transition: theme.transitions.create(["background-color"], {
      duration: theme.transitions.duration.shortest,
    }),
    "&:hover": {
      backgroundColor: alpha(theme.palette.primary.main, 0.08),
    },
    "&.selected": {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
    },
  },
}));

/**
 * DateTimePicker 组件 - 既可以选择年月日又能选择时分秒的时间组件
 */
const DateTimePicker = ({
  // 基础属性
  value,
  defaultValue,
  onChange,
  onOk,
  onOpenChange,

  // 显示相关
  placeholder = "请选择日期时间",
  size = "medium",
  variant = "outlined",
  disabled = false,
  allowClear = true,
  autoFocus = false,

  // 格式化
  format = "YYYY-MM-DD HH:mm:ss",

  // 下拉框相关
  open,
  defaultOpen = false,
  placement = "bottomLeft",

  // 日期限制
  disabledDate,
  minDate,
  maxDate,

  // 其他
  className,
  style,
  needConfirm = true,

  ...restProps
}) => {
  // 状态管理
  const [internalOpen, setInternalOpen] = useState(defaultOpen);
  const [internalValue, setInternalValue] = useState(defaultValue);
  const [anchorEl, setAnchorEl] = useState(null);
  const [tempValue, setTempValue] = useState(null);

  const inputRef = useRef(null);
  const theme = useTheme();

  // 受控状态处理
  const isControlledOpen = open !== undefined;
  const isControlledValue = value !== undefined;

  const currentOpen = isControlledOpen ? open : internalOpen;
  const currentValue = isControlledValue ? value : internalValue;

  // 处理打开状态变化
  const handleOpenChange = useCallback(
    (newOpen) => {
      if (!isControlledOpen) {
        setInternalOpen(newOpen);
      }

      if (onOpenChange) {
        onOpenChange(newOpen);
      }
    },
    [isControlledOpen, onOpenChange]
  );

  // 处理值变化
  const handleValueChange = useCallback(
    (newValue, triggerEvent = "change") => {
      const formattedValue = newValue ? dayjs(newValue) : null;
      const dateString = formattedValue ? formattedValue.format(format) : "";

      if (!isControlledValue) {
        setInternalValue(formattedValue);
      }

      if (onChange) {
        onChange(formattedValue, dateString);
      }
    },
    [isControlledValue, onChange, format]
  );

  // 处理确认
  const handleOk = useCallback(() => {
    if (tempValue !== null) {
      handleValueChange(tempValue, "ok");
      setTempValue(null);
    }
    handleOpenChange(false);

    if (onOk) {
      onOk();
    }
  }, [tempValue, handleValueChange, handleOpenChange, onOk]);

  // 处理取消
  const handleCancel = useCallback(() => {
    setTempValue(null);
    handleOpenChange(false);
  }, [handleOpenChange]);

  // 处理清除
  const handleClear = useCallback(
    (e) => {
      e.stopPropagation();
      handleValueChange(null, "clear");
    },
    [handleValueChange]
  );

  // 处理输入框点击
  const handleInputClick = useCallback(
    (e) => {
      if (!disabled) {
        setAnchorEl(e.currentTarget);
        handleOpenChange(true);
      }
    },
    [disabled, handleOpenChange]
  );

  // 获取显示文本
  const getDisplayText = useCallback(() => {
    if (!currentValue) return "";
    return dayjs(currentValue).format(format);
  }, [currentValue, format]);

  // 获取输入框尺寸
  const getInputSize = () => {
    switch (size) {
      case "large":
        return "medium";
      case "small":
        return "small";
      default:
        return "medium";
    }
  };

  // 判断日期是否禁用
  const isDateDisabled = useCallback(
    (date) => {
      if (disabledDate) {
        return disabledDate(dayjs(date));
      }

      if (minDate && dayjs(date).isBefore(dayjs(minDate), "day")) {
        return true;
      }

      if (maxDate && dayjs(date).isAfter(dayjs(maxDate), "day")) {
        return true;
      }

      return false;
    },
    [disabledDate, minDate, maxDate]
  );

  // 生成时间选项
  const generateTimeOptions = useCallback((type) => {
    const options = [];
    let max = 24;

    if (type === "hour") {
      max = 24;
    } else if (type === "minute" || type === "second") {
      max = 60;
    }

    for (let i = 0; i < max; i++) {
      const value = i.toString().padStart(2, "0");
      options.push({ value: i, label: value });
    }

    return options;
  }, []);

  // 渲染时间选择面板
  const renderTimePanel = () => {
    const timeValue = needConfirm ? tempValue || currentValue : currentValue;
    const currentTime = timeValue ? dayjs(timeValue) : dayjs();

    const hours = generateTimeOptions("hour");
    const minutes = generateTimeOptions("minute");
    const seconds = generateTimeOptions("second");

    const handleTimeChange = (type, value) => {
      let newTime = currentTime.clone();

      if (type === "hour") {
        newTime = newTime.hour(value);
      } else if (type === "minute") {
        newTime = newTime.minute(value);
      } else if (type === "second") {
        newTime = newTime.second(value);
      }

      if (needConfirm) {
        setTempValue(newTime);
      } else {
        handleValueChange(newTime, "select");
      }
    };

    return (
      <Box sx={{ minWidth: "auto" }}>
        {" "}
        {/* 自适应宽度 */}
        <StyledTimePanel>
          {/* 小时列 */}
          <Box className="time-column">
            <Box className="time-column-header">时</Box>
            <Box className="time-list">
              {hours.map((hour) => (
                <Box
                  key={hour.value}
                  className={`time-item ${
                    currentTime.hour() === hour.value ? "selected" : ""
                  }`}
                  onClick={() => handleTimeChange("hour", hour.value)}>
                  {hour.label}
                </Box>
              ))}
            </Box>
          </Box>

          {/* 分钟列 */}
          <Box className="time-column">
            <Box className="time-column-header">分</Box>
            <Box className="time-list">
              {minutes.map((minute) => (
                <Box
                  key={minute.value}
                  className={`time-item ${
                    currentTime.minute() === minute.value ? "selected" : ""
                  }`}
                  onClick={() => handleTimeChange("minute", minute.value)}>
                  {minute.label}
                </Box>
              ))}
            </Box>
          </Box>

          {/* 秒列 */}
          <Box className="time-column">
            <Box className="time-column-header">秒</Box>
            <Box className="time-list">
              {seconds.map((second) => (
                <Box
                  key={second.value}
                  className={`time-item ${
                    currentTime.second() === second.value ? "selected" : ""
                  }`}
                  onClick={() => handleTimeChange("second", second.value)}>
                  {second.label}
                </Box>
              ))}
            </Box>
          </Box>
        </StyledTimePanel>
      </Box>
    );
  };

  // 渲染日期时间面板
  const renderDateTimePanel = () => {
    const dateTimeValue = needConfirm
      ? tempValue || currentValue
      : currentValue;

    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          minWidth: "auto", // 自适应宽度
          position: "relative",
        }}>
        {/* 主要内容区域 */}
        <Box sx={{ display: "flex", flexDirection: "row" }}>
          {/* 日期选择部分 */}
          <Box sx={{ width: 280 }}>
            <LocalizationProvider
              dateAdapter={AdapterDayjs}
              adapterLocale="zh-cn">
              <StyledDateCalendar
                value={dateTimeValue}
                onChange={(newValue) => {
                  if (needConfirm) {
                    setTempValue(newValue);
                  } else {
                    handleValueChange(newValue, "select");
                  }
                }}
                shouldDisableDate={isDateDisabled}
                views={["year", "month", "day"]}
                openTo="day"
                displayWeekNumber={false}
                fixedWeekNumber={6}
              />
            </LocalizationProvider>
          </Box>

          {/* 时间选择部分 */}
          <Box
            sx={{
              minWidth: "auto", // 自适应宽度
              borderLeft: `1px solid ${theme.palette.divider}`,
            }}>
            {renderTimePanel()}
          </Box>
        </Box>

        {/* 确认按钮 */}
        {needConfirm && (
          <Box
            sx={{
              p: 1,
              borderTop: `1px solid ${theme.palette.divider}`,
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
              backgroundColor: theme.palette.background.paper,
            }}>
            <Button size="small" onClick={handleCancel}>
              取消
            </Button>
            <Button size="small" variant="contained" onClick={handleOk}>
              确定
            </Button>
          </Box>
        )}
      </Box>
    );
  };

  return (
    <Box className={className} style={style}>
      {/* 输入框 */}
      <TextField
        ref={inputRef}
        value={getDisplayText()}
        placeholder={placeholder}
        size={getInputSize()}
        variant={variant}
        disabled={disabled}
        autoFocus={autoFocus}
        fullWidth
        onClick={handleInputClick}
        InputProps={{
          readOnly: true,
          style: { cursor: disabled ? "default" : "pointer" },
          endAdornment: (
            <InputAdornment position="end">
              {allowClear && currentValue && !disabled && (
                <IconButton size="small" onClick={handleClear} sx={{ mr: 0.5 }}>
                  <ClearIcon fontSize="small" />
                </IconButton>
              )}
              <Stack direction="row" spacing={0.5}>
                <CalendarIcon fontSize="small" />
                {/* <TimeIcon fontSize="small" /> */}
              </Stack>
            </InputAdornment>
          ),
        }}
        {...restProps}
      />

      {/* 下拉日期时间面板 */}
      <StyledPopover
        open={currentOpen}
        anchorEl={anchorEl}
        onClose={() => {
          setAnchorEl(null);
          handleOpenChange(false);
        }}
        anchorOrigin={{
          vertical: placement.includes("top") ? "top" : "bottom",
          horizontal: placement.includes("Right") ? "right" : "left",
        }}
        transformOrigin={{
          vertical: placement.includes("top") ? "bottom" : "top",
          horizontal: placement.includes("Right") ? "right" : "left",
        }}
        slotProps={{
          paper: {
            style: {
              pointerEvents: "auto",
              position: "relative",
            },
          },
        }}
        sx={{
          "& .MuiPopover-paper": {
            pointerEvents: "auto",
          },
        }}>
        {renderDateTimePanel()}
      </StyledPopover>
    </Box>
  );
};

export default DateTimePicker;
