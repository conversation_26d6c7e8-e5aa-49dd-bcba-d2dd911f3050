import SchemaInput from './fields/SchemaInput';
import SchemaSelect from './fields/SchemaSelect';
import SchemaCheckbox from './fields/SchemaCheckbox';
import SchemaRadio from './fields/SchemaRadio';
import SchemaDatePicker from './fields/SchemaDatePicker';
import SchemaTextarea from './fields/SchemaTextarea';

/**
 * Schema字段注册表
 * 负责管理和提供各种字段组件的注册与获取
 */
class SchemaFieldRegistry {
  constructor() {
    this.fields = new Map();
    this.widgets = new Map();
    this.validators = new Map();
    this.formatters = new Map();
    
    // 注册默认字段组件
    this.registerDefaultFields();
  }

  /**
   * 注册默认字段组件
   */
  registerDefaultFields() {
    // 基础输入类型
    this.registerField('string', SchemaInput);
    this.registerField('number', SchemaInput);
    this.registerField('integer', SchemaInput);
    this.registerField('email', SchemaInput);
    this.registerField('password', SchemaInput);
    this.registerField('url', SchemaInput);
    this.registerField('tel', SchemaInput);
    
    // 文本区域
    this.registerField('text', SchemaTextarea);
    
    // 选择类型
    this.registerField('select', SchemaSelect);
    this.registerField('enum', SchemaSelect);
    
    // 布尔类型
    this.registerField('boolean', SchemaCheckbox);
    this.registerField('checkbox', SchemaCheckbox);
    
    // 单选类型
    this.registerField('radio', SchemaRadio);
    
    // 日期时间类型
    this.registerField('date', SchemaDatePicker);
    this.registerField('datetime', SchemaDatePicker);
    this.registerField('time', SchemaDatePicker);

    // 注册Widget变体
    this.registerWidget('string', 'textarea', SchemaTextarea);
    this.registerWidget('string', 'select', SchemaSelect);
    this.registerWidget('string', 'radio', SchemaRadio);
    this.registerWidget('boolean', 'switch', SchemaCheckbox);
    this.registerWidget('number', 'range', SchemaInput);
  }

  /**
   * 注册字段组件
   * @param {string} type - 字段类型
   * @param {React.Component} component - 组件
   * @param {Object} options - 选项
   */
  registerField(type, component, options = {}) {
    if (!type || !component) {
      throw new Error('Type and component are required for field registration');
    }
    
    this.fields.set(type, {
      component,
      options,
      registeredAt: new Date()
    });
    
    return this;
  }

  /**
   * 注册Widget变体
   * @param {string} type - 基础类型
   * @param {string} widget - Widget名称
   * @param {React.Component} component - 组件
   * @param {Object} options - 选项
   */
  registerWidget(type, widget, component, options = {}) {
    if (!type || !widget || !component) {
      throw new Error('Type, widget and component are required for widget registration');
    }
    
    const key = `${type}:${widget}`;
    this.widgets.set(key, {
      component,
      type,
      widget,
      options,
      registeredAt: new Date()
    });
    
    return this;
  }

  /**
   * 注册验证器
   * @param {string} name - 验证器名称
   * @param {Function} validator - 验证函数
   */
  registerValidator(name, validator) {
    if (!name || typeof validator !== 'function') {
      throw new Error('Name and validator function are required');
    }
    
    this.validators.set(name, validator);
    return this;
  }

  /**
   * 注册格式化器
   * @param {string} name - 格式化器名称
   * @param {Function} formatter - 格式化函数
   */
  registerFormatter(name, formatter) {
    if (!name || typeof formatter !== 'function') {
      throw new Error('Name and formatter function are required');
    }
    
    this.formatters.set(name, formatter);
    return this;
  }

  /**
   * 获取字段组件
   * @param {string} type - 字段类型
   * @param {string} widget - Widget名称（可选）
   * @returns {React.Component|null}
   */
  getField(type, widget = null) {
    // 优先查找Widget变体
    if (widget) {
      const widgetKey = `${type}:${widget}`;
      const widgetEntry = this.widgets.get(widgetKey);
      if (widgetEntry) {
        return widgetEntry.component;
      }
    }
    
    // 查找基础类型
    const fieldEntry = this.fields.get(type);
    if (fieldEntry) {
      return fieldEntry.component;
    }
    
    // 尝试查找通用类型
    if (type !== 'string') {
      return this.getField('string');
    }
    
    return null;
  }

  /**
   * 获取验证器
   * @param {string} name - 验证器名称
   * @returns {Function|null}
   */
  getValidator(name) {
    return this.validators.get(name) || null;
  }

  /**
   * 获取格式化器
   * @param {string} name - 格式化器名称
   * @returns {Function|null}
   */
  getFormatter(name) {
    return this.formatters.get(name) || null;
  }

  /**
   * 检查字段类型是否已注册
   * @param {string} type - 字段类型
   * @param {string} widget - Widget名称（可选）
   * @returns {boolean}
   */
  hasField(type, widget = null) {
    if (widget) {
      return this.widgets.has(`${type}:${widget}`);
    }
    return this.fields.has(type);
  }

  /**
   * 获取所有已注册的字段类型
   * @returns {Array<string>}
   */
  getRegisteredTypes() {
    return Array.from(this.fields.keys());
  }

  /**
   * 获取所有已注册的Widget
   * @returns {Array<Object>}
   */
  getRegisteredWidgets() {
    return Array.from(this.widgets.entries()).map(([key, value]) => ({
      key,
      type: value.type,
      widget: value.widget,
      component: value.component
    }));
  }

  /**
   * 合并另一个注册表
   * @param {SchemaFieldRegistry} otherRegistry - 另一个注册表
   * @returns {SchemaFieldRegistry}
   */
  merge(otherRegistry) {
    if (!(otherRegistry instanceof SchemaFieldRegistry)) {
      throw new Error('Can only merge with another SchemaFieldRegistry instance');
    }
    
    const newRegistry = new SchemaFieldRegistry();
    
    // 复制当前注册表的内容
    this.fields.forEach((value, key) => {
      newRegistry.fields.set(key, value);
    });
    this.widgets.forEach((value, key) => {
      newRegistry.widgets.set(key, value);
    });
    this.validators.forEach((value, key) => {
      newRegistry.validators.set(key, value);
    });
    this.formatters.forEach((value, key) => {
      newRegistry.formatters.set(key, value);
    });
    
    // 合并另一个注册表的内容（会覆盖重复的键）
    otherRegistry.fields.forEach((value, key) => {
      newRegistry.fields.set(key, value);
    });
    otherRegistry.widgets.forEach((value, key) => {
      newRegistry.widgets.set(key, value);
    });
    otherRegistry.validators.forEach((value, key) => {
      newRegistry.validators.set(key, value);
    });
    otherRegistry.formatters.forEach((value, key) => {
      newRegistry.formatters.set(key, value);
    });
    
    return newRegistry;
  }

  /**
   * 清空注册表
   */
  clear() {
    this.fields.clear();
    this.widgets.clear();
    this.validators.clear();
    this.formatters.clear();
  }

  /**
   * 克隆注册表
   * @returns {SchemaFieldRegistry}
   */
  clone() {
    const newRegistry = new SchemaFieldRegistry();
    newRegistry.clear(); // 清空默认注册的字段
    
    this.fields.forEach((value, key) => {
      newRegistry.fields.set(key, { ...value });
    });
    this.widgets.forEach((value, key) => {
      newRegistry.widgets.set(key, { ...value });
    });
    this.validators.forEach((value, key) => {
      newRegistry.validators.set(key, value);
    });
    this.formatters.forEach((value, key) => {
      newRegistry.formatters.set(key, value);
    });
    
    return newRegistry;
  }
}

export default SchemaFieldRegistry;
