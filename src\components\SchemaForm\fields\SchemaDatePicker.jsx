import React, { useCallback } from 'react';
import {
  TextField,
  Box,
  Typography
} from '@mui/material';
import { DatePicker, TimePicker, DateTimePicker } from '@mui/x-date-pickers';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

/**
 * Schema驱动的日期时间选择器组件
 */
const SchemaDatePicker = ({
  name,
  path,
  schema,
  uiSchema = {},
  value,
  error,
  touched,
  disabled = false,
  readonly = false,
  required = false,
  onChange,
  onBlur,
  onValidate,
  formData,
  registry,
  ...props
}) => {
  const { t } = useTranslation();

  // 从schema中提取配置
  const {
    title,
    description,
    format = 'date',
    minimum,
    maximum
  } = schema;

  // 从uiSchema中提取UI配置
  const {
    label = title,
    help = description,
    variant = 'outlined',
    size = 'medium',
    fullWidth = true,
    autoFocus = false,
    clearable = true,
    showTodayButton = false,
    views,
    openTo,
    disablePast = false,
    disableFuture = false,
    shouldDisableDate,
    shouldDisableTime,
    ...uiProps
  } = uiSchema;

  // 确定使用的组件类型
  const getPickerComponent = () => {
    switch (format) {
      case 'time':
        return TimePicker;
      case 'datetime':
      case 'date-time':
        return DateTimePicker;
      case 'date':
      default:
        return DatePicker;
    }
  };

  // 转换值为dayjs对象
  const dayjsValue = value ? dayjs(value) : null;

  // 处理值变化
  const handleChange = useCallback((newValue) => {
    let formattedValue = null;
    
    if (newValue && newValue.isValid()) {
      switch (format) {
        case 'time':
          formattedValue = newValue.format('HH:mm:ss');
          break;
        case 'datetime':
        case 'date-time':
          formattedValue = newValue.toISOString();
          break;
        case 'date':
        default:
          formattedValue = newValue.format('YYYY-MM-DD');
          break;
      }
    }
    
    onChange?.(formattedValue);
  }, [onChange, format]);

  // 处理失焦
  const handleBlur = useCallback(() => {
    onBlur?.();
    
    // 执行验证
    if (onValidate) {
      const errors = [];
      
      // 必填验证
      if (required && !value) {
        errors.push(t('validation.required', { field: label || name }));
      }
      
      // 日期范围验证
      if (value) {
        const dateValue = dayjs(value);
        
        if (!dateValue.isValid()) {
          errors.push(t('validation.invalidDate', { field: label || name }));
        } else {
          // 最小日期验证
          if (minimum) {
            const minDate = dayjs(minimum);
            if (dateValue.isBefore(minDate)) {
              errors.push(t('validation.minDate', { 
                field: label || name, 
                min: minDate.format('YYYY-MM-DD') 
              }));
            }
          }
          
          // 最大日期验证
          if (maximum) {
            const maxDate = dayjs(maximum);
            if (dateValue.isAfter(maxDate)) {
              errors.push(t('validation.maxDate', { 
                field: label || name, 
                max: maxDate.format('YYYY-MM-DD') 
              }));
            }
          }
        }
      }
      
      onValidate(errors.length > 0 ? errors[0] : null);
    }
  }, [onBlur, onValidate, value, required, minimum, maximum, label, name, t]);

  // 获取最小和最大日期
  const minDate = minimum ? dayjs(minimum) : undefined;
  const maxDate = maximum ? dayjs(maximum) : undefined;

  // 选择器组件
  const PickerComponent = getPickerComponent();

  // 构建picker属性
  const pickerProps = {
    label,
    value: dayjsValue,
    onChange: handleChange,
    onClose: handleBlur,
    disabled,
    readOnly: readonly,
    minDate: disablePast ? dayjs() : minDate,
    maxDate: disableFuture ? dayjs() : maxDate,
    shouldDisableDate,
    shouldDisableTime,
    views,
    openTo,
    slotProps: {
      textField: {
        variant,
        size,
        fullWidth,
        required,
        autoFocus,
        error: Boolean(error && touched),
        helperText: (error && touched) ? error : (
          help ? (
            <Typography variant="caption" color="text.secondary">
              {help}
            </Typography>
          ) : null
        ),
        ...uiProps
      },
      actionBar: showTodayButton ? {
        actions: ['today', 'clear', 'cancel', 'accept']
      } : undefined
    },
    ...props
  };

  // 如果不可清空，移除clear action
  if (!clearable && pickerProps.slotProps?.actionBar) {
    pickerProps.slotProps.actionBar.actions = 
      pickerProps.slotProps.actionBar.actions.filter(action => action !== 'clear');
  }

  return (
    <Box className="schema-form-field">
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <PickerComponent {...pickerProps} />
      </LocalizationProvider>
    </Box>
  );
};

export default SchemaDatePicker;
