import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  BootstrapDialogTitle,
  BootstrapA<PERSON>,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
import i18n from "i18next";
function ZkDialog(props) {
  const {
    open,
    setOpen,
    children,
    title,
    handlerSubmit,
    buttonText = i18n.t("common.common_save"),
  } = props;
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <BootstrapDialog
        fullWidth
        maxWidth="xl"
        onClose={() => setOpen(false)}
        aria-labelledby="customized-dialog-title"
        open={open}>
        <BootstrapDialogTitle
          id="customized-dialog-title"
          onClose={() => setOpen(false)}>
          <Typography variant="h4" component="p">
            {title}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent>{children}</BootstrapContent>
        <BootstrapActions>
          <Grid container xs={12}>
            <Grid item display={"flex"} xs={12} justifyContent={"flex-end"}>
              <Button
                style={{
                  width: "236px",
                  height: "50px",
                  background: `#FFFFFF 0% 0% no-repeat padding-box`,
                  borderRadius: "5px",
                  border: `1px solid #E0E0E0`,
                  marginRight: "15px",
                }}
                onClick={() => setOpen(false)}>
                {t("common.common_edit_cancel")}
              </Button>
              <Button
                style={{
                  width: "236px",
                  height: "50px",
                  color: "#fff",
                  background: `transparent linear-gradient(102deg, #78BC27 0%, #1487CB 100%) 0% 0% no-repeat padding-box`,
                  borderRadius: "5px",
                  border: `1px solid #E0E0E0`,
                }}
                onClick={() => handlerSubmit()}>
                {buttonText}
              </Button>
            </Grid>
          </Grid>
        </BootstrapActions>
      </BootstrapDialog>
    </React.Fragment>
  );
}

export default ZkDialog;
