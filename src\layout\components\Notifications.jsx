import React from "react";
import Typography from "@mui/material/Typography";
import Popover from "@mui/material/Popover";
import {
  usePopupState,
  bindTrigger,
  bindPopover,
} from "material-ui-popup-state/hooks";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import Ball from "@/assets/Icons/Bell.svg?react";
import { useTranslation } from "react-i18next";
import { pxToRem } from "@/utils/zkUtils.js";
import { useTheme } from "@emotion/react";
export default function Notifications() {
  const { t } = useTranslation();
  const theme = useTheme();

  const popupState = usePopupState({
    variant: "popover",
    popupId: "demoPopover",
  });
  return (
    <div>
      <Box
        display="flex"
        alignItems={"center"}
        justifyContent={"center"}
        sx={{
          pb: 0.6,
          pt: 0.6,
        }}
        style={{ cursor: "pointer" }}
        {...bindTrigger(popupState)}>
        <Box sx={{ py: { xs: 2, md: 0.5 } }} pl={2} pr={1}>
          <Ball></Ball>
        </Box>
        <Box flexGrow={1} sx={{ display: { md: "flex" } }} ml={1}>
          <Tooltip title={"Notifications"} arrow placement="bottom">
            <Typography
              sx={{
                color: "rgba(71, 75, 79, 0.6)",
                font: `normal normal normal 14px/18px Proxima Nova`,
              }}>
              Notifications
            </Typography>
          </Tooltip>
        </Box>
        <Box py={0.5} px={2} sx={{ display: { md: "flex" } }}>
          <Typography variant="menuItem">
            {popupState.isOpen ? (
              <KeyboardArrowLeftIcon
                fontSize="small"
                style={{
                  color: `#A2A3A3`,
                }}
              />
            ) : (
              <KeyboardArrowRightIcon
                fontSize="small"
                style={{
                  color: `#A2A3A3`,
                }}
              />
            )}
          </Typography>
        </Box>
      </Box>
      <Popover
        {...bindPopover(popupState)}
        anchorOrigin={{
          vertical: "center",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "center",
          horizontal: "left",
        }}>
        <MenuItem
          onClick={() => {
            switchLanguage("en");
            popupState.close();
          }}>
          {t("通知")}
        </MenuItem>
      </Popover>
    </div>
  );
}
