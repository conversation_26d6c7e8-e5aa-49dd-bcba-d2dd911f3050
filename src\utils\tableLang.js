import i18n from 'i18next';
// TABLE 国际化
export const tableI18n = {
    actions: i18n.t('table.actions'),
    and: i18n.t('table.and'),
    cancel: i18n.t('table.cancel'),
    changeFilterMode: i18n.t('table.changeFilterMode'),
    changeSearchMode: i18n.t('table.changeSearchMode'),
    clearFilter: i18n.t('table.clearFilter'),
    clearSearch: i18n.t('table.clearSearch'),
    clearSort: i18n.t('table.clearSort'),
    clickToCopy: i18n.t('table.clickToCopy'),
    collapse: i18n.t('table.collapse'),
    collapseAll: i18n.t('table.collapseAll'),
    columnActions: i18n.t('table.columnActions'),
    copiedToClipboard: i18n.t('table.copiedToClipboard'),
    dropToGroupBy: i18n.t('table.dropToGroupBy'),
    edit: i18n.t('table.edit'),
    expand: i18n.t('table.expand'),
    expandAll: i18n.t('table.expandAll'),
    filterArrIncludes: i18n.t('table.filterArrIncludes'),
    filterArrIncludesAll: i18n.t('table.filterArrIncludesAll'),
    filterArrIncludesSome: i18n.t('table.filterArrIncludesSome'),
    filterBetween: i18n.t('table.filterBetween'),
    filterBetweenInclusive: i18n.t('table.filterBetweenInclusive'),
    filterByColumn: i18n.t('table.filterByColumn'),
    filterContains: i18n.t('table.filterContains'),
    filterEmpty: i18n.t('table.filterEmpty'),
    filterEndsWith: i18n.t('table.filterEndsWith'),
    filterEquals: i18n.t('table.filterEquals'),
    filterEqualsString: i18n.t('table.filterEqualsString'),
    filterFuzzy: i18n.t('table.filterFuzzy'),
    filterGreaterThan: i18n.t('table.filterGreaterThan'),
    filterGreaterThanOrEqualTo: i18n.t('table.filterGreaterThanOrEqualTo'),
    filterInNumberRange: i18n.t('table.filterInNumberRange'),
    filterIncludesString: i18n.t('table.filterIncludesString'),
    filterIncludesStringSensitive: i18n.t('table.filterIncludesStringSensitive'),
    filterLessThan: i18n.t('table.filterLessThan'),
    filterLessThanOrEqualTo: i18n.t('table.filterLessThanOrEqualTo'),
    filterMode: i18n.t('table.filterMode'),
    filterNotEmpty: i18n.t('table.filterNotEmpty'),
    filterNotEquals: i18n.t('table.filterNotEquals'),
    filterStartsWith: i18n.t('table.filterStartsWith'),
    filterWeakEquals: i18n.t('table.filterWeakEquals'),
    filteringByColumn: i18n.t('table.filteringByColumn'),
    goToFirstPage: i18n.t('table.goToFirstPage'),
    goToLastPage: i18n.t('table.goToLastPage'),
    goToNextPage: i18n.t('table.goToNextPage'),
    goToPreviousPage: i18n.t('table.goToPreviousPage'),
    grab: i18n.t('table.grab'),
    groupByColumn: i18n.t('table.groupByColumn'),
    groupedBy: i18n.t('table.groupByColumn'),
    hideAll: i18n.t('table.hideAll'),
    hideColumn: i18n.t('table.hideColumn'),
    max: i18n.t('table.max'),
    min: i18n.t('table.min'),
    move: i18n.t('table.move'),
    noRecordsToDisplay: i18n.t('table.noRecordsToDisplay'),
    noResultsFound: i18n.t('table.noResultsFound'),
    of: i18n.t('table.of'),
    or: i18n.t('table.or'),
    pinToLeft: i18n.t('table.pinToLeft'),
    pinToRight: i18n.t('table.pinToRight'),
    resetColumnSize: i18n.t('table.resetColumnSize'),
    resetOrder: i18n.t('table.resetOrder'),
    rowActions: i18n.t('table.rowActions'),
    rowNumber: i18n.t('table.rowNumber'),
    rowNumbers: i18n.t('table.rowNumbers'),
    rowsPerPage: i18n.t('table.rowsPerPage'),
    save: i18n.t('table.save'),
    search: i18n.t('table.search'),
    selectedCountOfRowCountRowsSelected: i18n.t('table.selectedCountOfRowCountRowsSelected'),
    select: i18n.t('table.select'),
    showAll: i18n.t('table.showAll'),
    showAllColumns: i18n.t('table.showAllColumns'),
    showHideColumns: i18n.t('table.showHideColumns'),
    showHideFilters: i18n.t('table.showHideFilters'),
    showHideSearch: i18n.t('table.showHideSearch'),
    sortByColumnAsc: i18n.t('table.sortByColumnAsc'),
    sortByColumnDesc: i18n.t('table.sortByColumnDesc'),
    sortedByColumnAsc: i18n.t('table.sortedByColumnAsc'),
    sortedByColumnDesc: i18n.t('table.sortedByColumnDesc'),
    thenBy: i18n.t('table.thenBy'),
    toggleDensity: i18n.t('table.toggleDensity'),
    toggleFullScreen: i18n.t('table.toggleFullScreen'),
    toggleSelectAll: i18n.t('table.toggleSelectAll'),
    toggleSelectRow: i18n.t('table.toggleSelectRow'),
    toggleVisibility: i18n.t('table.toggleVisibility'),
    ungroupByColumn: i18n.t('table.ungroupByColumn'),
    unpin: i18n.t('table.unpin'),
    unpinAll: i18n.t('table.unpinAll'),
    unsorted: i18n.t('table.unsorted')
};
