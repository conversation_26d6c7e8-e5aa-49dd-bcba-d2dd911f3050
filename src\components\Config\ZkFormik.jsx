import { Grid } from "@mui/material";
import RenderingFromItem from "./RenderingFromItem";

const ZkFormik = (props) => {
  const {
    formik,
    formConfig,
    labelpostion = "top",
    itemSpacing = 3,
    defaultBox,
    sx = 12,
    children,
  } = props;

  return (
    <form noValidate onSubmit={formik.handleSubmit}>
      <Grid container spacing={itemSpacing}>
        <RenderingFromItem
          sx={sx}
          labelpostion={labelpostion}
          config={formConfig}
          formik={formik}
          defaultBox={defaultBox}></RenderingFromItem>
        {children}
      </Grid>
    </form>
  );
};

export default ZkFormik;
