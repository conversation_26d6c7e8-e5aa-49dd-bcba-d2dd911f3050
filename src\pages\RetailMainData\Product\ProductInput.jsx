import React, { useEffect, useState } from "react";
import { Box, InputLabel, TextField } from "@mui/material";
import { useTranslation } from "react-i18next";

export default function CustomInput(props) {
  const [helperText, setHelperText] = useState(props.helperText);
  const [error, setError] = useState(props.error);
  const { t } = useTranslation();

  useEffect(() => {
    setError(props.error);
    setHelperText(props.helperText);
  }, [props.error]);

  const preventHandleBlur = [
    "email",
    "superAdminEmail",
    "password",
    "confirmPassword",
    "oldPassword",
    "newPassword",
    "date",
  ];
  const handleBlur = () => {
    if (!preventHandleBlur.includes(props.name)) {
      setHelperText("");
      setError(false);
      props.resetError();
    }
  };

  return (
    <Box letiant="standard" style={{ width: "100%" }} pb={1}>
      <InputLabel
        shrink
        htmlFor="bootstrap-input"
        style={{ paddingLeft: "0px", fontSize: "18px" }}>
        {props.label}
        {props.required && <span style={{ color: "red" }}>*</span>}
      </InputLabel>
      <TextField
        {...props}
        autoComplete="new-password"
        disableMaskedInput={true}
        fullWidth
        sx={{
          borderRadius: "10px",
          color: "#474B4F",
          opacity: "0.8",
          fontSize: "16px",
          fontFamily: "Roboto",
          "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall": {
            fontSize: "14px",
            padding: "12px",
          },

          "& label.Mui-focused": {
            color: "#0F5A9C",
          },
          "& label": {
            color: "#8A8A8A !important",
            fontFamily: "inter",
            fontStyle: "normal",
            fontWeight: "400",
            fontSize: "14px",
            lineHeight: "20px",
          },

          "& .MuiOutlinedInput-root": {
            borderRadius: "4px",
            height: 45,
            "& fieldset": {
              border: (props) =>
                (props.hasValue
                  ? "1px solid rgba(20, 20, 20, 0.80)"
                  : "1px solid #B9B9B9") + " !important",
            },

            "&:hover fieldset": {
              border: "1px solid rgba(20, 20, 20, 0.80) !important",
            },

            "&.Mui-focused fieldset": {
              border: "1px solid #2F54EB !important",
            },

            "&.Mui-disabled fieldset": {
              border: "1px solid #B9B9B9 !important",
            },
            "&.Mui-disabled:hover fieldset": {
              // Prevent hover effect on disabled fields
              border: "1px solid #B9B9B9 !important",
            },
            // "&.Mui-error fieldset": {
            //   // Border color on error
            //   border: "1px solid #FF4D4F !important",
            // },
          },
          "& .MuiFormHelperText-root": {
            margin: "4px 0px 0px 0px !important",
          },
        }}
        label={""}
        value={props.value}
        helperText={helperText}
        error={error}
        onBlur={() => handleBlur()}
        onChange={(e) => props.handleChange(e)}
        disabled={props.disabled ? props.disabled : false}
      />
    </Box>
  );
}

CustomInput.defaultProps = {
  validation: "none",
  regex: "none",
};
