import React from "react";
import DataTable from "../components/DataTable";
import { useTranslation } from "react-i18next";
import { PrincipalsColumns, PartnerColumns } from "../components/Colums";
import { getCurrentPageData, filterValidItems } from "../components/utils";

const RightTable = (props) => {
  const {
    setOutletOpen,
    setRowId,
    dataType,
    setRowSelection,
    setSelectedRows,
    selectedRows,
    onDeleteRetailer,
  } = props;

  const { t } = useTranslation();

  // 表格数据
  const [data, setData] = useState([]);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const handlerDelete = (id) => {
    if (dataType == "6") {
      // 如果是零售商，调用父组件的删除函数
      onDeleteRetailer(id);
    } else {
      // 如果不是零售商，保持原有的删除逻辑
      setRowSelection((prev) => {
        const newSelection = { ...prev };
        delete newSelection[id];
        return newSelection;
      });

      setSelectedRows((prevMap) => {
        const newMap = new Map(prevMap);
        newMap.delete(id);
        return newMap;
      });
    }

    setData((prevData) => prevData.filter((item) => item.id !== id));
    setRowCount((prevCount) => prevCount - 1);
  };

  useEffect(() => {
    const data = filterValidItems(selectedRows);
    setData(data);
    setRowCount(data.length || 0);
  }, [selectedRows]);

  return (
    <Grid item xs={5.6}>
      <DataTable
        title={
          dataType == "6"
            ? t("datascope.selected_principals")
            : t("datascope.selected_partner_companies")
        }
        selectNumber={true}
        columns={dataType == "6" ? PrincipalsColumns() : PartnerColumns()}
        data={getCurrentPageData(data, pagination)}
        rowCount={rowCount}
        pagination
        rowsPerPage={pagination.pageSize}
        currentPage={pagination.pageIndex}
        getRowId={(originalRow) => originalRow.id} // 确保 ID 唯一
        onPageChange={(pageIndex) => {
          setPagination((prev) => ({
            ...prev,
            pageIndex, // 更新页码
          }));
        }}
        onPageSizeChange={(pageSize) => {
          setPagination({
            pageIndex: 0, // 重置页码
            pageSize, // 更新每页行数
          });
        }}
        renderRowActions={({ cell, row, table }) => {
          return (
            <Grid container xs={12}>
              {dataType == "6" && (
                <Grid item xs={6}>
                  <Button
                    sx={{
                      color: "#1487CA",
                      whiteSpace: "nowrap",
                    }}
                    onClick={() => {
                      setOutletOpen(true);
                      setRowId(row?.original);
                    }}>
                    {t("data_permission.select_outlets")}
                  </Button>
                </Grid>
              )}
              <Grid item xs={6}>
                <Button
                  sx={{
                    color: "red",
                    ml: 3,
                  }}
                  onClick={() => handlerDelete(row?.original.id)}>
                  {t("common.common_op_del")}
                </Button>
              </Grid>
            </Grid>
          );
        }}></DataTable>
    </Grid>
  );
};
export default RightTable;
