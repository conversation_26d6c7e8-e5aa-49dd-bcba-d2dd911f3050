import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import { useNavigate } from "react-router-dom";
import { useStateUserInfo } from "@/hooks/user.js";
import {
  bindPopover,
  bindTrigger,
  usePopupState,
} from "material-ui-popup-state/hooks";
import SvgIcon from "@/components/SvgIcon.jsx";
import { useEffect, useState } from "react";
import user from "@/assets/Images/user.png";
import { pxToRem } from "@/utils/zkUtils.js";
import { useTheme } from "@mui/material/styles";
import OrganizationDetails from "@/assets/menuIcon/OrganizationDetails.svg?react";
import SwitchOrganization from "@/assets/menuIcon/SwitchOrganization.svg?react";
import SwitchPrincipal from "@/assets/menuIcon/SwitchPrincipal.svg?react";
import CheckStatus from "@/assets/menuIcon/CheckStatus.svg?react";
import { Grid, Typography } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import { changeTenantList } from "@/service/api/tenant.js";
import { useTranslation } from "react-i18next";

export default function UserCompany() {
  const popupState = usePopupState({
    variant: "popover",
    popupId: "demoPopover",
  });
  const { t } = useTranslation();
  const navigate = useNavigate();
  const resData = useStateUserInfo();

  const [selectStatus, setSelectStatus] = useState(0); // 是否选中
  const [secondaryPopupOpen, setSecondaryPopupOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [searchName, setSearchName] = useState(null);
  const [selectType, setSelectType] = useState(1);
  const [organization, setOrganization] = useState([]);
  const handleOpenSecondaryPopover = (event, index) => {
    setSelectType(index);
    setAnchorEl(event.currentTarget);
    setSecondaryPopupOpen(true);
  };

  const handleCloseSecondaryPopover = () => {
    setAnchorEl(null);
    setSecondaryPopupOpen(false);
  };

  const handlerSearch = () => {
    changeTenantList({ name: searchName }).then((res) => {
      setOrganization(res?.data);
    });
  };

  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      event.preventDefault(); // 阻止默认行为
      handlerSearch(); // 调用搜索函数
    }
  };

  const [principal, setPrincipal] = useState([
    { id: 0, name: "Principal " },
    { id: 1, name: "Principal " },
    { id: 2, name: "Principal " },
    { id: 3, name: "Principal " },
    { id: 4, name: "Principal " },
    { id: 5, name: "Principal " },
    { id: 6, name: "Principal " },
  ]);

  const [selectedId, setSelectedId] = useState(null); // 记录当前选中的 ID

  useEffect(() => {
    if (
      Array.isArray(resData?.roles) &&
      resData?.roles?.includes("SuperAdmin")
    ) {
      changeTenantList().then((res) => {
        setOrganization(res?.data);
      });
    }
  }, [resData?.roles]);

  const handleClick = (id, name) => {
    setSelectedId(id); // 更新选中的 ID
    sessionStorage.setItem("tenantCode", id);
    sessionStorage.setItem("tenantName", name);
    window.location.reload();
    // popupState.close();
  };

  return (
    <div>
      <Box
        className="flex items-center  h-[40px]  justify-between px-4 py-1 hover:bg-gray-100 focus:bg-gray-100 rounded-md cursor-pointer"
        {...bindTrigger(popupState)}>
        <div className="flex  gap-4 items-center">
          <SvgIcon
            height="1.2em"
            color={"#a1a4a6"}
            icon="simple-line-icons:organization"></SvgIcon>
          <Tooltip
            title={sessionStorage.getItem("tenantName")}
            arrow
            placement="bottom">
            <div className="text-gray-400 text-[14px]">
              {sessionStorage.getItem("tenantName")
                ? sessionStorage.getItem("tenantName")
                : "ZKDIGIMAX"}
            </div>
          </Tooltip>
        </div>
        <Box>
          <div variant="menuItem">
            {popupState.isOpen ? (
              <KeyboardArrowLeftIcon
                fontSize="small"
                style={{
                  color: `#A2A3A3`,
                }}
              />
            ) : (
              <KeyboardArrowRightIcon
                fontSize="small"
                style={{
                  color: `#A2A3A3`,
                }}
              />
            )}
          </div>
        </Box>
      </Box>
      <Popover
        {...bindPopover(popupState)}
        sx={{
          "& .MuiPaper-root": {
            width: "250px",
            // minHeight: 160,
          },
        }}
        anchorOrigin={{
          vertical: "center",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "center",
          horizontal: "left",
        }}>
        <div className="my-1 mx-1">
          <TabButtom
            Icon={
              <SvgIcon
                icon="jam:ghost-org-square"
                className="text-gray-400"
                height="1.5em"
              />
            }
            Text={t("common.common_org_details")}
            selectStatus={selectStatus === 0}
            popupState={popupState}
            onClick={() => {
              popupState.close();
              setSelectStatus(0);
              navigate("/menuItem/viewCompany");
            }}></TabButtom>

          {Array.isArray(resData?.roles) &&
            resData?.roles?.includes("SuperAdmin") && (
              <TabButtom
                Icon={
                  <SvgIcon
                    icon="jam:directions"
                    className="text-gray-400"
                    height="1.5em"
                  />
                }
                Text={t("common.common_switch_org")}
                showArrow={true}
                selectStatus={selectStatus === 1}
                popupState={popupState}
                onClick={(event) => {
                  setSelectStatus(1);
                  handleOpenSecondaryPopover(event, 1);
                  // popupState.close();
                }}></TabButtom>
            )}
        </div>
      </Popover>
      <SecondPapover
        title={
          selectType == "1"
            ? t("common.common_org_list")
            : t("common.common_principal_list")
        }
        secondaryPopupOpen={secondaryPopupOpen}
        anchorEl={anchorEl}
        handleCloseSecondaryPopover={handleCloseSecondaryPopover}
        searchName={searchName}
        setSearchName={setSearchName}
        handlerSearch={handlerSearch}
        handleKeyDown={handleKeyDown}>
        <Grid
          item
          sx={{
            overflowY: "auto",
            maxHeight: "200px",
          }}>
          {(selectType == "1" ? organization : principal).map((item) => {
            const isSelect = selectedId === item.tenantCode; // 判断当前项是否选中
            return (
              <SingleOrganization
                title={item.name}
                isSelect={isSelect}
                key={item.id}
                onClick={() => {
                  handleClick(item.tenantCode, item.name);
                }}></SingleOrganization>
            );
          })}
        </Grid>
      </SecondPapover>
    </div>
  );
}

const TabButtom = (props) => {
  const {
    Icon,
    Text,
    selectStatus,
    onClick,
    popupState,
    showArrow = false,
  } = props;
  const theme = useTheme();
  return (
    <div
      className="flex justify-between text-[14px] flex-row items-center px-2 py-2 gap-2 py-1  hover:bg-gray-100 focus:bg-gray-100 rounded-md"
      onClick={onClick}>
      <div className="flex items-center gap-2">
        <div item>{Icon}</div>
        <div className="text-gray-500">{Text}</div>
      </div>
      {showArrow && (
        <div>
          {popupState.isOpen ? (
            <KeyboardArrowRightIcon style={{ color: "#c4c4c4" }} />
          ) : (
            <KeyboardArrowLeftIcon style={{ color: "#c4c4c4" }} />
          )}
        </div>
      )}
    </div>
  );
};

const PapoverCard = ({
  secondaryPopupOpen,
  anchorEl,
  handleCloseSecondaryPopover,
  children,
}) => {
  return (
    <Popover
      open={secondaryPopupOpen}
      anchorEl={anchorEl}
      onClose={handleCloseSecondaryPopover}
      anchorOrigin={{
        vertical: "center",
        horizontal: "right",
      }}
      sx={{
        "& .MuiPaper-root": {
          width: "280px",
        },
      }}
      transformOrigin={{
        vertical: "center",
        horizontal: "left",
      }}>
      <Box className="mx-2 py-2 rounded-md">{children}</Box>
    </Popover>
  );
};

const SingleOrganization = (props) => {
  const { title, isSelect, onClick, key } = props;
  return (
    <Grid
      container
      key={key}
      className="h-[40px] justify-between  text-[14px]  px-2 hover:bg-primary-100 rounded-md items-center"
      // sx={{
      //   pl: 3,
      //   display: "flex",
      //   p: 1,
      //   // background: selectStatus ? "rgba(122, 193, 67, 0.26)" : "#fff",
      //   alignContent: "center",
      //   justifyContent: "space-between",
      //   "&:hover": {
      //     color: "primary.main",
      //     bgcolor: "rgba(122, 193, 67, 0.26)",
      //   },
      // }}
      onClick={onClick}>
      <div>{title}</div>

      <div>
        {isSelect && (
          <CheckStatus
            style={{
              fontSize: pxToRem(16),
            }}></CheckStatus>
        )}
      </div>
    </Grid>
  );
};

const SecondPapover = (props) => {
  const {
    title,
    secondaryPopupOpen,
    anchorEl,
    handleCloseSecondaryPopover,
    searchName,
    setSearchName,
    handleKeyDown,
    handlerSearch,
    children,
  } = props;

  const { t } = useTranslation();
  return (
    <PapoverCard
      secondaryPopupOpen={secondaryPopupOpen}
      anchorEl={anchorEl}
      handleCloseSecondaryPopover={handleCloseSecondaryPopover}
      sx={{
        "& .MuiBox-root": {
          width: "300px",
        },
      }}>
      <div className="flex flex-col gap-2 w-full">
        <div
          // sx={{
          //   position: "sticky",
          //   top: 0,
          //   // zIndex: 10,
          //   backgroundColor: "#FFFFFF",
          // }}
          className="flex flex-col gap-1">
          <div className="text-[16px] font-bold  px-1">{title}</div>

          <Stack
            sx={{
              flexGrow: 1,
              width: "100%",
              mt: 2,
            }}>
            <OutlinedInput
              id={"zkInput_" + name}
              type="text"
              size="small"
              autoComplete="off"
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              onKeyDown={handleKeyDown} // 监听回车键事件
              placeholder={t("common.search")}
              fullWidth
              sx={{
                "& .MuiOutlinedInput-input": {
                  height: pxToRem(25),
                  fontSize: "14px",
                },
                borderRadius: "7px",
              }}
              endAdornment={
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    edge="end"
                    size="large"
                    sx={{
                      color: "#cacaca",
                    }}
                    onClick={handlerSearch}>
                    <SearchIcon></SearchIcon>
                  </IconButton>
                </InputAdornment>
              }
              disabled={false}
            />
          </Stack>
        </div>

        <div>{children}</div>
      </div>
    </PapoverCard>
  );
};
