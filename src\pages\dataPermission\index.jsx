import React from "react";
import { useNavigate } from "react-router-dom";
import {
  getDataPermissionList,
  deletePermission,
} from "@/service/api/dataPermission.js";
import LayoutList from "@/layout/components/LayoutList.jsx";
import ZktecoTable from "@/components/ZktecoTable/index";
import ZkTooltip from "@/components/ZkTooltip";
import CustomDelete from "@c/Toast/CustomDelete";
import { toast } from "react-toastify";
function index(props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [rowCount, setRowCount] = useState(0);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  const [isError, setIsError] = useState(false);
  const [open, setOpen] = useState(false); // 删除Branch 弹窗
  const [serchName, setSeachName] = useState("");
  const [tenantId, setTenantId] = useState(null);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("data_permission.name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "type",
        header: t("data_permission.type"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <ZkTooltip
              title={
                row.original.type == "6"
                  ? t("data_permission.retail_client")
                  : t("data_permission.partner_permissions")
              }
              arrow
              placement="bottom">
              <span>
                {row.original.type == "6"
                  ? t("data_permission.retail_client")
                  : t("data_permission.partner_permissions")}
              </span>
            </ZkTooltip>
          );
        },
      },
    ],
    []
  );

  const isShowAction = {
    // isShowView: "auth:datascope:query",
    isShowEditor: "auth:datascope:update",
    isShowDetele: "auth:datascope:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerEditor: (data) =>
        navigate("/add/data/permission", {
          state: { id: data?.id, type: "editor" },
        }),
      onAdd: (data) => {
        navigate("/add/data/permission", {
          state: { type: "add" },
        });
      },
      Detele: (data) => {
        setTenantId(data?.id);
        setOpen(true);
      },
    }),
    []
  );

  // 获取数据
  const loadData = useCallback(
    async (searchOverride = null) => {
      if (!data.length) {
        setIsLoading(true);
      } else {
        setIsRefetching(true);
      }

      try {
        const params = {
          page: pagination.pageIndex + 1,
          pageSize: pagination.pageSize,
          name: searchOverride !== null ? searchOverride : serchName.trim(),
        };
        const res = await getDataPermissionList(params);
        setData(res?.data?.data || []);
        setRowCount(res.data?.total || 0);
      } catch (error) {
        setIsError(true);
        console.error("Failed to load data:", error);
      } finally {
        setIsLoading(false);
        setIsRefetching(false);
      }
    },
    [pagination, serchName]
  );

  useEffect(() => {
    loadData();
  }, [pagination.pageIndex, pagination.pageSize]);

  const handlerDetele = async () => {
    try {
      setIsLoading(true);
      const res = await deletePermission(tenantId);
      toast.success(res?.message);
    } finally {
      setIsLoading(false);
      loadData();
      setOpen(false);
    }
  };

  const renderTable = () => {
    return (
      <ZktecoTable
        columns={columns}
        data={data}
        rowCount={rowCount}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        loadDada={() => loadData()}
        paginationProps={{
          currentPage: pagination.pageIndex,
          rowsPerPage: pagination.pageSize,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
        }}
        topActions={{
          showAdd: "auth:datascope:save",
          onAdd: actionHandlers.onAdd, // 添加这一行
        }}
        actionHandlers={actionHandlers}
        isShowAction={isShowAction}
      />
    );
  };

  // 搜索
  const handlerSeacher = () => {
    loadData();
  };

  // 清空
  const handleClear = () => {
    setSeachName(""); // 清空为字符串
    setPagination((prev) => ({ pageIndex: 0, pageSize: prev.pageSize })); // 重置页码

    // 直接传递空字符串作为搜索参数
    loadData("");
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("data_permission.title")}
        isSearch={true}
        onClick={handlerSeacher}
        serchName={serchName}
        setSeachName={setSeachName}
        onClear={handleClear} // 添加 onClear 属性
        content={renderTable()}></LayoutList>

      <CustomDelete
        open={open}
        setOpen={setOpen}
        handlerDetele={handlerDetele}
        content={t("data_permission.sure_delete")}
        onContent={t("data_permission.devices_data")}
        noDelete={false}></CustomDelete>
    </React.Fragment>
  );
}

export default index;
