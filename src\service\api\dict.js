import request from "@/utils/request.js";

const baseProfixURI = `${import.meta.env.VITE_APICODE}/dict_data`;

export const getDictByType = (type) => {
  return request({
    url: `${baseProfixURI}/query/${type}`,
    method: "get",
  });
};

/**
 * 
 * @param {分页查询} params 
 * @returns 
 */
export const getDictList = (params) => {
  return request({
    url: `${baseProfixURI}/query/page`,
    method: "GET",
    params,
  });
};