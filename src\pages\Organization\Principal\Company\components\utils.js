import { getTreeSelect } from "@s/api/area";

export const handleUpload = (file, setImageUrl, setFileUrl) => {
  setFileUrl(file);
  const reader = new FileReader();
  reader.onload = (e) => {
    setImageUrl(e.target.result);
  };
  reader.readAsDataURL(file);
};

// 获取区域下拉选择树
export const getTreeList = (setTreeList) => {
  getTreeSelect().then((res) => {
    if (res?.code == "00000000") {
      setTreeList(res?.data);
    } else {
      setTreeList([]);
    }
  });
};




export const getFormConfig = (t, type, treeList, formik) => {
  let formConfig = [
    {
      name: "name",
      label: t("principal.principal_name"),
      placeholder: t("principal.enter_principal_name"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal.principal_name_required"),
        },
      ],
    },

    {
      name: "email",
      label: t("principal.principal_owner_email"),
      type: "input",
      disabled: type == "editor",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal.principal_owner_email_required"),
        },
        {
          type: "email",
          message: t("邮箱格式有误"),
        },
      ],
    },


    {
      codename: "countryCode", // 对应区号字段名
      name: "phone", // 对应电话号码字段名
      label: t("principal.mobile_number"),
      type: "mobile",
      required: true,

      disabled: type == "editor",
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal.mobile_number_required"),
        },
        {
          type: "matches",
          matches: /^\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}$/,
          message: t("common.common_mobile_format"),
        },
      ],
    },

    {
      name: "areaName",
      label: t("principal.outlet_location"),
      type: "input",
      required: true,
      placeholder: t("principal.select_an_area"),

      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal.area_required"),
        },
      ],
    },

    {
      name: "address",
      label: t("branch_user.address"),
      type: "address",
      required: true,
      placeholder: t("common.common_input_address"),
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_input_address"),
        },
      ],
    },
  ];

  return formConfig;
};