
export const filterValidItems = (items) => {
    return Array.from(items?.values() || []).filter((item) => {
        return (
            item &&
            typeof item === "object" &&
            Object.values(item).every((value) => value !== undefined)
        );
    });
};

export const filterValidKeys = (items) => {
    if (items instanceof Map) {
        // 如果 items 是 Map 实例，直接返回所有的键
        return Array.from(items.keys());
    } else if (items && typeof items === "object") {
        // 如果 items 是普通对象，返回它的所有键
        return Object.keys(items);
    }
    // 如果 items 既不是 Map 也不是对象，返回空数组
    return [];
};

export const SvgIconStyle = {
    width: "35px",
    height: "35px",
    textAlign: "center",
    alignItems: "center",
    display: "flex",
    justifyContent: "center",
    borderRadius: "50%",
    backgroundColor: "#F2F2F2",
    marginTop: "10px",
};

export const getCurrentPageData = (data = [], pagination) => {
    const startIndex = pagination.pageIndex * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return data?.slice(startIndex, endIndex);
};



export const comBinationID = (principalId, outletsId) => {
    // 确保 principalId 是 Map 类型
    const principalMap = principalId instanceof Map ? principalId : new Map(Object.entries(principalId));

    // 过滤掉已经有门店的零售商ID
    const filteredPrincipalIds = Array.from(principalMap.keys()).filter(
        (key) => !Array.from(outletsId.values()).some((outlet) => outlet?.parentId === key || outlet?.principalId === key)
    );

    // 检查 outletsId 是否有数据
    const outletIds = outletsId.size > 0
        ? Array.from(outletsId.values()).map((outlet) => outlet?.id)
        : [];


    // 合并过滤后的零售商ID和门店ID
    const combinedIds = [
        ...filteredPrincipalIds,
        ...outletIds
    ];

    return combinedIds;
};