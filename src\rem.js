// // rem
import 'amfe-flexible'
// rem
const baseSize = 19.2
// 设置 rem 函数
function setRem() {
  // 当前页面宽度缩放比例，可根据自己需要修改
  const scale = document.documentElement.clientWidth / 1920
  // 设置页面根节点字体大小
  let size = baseSize * Math.min(scale, 2) + 'px'
  document.documentElement.style.fontSize = size
}

// 初始化 rem
setRem()
// 改变窗口大小时重新设置 rem
window.onresize = function () {
  setRem()
}

// function setRem() {
//   const baseSize = 32

//   // 当前页面宽度相对于 750 宽的缩放比例，可根据自己需要修改。
//   const scale = document.documentElement.clientWidth / 750
//   // 设置页面根节点字体大小
//   document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px'

//   // const defaultWidth = 1920;
//   // const defaultScale = 1;
//   // let defaultFontSize = 192;

//   // const getWidth = window.innerWidth;

//   // let currentScale = getWidth / defaultWidth;

//   // if (currentScale < 0.85 && getWidth > 1024) {
//   //   currentScale = 0.855;
//   // }

//   // if (getWidth <= 1024) {
//   //   defaultFontSize = defaultFontSize * 2;
//   // }

//   // const currentFontSize = (currentScale / defaultScale) * defaultFontSize;

//   // document.documentElement.style.fontSize = currentFontSize + 'px';
// }
// setRem()
// window.onresize = function () {
//   setRem()
// }
