import request from "@u/request";

export const getProductList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/product/page`,
    method: "GET",
    params: params,
  });
};

export const addProduct = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/product`,
    method: "POST",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const editProduct = (params, id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/product`,
    method: "PUT",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const deleteProduct = (ids) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/product/${ids}`,
    method: "DELETE",
  });
};

export const getProductDetail = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/product/${id}`,
    method: "GET",
  });
};

export const getProductLabel = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/product_type`,
    method: "GET",
    params: params,
  });
};
