import React from "react";
import { getMessagelogList } from "@s/messagelog";
import { useFormik } from "formik";
import SearchForm from "@c/SearchForm";
import AuthButton from "@c/AuthButton";
import DetailMessageLog from "./DetailLog";
import { getStoreLang } from "@/utils/langUtils";
import ZktecoTable from "@c/ZktecoTable";
import CustomInput from "@c/CustInput.jsx";

import { pxToRem } from "@u/zkUtils";
import LayoutList from "@l/components/LayoutList";
function MessageLogList(props) {
  const { t } = useTranslation();
  const detailRef = useRef();
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
    };

    return params;
  };

  // 获取数据
  const getTableData = () => {
    getMessagelogList(buildParams())
      .then((res) => {
        console.log("BBBBBBBBBBBB", res);

        if (res.code == "00000000") {
          setData(res?.data?.data);
          setRowCount(res?.data?.total);
        } else {
          setData([]);
          setRowCount(0);
        }
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "title", //access nested data with dot notation
        header: t("名称"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "templateCode", //access nested data with dot notation
        header: t("code"),
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "status", //access nested data with dot notation
        header: t("状态"),
        enableColumnActions: false,
        enableSorting: false,
      },

      {
        accessorKey: "receiver", //access nested data with dot notation
        header: t("电话"),
        enableColumnActions: false,
        enableSorting: false,
      },
    ],
    []
  );

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      ...buildParams(),
      title: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      getMessagelogList(values)
        .then((res) => {
          if (res.code == "00000000") {
            setData(res?.data?.data);
            setRowCount(res?.data?.total);
          } else {
            setData([]);
            setRowCount(0);
          }
          setIsLoading(false);
          setIsRefetching(false);
        })
        .catch((err) => {
          setIsError(true);
          setIsLoading(false);
          setIsRefetching(false);
        });
    },
  });

  return (
    <LayoutList
      title={"Message Log"}
      header={
        <SearchForm formik={queryFormik}>
          <Grid container>
            <Grid item ml={2}>
              <CustomInput
                value={queryFormik.values.name}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                width={pxToRem(264)}
                name="account"
                fullWidth
                placeholder={t("Login Account")}
              />
            </Grid>

            <Grid item ml={2}>
              <CustomInput
                value={queryFormik.values.name}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="userName"
                width={pxToRem(264)}
                fullWidth
                placeholder={t("Login User Name")}
              />
            </Grid>

            <Grid item ml={2}>
              <CustomInput
                value={queryFormik.values.name}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="address"
                width={pxToRem(264)}
                fullWidth
                placeholder={t("Login Address")}
              />
            </Grid>
          </Grid>
        </SearchForm>
      }
      content={
        <ZktecoTable
          showTopBar={false}
          renderToolbarInternalActions={({ table }) => {
            return null;
          }}
          data={data}
          columns={columns}
          // 是否启动 Action
          enableRowActions
          // 列数
          rowCount={rowCount}
          getRowId={(originalRow) => {
            return originalRow.devSn;
          }}
          state={{
            // 加载状态
            isLoading,
            rowSelection: true,
            showProgressBars: isRefetching,
            showAlertBanner: isError,
          }}
          totalRecords={rowCount || 0} // 如果 data 为空时避免报错
          rowsPerPage={pagination.pageSize}
          currentPage={pagination.pageIndex}
          onPageChange={(pageIndex) => {
            setPagination((prev) => ({
              ...prev,
              pageIndex, // 更新页码
            }));
          }}
          onPageSizeChange={(pageSize) => {
            setPagination({
              pageIndex: 0, // 重置页码
              pageSize, // 更新每页行数
            });
          }}
          isShowAction={{
            isShowView: "message:records:list",
          }}></ZktecoTable>
      }>
      <DetailMessageLog ref={detailRef}></DetailMessageLog>
    </LayoutList>
  );
}

export default MessageLogList;
