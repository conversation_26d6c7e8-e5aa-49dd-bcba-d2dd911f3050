/**
 * Schema验证器
 * 负责表单数据的验证逻辑
 */
class SchemaValidator {
  constructor(schema, customRules = {}) {
    this.schema = schema;
    this.customRules = customRules;
    this.errors = {};
  }

  /**
   * 验证整个表单数据
   * @param {Object} data 表单数据
   * @returns {Object} 验证结果 { isValid: boolean, errors: Object }
   */
  validate(data) {
    this.errors = {};
    this.validateNode(this.schema, data, '');
    
    return {
      isValid: Object.keys(this.errors).length === 0,
      errors: { ...this.errors }
    };
  }

  /**
   * 验证单个字段
   * @param {string} path 字段路径
   * @param {any} value 字段值
   * @param {Object} allData 完整表单数据
   * @returns {string|null} 错误信息或null
   */
  validateField(path, value, allData = {}) {
    const fieldSchema = this.getFieldSchema(path);
    if (!fieldSchema) return null;

    const errors = this.validateValue(value, fieldSchema, path, allData);
    
    if (errors.length > 0) {
      this.errors[path] = errors[0];
      return errors[0];
    } else {
      delete this.errors[path];
      return null;
    }
  }

  /**
   * 设置字段错误
   * @param {string} path 字段路径
   * @param {string|null} error 错误信息
   */
  setFieldError(path, error) {
    if (error) {
      this.errors[path] = error;
    } else {
      delete this.errors[path];
    }
  }

  /**
   * 获取所有错误
   * @returns {Object}
   */
  getErrors() {
    return { ...this.errors };
  }

  /**
   * 清空所有错误
   */
  clearErrors() {
    this.errors = {};
  }

  /**
   * 递归验证节点
   * @param {Object} schema 节点Schema
   * @param {any} data 节点数据
   * @param {string} path 当前路径
   */
  validateNode(schema, data, path) {
    if (!schema || typeof schema !== 'object') return;

    // 验证对象属性
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([key, fieldSchema]) => {
        const fieldPath = path ? `${path}.${key}` : key;
        const fieldValue = data?.[key];
        
        const errors = this.validateValue(fieldValue, fieldSchema, fieldPath, data);
        if (errors.length > 0) {
          this.errors[fieldPath] = errors[0];
        }
        
        // 递归验证嵌套对象
        if (fieldSchema.type === 'object' && fieldValue) {
          this.validateNode(fieldSchema, fieldValue, fieldPath);
        }
        
        // 验证数组项
        if (fieldSchema.type === 'array' && Array.isArray(fieldValue) && fieldSchema.items) {
          fieldValue.forEach((item, index) => {
            const itemPath = `${fieldPath}[${index}]`;
            this.validateNode(fieldSchema.items, item, itemPath);
          });
        }
      });
    }

    // 验证字段数组
    if (schema.fields) {
      schema.fields.forEach(fieldSchema => {
        if (fieldSchema.name) {
          const fieldPath = path ? `${path}.${fieldSchema.name}` : fieldSchema.name;
          const fieldValue = data?.[fieldSchema.name];
          
          const errors = this.validateValue(fieldValue, fieldSchema, fieldPath, data);
          if (errors.length > 0) {
            this.errors[fieldPath] = errors[0];
          }
        }
      });
    }
  }

  /**
   * 验证单个值
   * @param {any} value 值
   * @param {Object} schema 字段Schema
   * @param {string} path 字段路径
   * @param {Object} allData 完整数据
   * @returns {Array<string>} 错误列表
   */
  validateValue(value, schema, path, allData) {
    const errors = [];
    const fieldName = schema.title || path.split('.').pop();

    // 必填验证
    if (schema.required && this.isEmpty(value)) {
      errors.push(`${fieldName} is required`);
      return errors; // 必填验证失败时，不进行其他验证
    }

    // 如果值为空且非必填，跳过其他验证
    if (this.isEmpty(value)) {
      return errors;
    }

    // 类型验证
    const typeError = this.validateType(value, schema, fieldName);
    if (typeError) {
      errors.push(typeError);
      return errors; // 类型验证失败时，不进行其他验证
    }

    // 字符串验证
    if (schema.type === 'string' && typeof value === 'string') {
      // 长度验证
      if (schema.minLength && value.length < schema.minLength) {
        errors.push(`${fieldName} must be at least ${schema.minLength} characters long`);
      }
      if (schema.maxLength && value.length > schema.maxLength) {
        errors.push(`${fieldName} must be no more than ${schema.maxLength} characters long`);
      }
      
      // 模式验证
      if (schema.pattern) {
        const regex = new RegExp(schema.pattern);
        if (!regex.test(value)) {
          errors.push(`${fieldName} format is invalid`);
        }
      }
      
      // 格式验证
      if (schema.format) {
        const formatError = this.validateFormat(value, schema.format, fieldName);
        if (formatError) {
          errors.push(formatError);
        }
      }
      
      // 枚举验证
      if (schema.enum && !schema.enum.includes(value)) {
        errors.push(`${fieldName} must be one of: ${schema.enum.join(', ')}`);
      }
    }

    // 数字验证
    if ((schema.type === 'number' || schema.type === 'integer') && typeof value === 'number') {
      if (schema.minimum !== undefined && value < schema.minimum) {
        errors.push(`${fieldName} must be at least ${schema.minimum}`);
      }
      if (schema.maximum !== undefined && value > schema.maximum) {
        errors.push(`${fieldName} must be no more than ${schema.maximum}`);
      }
      if (schema.exclusiveMinimum !== undefined && value <= schema.exclusiveMinimum) {
        errors.push(`${fieldName} must be greater than ${schema.exclusiveMinimum}`);
      }
      if (schema.exclusiveMaximum !== undefined && value >= schema.exclusiveMaximum) {
        errors.push(`${fieldName} must be less than ${schema.exclusiveMaximum}`);
      }
      if (schema.multipleOf && value % schema.multipleOf !== 0) {
        errors.push(`${fieldName} must be a multiple of ${schema.multipleOf}`);
      }
    }

    // 数组验证
    if (schema.type === 'array' && Array.isArray(value)) {
      if (schema.minItems && value.length < schema.minItems) {
        errors.push(`${fieldName} must have at least ${schema.minItems} items`);
      }
      if (schema.maxItems && value.length > schema.maxItems) {
        errors.push(`${fieldName} must have no more than ${schema.maxItems} items`);
      }
      if (schema.uniqueItems && new Set(value).size !== value.length) {
        errors.push(`${fieldName} items must be unique`);
      }
    }

    // 自定义验证规则
    if (this.customRules[path]) {
      const customError = this.customRules[path](value, allData, schema);
      if (customError) {
        errors.push(customError);
      }
    }

    return errors;
  }

  /**
   * 验证类型
   * @param {any} value 值
   * @param {Object} schema 字段Schema
   * @param {string} fieldName 字段名
   * @returns {string|null}
   */
  validateType(value, schema, fieldName) {
    switch (schema.type) {
      case 'string':
        if (typeof value !== 'string') {
          return `${fieldName} must be a string`;
        }
        break;
      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          return `${fieldName} must be a number`;
        }
        break;
      case 'integer':
        if (typeof value !== 'number' || !Number.isInteger(value)) {
          return `${fieldName} must be an integer`;
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          return `${fieldName} must be a boolean`;
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          return `${fieldName} must be an array`;
        }
        break;
      case 'object':
        if (typeof value !== 'object' || value === null || Array.isArray(value)) {
          return `${fieldName} must be an object`;
        }
        break;
    }
    return null;
  }

  /**
   * 验证格式
   * @param {string} value 值
   * @param {string} format 格式
   * @param {string} fieldName 字段名
   * @returns {string|null}
   */
  validateFormat(value, format, fieldName) {
    const formats = {
      email: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: `${fieldName} must be a valid email address`
      },
      url: {
        pattern: /^https?:\/\/.+/,
        message: `${fieldName} must be a valid URL`
      },
      uri: {
        pattern: /^[a-zA-Z][a-zA-Z0-9+.-]*:/,
        message: `${fieldName} must be a valid URI`
      },
      date: {
        pattern: /^\d{4}-\d{2}-\d{2}$/,
        message: `${fieldName} must be a valid date (YYYY-MM-DD)`
      },
      'date-time': {
        pattern: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/,
        message: `${fieldName} must be a valid date-time`
      },
      time: {
        pattern: /^\d{2}:\d{2}:\d{2}$/,
        message: `${fieldName} must be a valid time (HH:MM:SS)`
      },
      ipv4: {
        pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
        message: `${fieldName} must be a valid IPv4 address`
      },
      ipv6: {
        pattern: /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/,
        message: `${fieldName} must be a valid IPv6 address`
      }
    };

    const formatRule = formats[format];
    if (formatRule && !formatRule.pattern.test(value)) {
      return formatRule.message;
    }

    return null;
  }

  /**
   * 检查值是否为空
   * @param {any} value 值
   * @returns {boolean}
   */
  isEmpty(value) {
    return value === undefined || 
           value === null || 
           value === '' || 
           (Array.isArray(value) && value.length === 0) ||
           (typeof value === 'object' && Object.keys(value).length === 0);
  }

  /**
   * 获取字段Schema
   * @param {string} path 字段路径
   * @returns {Object|null}
   */
  getFieldSchema(path) {
    if (!path) return null;
    
    const keys = path.split('.');
    let current = this.schema;
    
    for (const key of keys) {
      if (current.properties && current.properties[key]) {
        current = current.properties[key];
      } else if (current.fields) {
        current = current.fields.find(field => field.name === key);
        if (!current) return null;
      } else {
        return null;
      }
    }
    
    return current;
  }

  /**
   * 添加自定义验证规则
   * @param {string} path 字段路径
   * @param {Function} rule 验证函数
   */
  addCustomRule(path, rule) {
    this.customRules[path] = rule;
  }

  /**
   * 移除自定义验证规则
   * @param {string} path 字段路径
   */
  removeCustomRule(path) {
    delete this.customRules[path];
  }
}

export default SchemaValidator;
