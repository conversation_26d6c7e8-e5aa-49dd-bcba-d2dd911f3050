import MainCard from "@c/MainCard";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { pxToRem } from "@/utils/zkUtils";

function SearchForm(props) {
  let navigate = useNavigate();
  const backFunction = () => {
    navigate(-1);
  };

  const addBack = () => {
    if (props?.pathRoute) {
      navigate(pathRoute);
    }
  };

  const {
    formik,
    children,
    showBack = false,
    backFn = backFunction,
    title = "",
    reloadData,
    headerTitle,
    showAdd = true,
    showRefresh = true,
    showDownload = false,
    showUpload = false,
    addCallback = addBack,
    refreshCallback = () => {
      props?.loadDada();
    },
    ...other
  } = props;

  const { t } = useTranslation();

  return (
    <MainCard>
      <Grid
        sx={{
          display: "flex",
          justifyContent: "space-between",
        }}>
        <form noValidate onSubmit={formik.handleSubmit}>
          <Grid container spacing={2}>
            <Grid mt={4} ml={2}>
              {children}
            </Grid>

            <Stack direction="row" spacing={2} mt={4} ml={2}>
              <Button
                disableElevation
                type="submit"
                variant="contained"
                size="medium"
                style={{
                  minWidth: pxToRem(157),
                  height: pxToRem(50),
                  borderRadius: "10px",

                  border: "1px solid #E3E3E3",
                  opacity: 1,
                  background:
                    "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                }}>
                {t("common.common_query")}
              </Button>
              <Button
                variant="outlined"
                onClick={() => {
                  formik.resetForm();
                  reloadData();
                }}
                style={{
                  minWidth: pxToRem(157),
                  height: pxToRem(50),
                  borderRadius: "10px",

                  border: "1px solid #E3E3E3",
                  opacity: 1,
                  color: "#000",
                  fontWeight: 500,
                }}
                disableElevation
                size="medium">
                {t("common.common_reset")}
              </Button>
            </Stack>
          </Grid>
        </form>

        {/* <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}>
          <Grid pl={4}>
            <Typography variant="h5">{headerTitle}</Typography>
          </Grid>
          <Grid
            sx={{
              display: "flex",
              justifyContent: "flex-end",
            }}>
            {showDownload && (
              <Button
                style={{
                  border: "none",
                  mb: 2,
                }}
                onClick={downCallBack}>
                <DownLoadIcon></DownLoadIcon>
              </Button>
            )}

            {showUpload && (
              <Button onClick={uploadCallBack}>
                <UploadIcon></UploadIcon>
              </Button>
            )}

            {showRefresh && (
              <Button onClick={refreshCallback}>
                <RefreshIcon></RefreshIcon>
              </Button>
            )}

            {showAdd && (
              <Button onClick={addCallback}>
                <AddIcon></AddIcon>
              </Button>
            )}
          </Grid>
        </Grid> */}
      </Grid>
    </MainCard>
  );
}
export default SearchForm;
