import React from "react";
import Login from "./Login";
import ForgotPassword from "./ForgotPassword";

function index() {
  const [loginOrForgot, setLoginOrForgot] = useState("0");
  return (
    <React.Fragment>
      {loginOrForgot == "0" ? (
        <Login
          loginOrForgot={loginOrForgot}
          setLoginOrForgot={setLoginOrForgot}></Login>
      ) : (
        <ForgotPassword
          loginOrForgot={loginOrForgot}
          setLoginOrForgot={setLoginOrForgot}></ForgotPassword>
      )}
    </React.Fragment>
  );
}

export default index;
