import React, { useState } from "react";
import Typography from "@mui/material/Typography";
import Popover from "@mui/material/Popover";
import {
  usePopupState,
  bindTrigger,
  bindPopover,
} from "material-ui-popup-state/hooks";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import Language from "@/assets/menuIcon/Language.svg?react";
import { useTranslation } from "react-i18next";
import { setStoreLang } from "@/utils/langUtils";
import SvgIcon from "@/components/SvgIcon";

export default function SwitchLanguage() {
  const { t } = useTranslation();
  const { i18n } = useTranslation();
  const popupState = usePopupState({
    variant: "popover",
    popupId: "demoPopover",
  });

  const switchLang = (lang) => {
    // setDefaultLang(lang);
    i18n.changeLanguage(lang);
    // 当前语言保存到浏览器
    setStoreLang(lang);
    // 刷新
    location.reload();
  };
  return (
    <div>
      <Box
        className="flex items-center justify-between h-[40px] px-4 py-1 hover:bg-gray-100 focus:bg-gray-100 rounded-md cursor-pointer"
        {...bindTrigger(popupState)}>
        <div className="flex gap-4 items-center">
          <SvgIcon
            color={"#a1a4a6"}
            height="1.2em"
            icon="carbon:ibm-watson-language-translator"
          />
          <Tooltip title={"Language"} arrow placement="bottom">
            <div className="text-gray-400 text-[14px]">
              {t("common.common_language")}
            </div>
          </Tooltip>
        </div>

        <Box>
          <div>
            {popupState.isOpen ? (
              <KeyboardArrowLeftIcon
                fontSize="small"
                style={{
                  color: `#A2A3A3`,
                }}
              />
            ) : (
              <KeyboardArrowRightIcon
                fontSize="small"
                style={{
                  color: `#A2A3A3`,
                }}
              />
            )}
          </div>
        </Box>
      </Box>
      <Popover
        {...bindPopover(popupState)}
        anchorOrigin={{
          vertical: "center",
          horizontal: "right",
        }}
        sx={{
          "& .MuiPaper-root": {
            width: "200px",
            // minHeight: 160,
          },
        }}
        transformOrigin={{
          vertical: "center",
          horizontal: "left",
        }}>
        <div className="my-1 mx-1">
          <MenuItem
            className="text-[14px] text-gray-500"
            onClick={() => {
              switchLang("en");
              popupState.close();
            }}>
            {t("English")}
          </MenuItem>
          <MenuItem
            className="text-[14px] text-gray-500"
            onClick={() => {
              switchLang("zh");
              popupState.close();
            }}>
            {t("中文(简体)")}
          </MenuItem>
        </div>

        {/* <MenuItem
          onClick={() => {
            switchLang("th");
            popupState.close();
          }}>
          {t("Español")}
        </MenuItem>
        <MenuItem
          onClick={() => {
            switchLang("jp");
            popupState.close();
          }}>
          {t("日本語")}
        </MenuItem>

        <MenuItem
          onClick={() => {
            switchLang("pt");
            popupState.close();
          }}>
          {t("Português")}
        </MenuItem> */}
      </Popover>
    </div>
  );
}
