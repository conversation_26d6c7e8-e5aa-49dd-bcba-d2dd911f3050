import React from "react";
import DataTable from "../components/DataTable";
import { filterValidItems, getCurrentPageData } from "../components/utils";
import { OutletsColumns } from "../components/Colums";
import { useTranslation } from "react-i18next";
const RightTable = (props) => {
  const { selectOutlets, setRowSelection, setSelectOutlets } = props;

  const { t } = useTranslation();

  // 表格数据
  const [data, setData] = useState([]);

  // 总数
  const [rowCount, setRowCount] = useState(0);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  useEffect(() => {
    const data = filterValidItems(selectOutlets);

    console.log("rrrrrrrrrrrrrrrrrrr", data);

    setData(data);
    setRowCount(data.length || 0);
  }, [selectOutlets]);

  const handlerDelete = (id) => {
    setRowSelection((prev) => {
      const newSelection = { ...prev };
      delete newSelection[id];
      return newSelection;
    });

    // 同时更新 selectedRows
    setSelectOutlets((prevMap) => {
      const newMap = new Map(prevMap);
      newMap.delete(id);
      return newMap;
    });

    setData((prevData) => prevData.filter((item) => item.id !== id));
    setRowCount((prevCount) => prevCount - 1);
  };

  return (
    <Grid item xs={5.6}>
      <DataTable
        title={t("datascope.selected_outlets")}
        columns={OutletsColumns()}
        data={getCurrentPageData(data, pagination)}
        selectNumber={true}
        rowCount={rowCount}
        rowsPerPage={pagination.pageSize}
        currentPage={pagination.pageIndex}
        getRowId={(originalRow) => originalRow.id} // 确保 ID 唯一
        onPageChange={(pageIndex) => {
          setPagination((prev) => ({
            ...prev,
            pageIndex, // 更新页码
          }));
        }}
        onPageSizeChange={(pageSize) => {
          setPagination({
            pageIndex: 0, // 重置页码
            pageSize, // 更新每页行数
          });
        }}
        renderRowActions={({ cell, row, table }) => {
          return (
            <Grid
              container
              xs={12}
              justifyContent={"center"}
              alignContent={"center"}>
              <Button
                sx={{
                  color: "red",
                }}
                onClick={() => handlerDelete(row?.original.id)}>
                {t("common.common_delete")}
              </Button>
            </Grid>
          );
        }}></DataTable>
    </Grid>
  );
};

export default RightTable;
