import React, { useState } from "react";
import {
  Box,
  Typography,
  Paper,
  Grid,
  FormControl,
  FormLabel,
  Switch,
  FormControlLabel,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>le,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import DatePicker from "./index";
import DateTimePicker from "./DateTimePicker";
import {
  NotificationProvider,
  useNotification,
} from "../Notification/NotificationProvider";
import dayjs from "dayjs";

// Material-UI 风格的样式组件
const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  boxShadow: theme.shadows[2],
  transition: theme.transitions.create(["box-shadow", "transform"], {
    duration: theme.transitions.duration.short,
  }),
  "&:hover": {
    boxShadow: theme.shadows[4],
    transform: "translateY(-2px)",
  },
}));

const ConfigPanel = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.spacing(2),
  background: `linear-gradient(135deg, ${theme.palette.primary.light}08, ${theme.palette.secondary.light}08)`,
  border: `1px solid ${theme.palette.divider}`,
}));

const ResultChip = styled(Chip)(({ theme }) => ({
  fontFamily: "monospace",
  fontSize: "0.75rem",
  maxWidth: "100%",
  "& .MuiChip-label": {
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
}));

// 预设选项
const datePresets = [
  {
    label: "今天",
    value: () => dayjs(),
  },
  {
    label: "昨天",
    value: () => dayjs().subtract(1, "day"),
  },
  {
    label: "一周前",
    value: () => dayjs().subtract(1, "week"),
  },
  {
    label: "一个月前",
    value: () => dayjs().subtract(1, "month"),
  },
];

const rangePresets = [
  {
    label: "最近7天",
    value: () => [dayjs().subtract(7, "day"), dayjs()],
  },
  {
    label: "最近30天",
    value: () => [dayjs().subtract(30, "day"), dayjs()],
  },
  {
    label: "本月",
    value: () => [dayjs().startOf("month"), dayjs().endOf("month")],
  },
  {
    label: "上月",
    value: () => [
      dayjs().subtract(1, "month").startOf("month"),
      dayjs().subtract(1, "month").endOf("month"),
    ],
  },
];

const AntdDatePickerExampleContent = () => {
  const notification = useNotification();

  // 基础示例
  const [basicValue, setBasicValue] = useState();

  // 范围选择示例
  const [rangeValue, setRangeValue] = useState([]);

  // 时间选择示例
  const [timeValue, setTimeValue] = useState();

  // 年月选择示例
  const [monthValue, setMonthValue] = useState();
  const [yearValue, setYearValue] = useState();

  // 时间选择示例
  const [pureTimeValue, setPureTimeValue] = useState();

  // 日期时间选择示例
  const [dateTimeValue, setDateTimeValue] = useState();

  // 配置选项
  const [showTime, setShowTime] = useState(false);
  const [disabled, setDisabled] = useState(false);
  const [allowClear, setAllowClear] = useState(true);
  const [needConfirm, setNeedConfirm] = useState(false);
  const [size, setSize] = useState("middle");

  // 处理日期时间变化，显示通知
  const handleDateTimeChange = (value, dateString) => {
    setDateTimeValue(value);
    if (value) {
      notification.success(`已选择日期时间: ${dateString}`, {
        duration: 3000,
      });
    } else {
      notification.info("已清空日期时间选择");
    }
  };

  return (
    <Box
      sx={{ p: 3, backgroundColor: "background.default", minHeight: "100vh" }}>
      <Box sx={{ textAlign: "center", mb: 4 }}>
        <Typography
          variant="h3"
          component="h1"
          gutterBottom
          sx={{ fontWeight: 600 }}>
          Material-UI DatePicker
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          兼容 Ant Design DatePicker API 的 Material-UI 风格日期选择组件
        </Typography>
        <Alert severity="info" sx={{ maxWidth: 600, mx: "auto" }}>
          <AlertTitle>特性说明</AlertTitle>
          完全兼容 Ant Design DatePicker API，使用 Material-UI 设计风格
        </Alert>
      </Box>

      {/* 配置面板 */}
      <ConfigPanel sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom sx={{ fontWeight: 500, mb: 3 }}>
          🎛️ 配置选项
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControlLabel
              control={
                <Switch
                  checked={showTime}
                  onChange={(e) => setShowTime(e.target.checked)}
                  color="primary"
                />
              }
              label="showTime"
              sx={{ "& .MuiFormControlLabel-label": { fontSize: "0.875rem" } }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControlLabel
              control={
                <Switch
                  checked={disabled}
                  onChange={(e) => setDisabled(e.target.checked)}
                  color="primary"
                />
              }
              label="disabled"
              sx={{ "& .MuiFormControlLabel-label": { fontSize: "0.875rem" } }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControlLabel
              control={
                <Switch
                  checked={allowClear}
                  onChange={(e) => setAllowClear(e.target.checked)}
                  color="primary"
                />
              }
              label="allowClear"
              sx={{ "& .MuiFormControlLabel-label": { fontSize: "0.875rem" } }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <FormControlLabel
              control={
                <Switch
                  checked={needConfirm}
                  onChange={(e) => setNeedConfirm(e.target.checked)}
                  color="primary"
                />
              }
              label="needConfirm"
              sx={{ "& .MuiFormControlLabel-label": { fontSize: "0.875rem" } }}
            />
          </Grid>
        </Grid>
      </ConfigPanel>

      <Grid container spacing={3}>
        {/* 基础用法 */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardHeader
              title="📅 基础日期选择"
              subheader="基本的日期选择功能"
              titleTypographyProps={{ variant: "h6", fontWeight: 500 }}
            />
            <CardContent>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <FormLabel sx={{ mb: 1, fontWeight: 500 }}>选择日期</FormLabel>
                <DatePicker
                  value={basicValue}
                  onChange={setBasicValue}
                  placeholder="请选择日期"
                  showTime={showTime}
                  disabled={disabled}
                  allowClear={allowClear}
                  needConfirm={needConfirm}
                  size={size}
                  presets={datePresets}
                  format={showTime ? "YYYY-MM-DD HH:mm:ss" : "YYYY-MM-DD"}
                />
              </FormControl>

              <Divider sx={{ my: 2 }} />

              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  选中值:
                </Typography>
                <ResultChip
                  label={
                    basicValue
                      ? basicValue.format("YYYY-MM-DD HH:mm:ss")
                      : "未选择"
                  }
                  variant="outlined"
                  size="small"
                  color={basicValue ? "primary" : "default"}
                />
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* 范围选择 */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardHeader
              title="📅 日期范围选择"
              subheader="选择日期范围"
              titleTypographyProps={{ variant: "h6", fontWeight: 500 }}
            />
            <CardContent>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <FormLabel sx={{ mb: 1, fontWeight: 500 }}>
                  选择日期范围
                </FormLabel>
                <DatePicker.RangePicker
                  value={rangeValue}
                  onChange={setRangeValue}
                  placeholder={["开始日期", "结束日期"]}
                  showTime={showTime}
                  disabled={disabled}
                  allowClear={allowClear}
                  needConfirm={needConfirm}
                  size={size}
                  presets={rangePresets}
                  format={showTime ? "YYYY-MM-DD HH:mm:ss" : "YYYY-MM-DD"}
                />
              </FormControl>

              <Divider sx={{ my: 2 }} />

              <Stack
                direction="row"
                spacing={1}
                alignItems="center"
                flexWrap="wrap">
                <Typography variant="body2" color="text.secondary">
                  选中范围:
                </Typography>
                {rangeValue && rangeValue[0] && rangeValue[1] ? (
                  <ResultChip
                    label={`${rangeValue[0].format(
                      "YYYY-MM-DD"
                    )} ~ ${rangeValue[1].format("YYYY-MM-DD")}`}
                    variant="outlined"
                    size="small"
                    color="primary"
                  />
                ) : (
                  <ResultChip
                    label="未选择"
                    variant="outlined"
                    size="small"
                    color="default"
                  />
                )}
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* 月份和年份选择 */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardHeader
              title="📅 月份/年份选择"
              subheader="选择月份或年份"
              titleTypographyProps={{ variant: "h6", fontWeight: 500 }}
            />
            <CardContent>
              <Stack spacing={3}>
                <FormControl fullWidth>
                  <FormLabel sx={{ mb: 1, fontWeight: 500 }}>
                    月份选择
                  </FormLabel>
                  <DatePicker
                    value={monthValue}
                    onChange={setMonthValue}
                    placeholder="请选择月份"
                    picker="month"
                    disabled={disabled}
                    allowClear={allowClear}
                    size={size}
                    format="YYYY-MM"
                  />
                </FormControl>

                <FormControl fullWidth>
                  <FormLabel sx={{ mb: 1, fontWeight: 500 }}>
                    年份选择
                  </FormLabel>
                  <DatePicker
                    value={yearValue}
                    onChange={setYearValue}
                    placeholder="请选择年份"
                    picker="year"
                    disabled={disabled}
                    allowClear={allowClear}
                    size={size}
                    format="YYYY"
                  />
                </FormControl>
              </Stack>

              <Divider sx={{ my: 2 }} />

              <Stack spacing={1}>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Typography variant="body2" color="text.secondary">
                    月份:
                  </Typography>
                  <ResultChip
                    label={monthValue ? monthValue.format("YYYY-MM") : "未选择"}
                    variant="outlined"
                    size="small"
                    color={monthValue ? "primary" : "default"}
                  />
                </Stack>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Typography variant="body2" color="text.secondary">
                    年份:
                  </Typography>
                  <ResultChip
                    label={yearValue ? yearValue.format("YYYY") : "未选择"}
                    variant="outlined"
                    size="small"
                    color={yearValue ? "primary" : "default"}
                  />
                </Stack>
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* 时间选择 */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardHeader
              title="⏰ 时间选择"
              subheader="纯时间选择功能"
              titleTypographyProps={{ variant: "h6", fontWeight: 500 }}
            />
            <CardContent>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <FormLabel sx={{ mb: 1, fontWeight: 500 }}>选择时间</FormLabel>
                <DatePicker.TimePicker
                  value={pureTimeValue}
                  onChange={setPureTimeValue}
                  placeholder="请选择时间"
                  disabled={disabled}
                  allowClear={allowClear}
                  needConfirm={needConfirm}
                  size={size}
                  format="HH:mm:ss"
                />
              </FormControl>

              <Divider sx={{ my: 2 }} />

              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  选中时间:
                </Typography>
                <ResultChip
                  label={
                    pureTimeValue ? pureTimeValue.format("HH:mm:ss") : "未选择"
                  }
                  variant="outlined"
                  size="small"
                  color={pureTimeValue ? "primary" : "default"}
                />
              </Stack>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* 日期时间选择 */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardHeader
              title="📅⏰ 日期时间选择"
              subheader="同时选择年月日和时分秒"
              titleTypographyProps={{ variant: "h6", fontWeight: 500 }}
            />
            <CardContent>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <FormLabel sx={{ mb: 1, fontWeight: 500 }}>
                  选择日期时间
                </FormLabel>
                <DateTimePicker
                  value={dateTimeValue}
                  onChange={handleDateTimeChange}
                  placeholder="请选择日期时间"
                  disabled={disabled}
                  allowClear={allowClear}
                  needConfirm={needConfirm}
                  size={size}
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </FormControl>

              <Divider sx={{ my: 2 }} />

              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  选中值:
                </Typography>
                <ResultChip
                  label={
                    dateTimeValue
                      ? dateTimeValue.format("YYYY-MM-DD HH:mm:ss")
                      : "未选择"
                  }
                  variant="outlined"
                  size="small"
                  color={dateTimeValue ? "primary" : "default"}
                />
              </Stack>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  ✨ <strong>新组件特性:</strong>
                  <br />
                  • 🗓️ 左侧日期选择面板
                  <br />
                  • ⏰ 右侧时分秒选择面板
                  <br />
                  • 🎯 完全兼容 MUI 设计风格
                  <br />
                  • ✅ 支持确认/取消操作
                  <br />• 🧹 支持清空功能
                </Typography>
              </Alert>
            </CardContent>
          </StyledCard>
        </Grid>

        {/* API 演示 */}
        <Grid item xs={12} md={6}>
          <StyledCard>
            <CardHeader
              title="🎯 API 演示"
              subheader="完整的功能演示和测试"
              titleTypographyProps={{ variant: "h6", fontWeight: 500 }}
            />
            <CardContent>
              <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
                <Button
                  variant="contained"
                  onClick={() => {
                    const values = {
                      basicValue: basicValue?.format("YYYY-MM-DD HH:mm:ss"),
                      rangeValue: rangeValue?.map((d) =>
                        d?.format("YYYY-MM-DD HH:mm:ss")
                      ),
                      monthValue: monthValue?.format("YYYY-MM"),
                      yearValue: yearValue?.format("YYYY"),
                      timeValue: timeValue?.format("YYYY-MM-DD HH:mm:ss"),
                      pureTimeValue: pureTimeValue?.format("HH:mm:ss"),
                      dateTimeValue: dateTimeValue?.format(
                        "YYYY-MM-DD HH:mm:ss"
                      ),
                    };
                    console.log("当前所有值:", values);
                    notification.info("所有值已打印到控制台", {
                      title: "操作完成",
                      description: "请打开浏览器控制台查看详细信息",
                    });
                  }}>
                  打印所有值
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => {
                    setBasicValue(undefined);
                    setRangeValue([]);
                    setMonthValue(undefined);
                    setYearValue(undefined);
                    setTimeValue(undefined);
                    setPureTimeValue(undefined);
                    setDateTimeValue(undefined);
                    notification.success("所有值已清空");
                  }}>
                  清空所有值
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={() => {
                    notification.warning("这是一个警告提示", {
                      title: "注意",
                      description: "演示通知组件的警告样式",
                    });
                  }}>
                  测试通知
                </Button>
              </Stack>

              <Alert severity="success">
                <AlertTitle>✅ 功能特性</AlertTitle>
                <Typography variant="body2" component="div">
                  • 🎯 100% 兼容 Ant Design DatePicker API
                  <br />
                  • 🎨 Material-UI 设计风格 + Ant Design 弹出样式
                  <br />
                  • 📅 支持日期、时间、范围选择
                  <br />
                  • ⏰ 三列时间选择器（时/分/秒）
                  <br />
                  • �⏰ 新增日期时间选择器（年月日+时分秒）
                  <br />
                  • �📱 响应式设计
                  <br />
                  • 🎭 主题集成
                  <br />
                  • ⚡ 预设快捷选项
                  <br />
                  • 🌍 国际化支持
                  <br />
                  • ♿ 无障碍支持
                  <br />• 💰 完全免费（无需 MUI X Pro）
                </Typography>
              </Alert>
            </CardContent>
          </StyledCard>
        </Grid>
      </Grid>
    </Box>
  );
};

// 主组件 - 包装NotificationProvider
const AntdDatePickerExample = () => {
  return (
    <NotificationProvider maxCount={5}>
      <AntdDatePickerExampleContent />
    </NotificationProvider>
  );
};

export default AntdDatePickerExample;
