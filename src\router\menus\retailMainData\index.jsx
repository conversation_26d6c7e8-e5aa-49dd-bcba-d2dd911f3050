const retailRoute = [
  {
    path: "/retail/list",
    component: () => import("@p/RetailMainData/index"),
    meta: {
      title: "Alfamart",
      i18n: "retail_list",
      id: "236999961774998666",
    },
  },

  {
    path: "/add/retail/outlet",
    component: () => import("@p/RetailMainData/Outlet/AddOutlet"),
    meta: {
      title: "Outlet",
      i18n: "outlet_add",
      id: "236974123699866",
    },
  },

  {
    path: "/view/retail/outlet",
    component: () => import("@p/RetailMainData/Outlet/ViewOutlet"),
    meta: {
      title: "View Outlet",
      i18n: "outlet_view",
      id: "1389694123699866",
    },
  },

  {
    path: "/add/retail/product",
    component: () => import("@p/RetailMainData/Product/AddProduct"),
    meta: {
      title: "Product",
      i18n: "product_add",
      id: "8969896623699866",
    },
  },

  {
    path: "/view/retail/product",
    component: () => import("@p/RetailMainData/Product/ViewProduct"),
    meta: {
      title: "Product",
      i18n: "product_view",
      id: "1285623823699866",
    },
  },
];

export default retailRoute;
