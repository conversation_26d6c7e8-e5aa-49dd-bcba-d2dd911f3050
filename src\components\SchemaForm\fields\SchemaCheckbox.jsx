import React, { useCallback } from 'react';
import {
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  FormLabel,
  Checkbox,
  Switch,
  Box,
  Typography
} from '@mui/material';
import { useTranslation } from 'react-i18next';

/**
 * Schema驱动的复选框/开关组件
 */
const SchemaCheckbox = ({
  name,
  path,
  schema,
  uiSchema = {},
  value = false,
  error,
  touched,
  disabled = false,
  readonly = false,
  required = false,
  onChange,
  onBlur,
  onValidate,
  formData,
  registry,
  ...props
}) => {
  const { t } = useTranslation();

  // 从schema中提取配置
  const {
    title,
    description,
    enum: enumValues,
    enumNames,
    type: schemaType
  } = schema;

  // 从uiSchema中提取UI配置
  const {
    label = title,
    help = description,
    widget = 'checkbox', // checkbox | switch | checkboxes
    inline = false,
    color = 'primary',
    size = 'medium',
    labelPlacement = 'end',
    ...uiProps
  } = uiSchema;

  // 处理值变化
  const handleChange = useCallback((event) => {
    const newValue = event.target.checked;
    onChange?.(newValue);
  }, [onChange]);

  // 处理多选值变化
  const handleMultipleChange = useCallback((optionValue) => (event) => {
    const checked = event.target.checked;
    const currentValues = Array.isArray(value) ? value : [];
    
    let newValues;
    if (checked) {
      newValues = [...currentValues, optionValue];
    } else {
      newValues = currentValues.filter(v => v !== optionValue);
    }
    
    onChange?.(newValues);
  }, [value, onChange]);

  // 处理失焦
  const handleBlur = useCallback((event) => {
    onBlur?.(event);
    
    // 执行验证
    if (onValidate) {
      const errors = [];
      
      // 必填验证
      if (required && !value) {
        errors.push(t('validation.required', { field: label || name }));
      }
      
      onValidate(errors.length > 0 ? errors[0] : null);
    }
  }, [onBlur, onValidate, value, required, label, name, t]);

  // 渲染单个复选框/开关
  const renderSingleControl = () => {
    const ControlComponent = widget === 'switch' ? Switch : Checkbox;
    
    return (
      <FormControlLabel
        control={
          <ControlComponent
            name={name}
            checked={Boolean(value)}
            onChange={handleChange}
            onBlur={handleBlur}
            disabled={disabled}
            readOnly={readonly}
            color={color}
            size={size}
            {...uiProps}
            {...props}
          />
        }
        label={label}
        labelPlacement={labelPlacement}
        disabled={disabled}
      />
    );
  };

  // 渲染多选复选框组
  const renderMultipleCheckboxes = () => {
    if (!enumValues || !Array.isArray(enumValues)) {
      return null;
    }

    const currentValues = Array.isArray(value) ? value : [];

    return (
      <FormGroup row={inline}>
        {enumValues.map((optionValue, index) => {
          const optionLabel = enumNames?.[index] || optionValue;
          const isChecked = currentValues.includes(optionValue);
          
          return (
            <FormControlLabel
              key={optionValue}
              control={
                <Checkbox
                  name={`${name}[${index}]`}
                  checked={isChecked}
                  onChange={handleMultipleChange(optionValue)}
                  onBlur={handleBlur}
                  disabled={disabled}
                  readOnly={readonly}
                  color={color}
                  size={size}
                />
              }
              label={optionLabel}
              labelPlacement={labelPlacement}
              disabled={disabled}
            />
          );
        })}
      </FormGroup>
    );
  };

  // 判断是否为多选模式
  const isMultiple = widget === 'checkboxes' || (enumValues && Array.isArray(enumValues));

  return (
    <Box className="schema-form-field">
      <FormControl
        component="fieldset"
        error={Boolean(error && touched)}
        disabled={disabled}
        required={required}
      >
        {/* 当有多个选项时显示标签 */}
        {isMultiple && label && (
          <FormLabel component="legend">
            {label}
          </FormLabel>
        )}
        
        {/* 渲染控件 */}
        {isMultiple ? renderMultipleCheckboxes() : renderSingleControl()}
        
        {/* 帮助文本和错误信息 */}
        {((error && touched) || help) && (
          <FormHelperText>
            {(error && touched) ? error : (
              help && (
                <Typography variant="caption" color="text.secondary">
                  {help}
                </Typography>
              )
            )}
          </FormHelperText>
        )}
      </FormControl>
    </Box>
  );
};

export default SchemaCheckbox;
