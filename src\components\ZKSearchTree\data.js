function generateRandomId() {
    return Math.floor(Math.random() * 1000000);
}

export const Data = [
    {
        title: "中国",
        value: generateRandomId(),
        key: "china",
        children: [
            {
                title: "北京",
                value: generateRandomId(),
                key: "china-beijing",
                children: [
                    {
                        title: "朝阳区",
                        value: generateRandomId(),
                        key: "china-beijing-chaoyang",
                        isLeaf: true
                    },
                    {
                        title: "海淀区",
                        value: generateRandomId(),
                        key: "china-beijing-haidian",
                        isLeaf: true
                    },
                ],
            },
            {
                title: "上海",
                value: generateRandomId(),
                key: "china-shanghai",
                children: [
                    {
                        title: "浦东新区",
                        value: generateRandomId(),
                        key: "china-shanghai-pudong",
                    },
                    {
                        title: "黄浦区",
                        value: generateRandomId(),
                        key: "china-shanghai-huangpu",
                    },
                ],
            },
        ],
    },
    {
        title: "美国",
        value: generateRandomId(),
        key: "usa",
        children: [
            {
                title: "加利福尼亚州",
                value: generateRandomId(),
                key: "usa-california",
                children: [
                    {
                        title: "洛杉矶",
                        value: generateRandomId(),
                        key: "usa-california-los-angeles",
                    },
                    {
                        title: "旧金山",
                        value: generateRandomId(),
                        key: "usa-california-san-francisco",
                    },
                ],
            },
            {
                title: "纽约州",
                value: generateRandomId(),
                key: "usa-new-york",
                children: [
                    {
                        title: "纽约市",
                        value: generateRandomId(),
                        key: "usa-new-york-new-york-city",
                    },
                    {
                        title: "布法罗",
                        value: generateRandomId(),
                        key: "usa-new-york-buffalo",
                    },
                ],
            },
        ],
    },
    {
        title: "日本",
        value: generateRandomId(),
        key: "japan",
        children: [
            {
                title: "东京都",
                value: generateRandomId(),
                key: "japan-tokyo",
                children: [
                    {
                        title: "涩谷区",
                        value: generateRandomId(),
                        key: "japan-tokyo-shibuya",
                    },
                    {
                        title: "新宿区",
                        value: generateRandomId(),
                        key: "japan-tokyo-shinjuku",
                    },
                ],
            },
            {
                title: "大阪府",
                value: generateRandomId(),
                key: "japan-osaka",
                children: [
                    {
                        title: "大阪市",
                        value: generateRandomId(),
                        key: "japan-osaka-osaka-city",
                    },
                    {
                        title: "堺市",
                        value: generateRandomId(),
                        key: "japan-osaka-sakai",
                    },
                ],
            },
        ],
    },
    {
        title: "德国",
        value: generateRandomId(),
        key: "germany",
        children: [
            {
                title: "巴伐利亚州",
                value: generateRandomId(),
                key: "germany-bavaria",
                children: [
                    {
                        title: "慕尼黑",
                        value: generateRandomId(),
                        key: "germany-bavaria-munich",
                    },
                    {
                        title: "纽伦堡",
                        value: generateRandomId(),
                        key: "germany-bavaria-nuremberg",
                    },
                ],
            },
            {
                title: "柏林州",
                value: generateRandomId(),
                key: "germany-berlin",
                children: [
                    {
                        title: "米特区",
                        value: generateRandomId(),
                        key: "germany-berlin-mitte",
                    },
                    {
                        title: "夏洛滕堡区",
                        value: generateRandomId(),
                        key: "germany-berlin-charlottenburg",
                    },
                ],
            },
        ],
    },
    {
        title: "英国",
        value: generateRandomId(),
        key: "uk",
        children: [
            {
                title: "英格兰",
                value: generateRandomId(),
                key: "uk-england",
                children: [
                    {
                        title: "伦敦",
                        value: generateRandomId(),
                        key: "uk-england-london",
                    },
                    {
                        title: "曼彻斯特",
                        value: generateRandomId(),
                        key: "uk-england-manchester",
                    },
                ],
            },
            {
                title: "苏格兰",
                value: generateRandomId(),
                key: "uk-scotland",
                children: [
                    {
                        title: "爱丁堡",
                        value: generateRandomId(),
                        key: "uk-scotland-edinburgh",
                    },
                    {
                        title: "格拉斯哥",
                        value: generateRandomId(),
                        key: "uk-scotland-glasgow",
                    },
                ],
            },
        ],
    },
    {
        title: "法国",
        value: generateRandomId(),
        key: "france",
        children: [
            {
                title: "法兰西岛大区",
                value: generateRandomId(),
                key: "france-ile-de-france",
                children: [
                    {
                        title: "巴黎",
                        value: generateRandomId(),
                        key: "france-ile-de-france-paris",
                    },
                    {
                        title: "凡尔赛",
                        value: generateRandomId(),
                        key: "france-ile-de-france-versailles",
                    },
                ],
            },
            {
                title: "普罗旺斯-阿尔卑斯-蓝色海岸大区",
                value: generateRandomId(),
                key: "france-provence-alpes-cote-dazur",
                children: [
                    {
                        title: "马赛",
                        value: generateRandomId(),
                        key: "france-provence-alpes-cote-dazur-marseille",
                    },
                    {
                        title: "尼斯",
                        value: generateRandomId(),
                        key: "france-provence-alpes-cote-dazur-nice",
                    },
                ],
            },
        ],
    },
    {
        title: "加拿大",
        value: generateRandomId(),
        key: "canada",
        children: [
            {
                title: "安大略省",
                value: generateRandomId(),
                key: "canada-ontario",
                children: [
                    {
                        title: "多伦多",
                        value: generateRandomId(),
                        key: "canada-ontario-toronto",
                    },
                    {
                        title: "渥太华",
                        value: generateRandomId(),
                        key: "canada-ontario-ottawa",
                    },
                ],
            },
            {
                title: "魁北克省",
                value: generateRandomId(),
                key: "canada-quebec",
                children: [
                    {
                        title: "蒙特利尔",
                        value: generateRandomId(),
                        key: "canada-quebec-montreal",
                    }
                ]
            }
        ]
    }
]

export function generateData(x = 3, y = 2, z = 1, gData = []) {
    // x：每一级下的节点总数。y：每级节点里有y个节点、存在子节点。z：树的level层级数（0表示一级）
    function _loop(_level, _preKey, _tns) {
        const preKey = _preKey || '0';
        const tns = _tns || gData;

        const children = [];
        for (let i = 0; i < x; i++) {
            const key = `${preKey}-${i}`;
            tns.push({
                label: `${key}-label`,
                value: `${key}-value`,
                key,
                disabled: key === '0-0-0-1' || false,
            });
            if (i < y) {
                children.push(key);
            }
        }
        if (_level < 0) {
            return tns;
        }
        const __level = _level - 1;
        children.forEach((key, index) => {
            tns[index].children = [];
            return _loop(__level, key, tns[index].children);
        });

        return null;
    }
    _loop(z);
    return gData;
}