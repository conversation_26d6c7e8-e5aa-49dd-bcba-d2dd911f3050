import React, { useEffect, useState } from "react";
import { getPageRoleList } from "@s/api/premission";
import { deteleRoles } from "@s/api/premission";
import ZktecoTable from "@/components/ZktecoTable/index";
import { useNavigate } from "react-router-dom";
import LayoutList from "@/layout/components/LayoutList.jsx";
import CustomDelete from "@c/Toast/CustomDelete";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import AppTap from "@/components/AppTap.jsx";
import useDict from "@/hooks/useDict.js";
import DictTag from "@c/DictTag";
function TableList(props) {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const dicts = useDict(["department_type"]);
  const [selectedValue, setSelectedValue] = useState(
    sessionStorage.getItem("APP_TYPE") || "L3"
  );

  const [deleteOpen, setDeleteOpen] = useState(false);
  const [currentId, setCurrentId] = useState("");
  const [deleteCompleted, setDeleteCompleted] = useState(false);
  const [data, setData] = useState([]);
  const [rowCount, setRowCount] = useState(0);

  // 表格加载
  const [isLoading, setIsLoading] = useState(false);

  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  const [isError, setIsError] = useState(false);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const loadData = useCallback((value, pagination) => {
    let params = {
      applicationCode: value,
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
    };
    setIsLoading(true);
    try {
      getPageRoleList(params).then((res) => {
        setData(res?.data?.data || []);
        setRowCount(res.data.total || 0);
      });
    } catch {
      setIsError(true);
    } finally {
      setIsLoading(false);
      setDeleteOpen(false);
      setIsRefetching(false);
    }
  }, []);

  useEffect(() => {
    loadData(selectedValue, pagination);
  }, [selectedValue, pagination.pageIndex, pagination.pageSize]);

  const handlerDelete = () => {
    setIsLoading(true);

    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    try {
      deteleRoles(currentId).then((res) => {
        toast.success(res.message);
        setDeleteOpen(false);
        loadData(selectedValue);
      });
    } catch (error) {
      setIsError(true);
    } finally {
      setIsLoading(false);
      setDeleteOpen(false);
      setIsRefetching(false);
    }
  };

  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("branch.name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "departmentName",
        header: t("roles.department_name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "departmentType",
        header: t("roles.department_level"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={dicts.current?.department_type}
              fieldName={{
                value: "value",
                title: "label",
                listClass: "listClass",
              }}
              value={row.original.departmentType}
            />
          );
        },
      },
    ],
    []
  );

  const isShowAction = {
    // isShowView: "auth:role:query",
    isShowEditor: "auth:role:update",
    isShowDetele: "auth:role:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/view/branch", { state: { id: data?.id, type: "view" } }),
      handlerEditor: (id) =>
        navigate("/add/premission/roles", {
          state: {
            id,
            type: "editor",
            applicationCode: selectedValue,
          },
        }),
      onAdd: (data) => {
        navigate("/add/premission/roles", {
          state: {
            type: "add",
            applicationCode: selectedValue,
          },
        });
        sessionStorage.setItem("APP_TYPE", selectedValue);
      },
      Detele: (data) => {
        setDeleteOpen(true);
        setCurrentId(id);
      },
    }),
    [selectedValue]
  );

  const renderTable = () => {
    return (
      <ZktecoTable
        columns={columns}
        data={data}
        rowCount={rowCount}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        loadDada={() => loadData(selectedValue, pagination)}
        paginationProps={{
          currentPage: pagination.pageIndex,
          rowsPerPage: pagination.pageSize,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
        }}
        topActions={{
          showAdd: "auth:role:save",
          onAdd: actionHandlers.onAdd, // 添加这一行
        }}
        actionHandlers={actionHandlers}
        isShowAction={isShowAction}
      />
    );
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("roles.title")}
        header={
          <AppTap
            isShowL3={true}
            value={selectedValue}
            setValue={setSelectedValue}></AppTap>
        }
        content={renderTable()}></LayoutList>

      <CustomDelete
        open={deleteOpen}
        setOpen={setDeleteOpen}
        handlerDetele={handlerDelete}
        title={t("common.common_delete_confirm")}
        content={t("common.common_delete_sure")}
        onContent={t("common.common_delete_not")}
        noDelete={deleteCompleted}></CustomDelete>
    </React.Fragment>
  );
}

export default TableList;

//  <>
//         <Grid
//           sx={{
//             width: "100%",
//             height: "64px",
//             background: " #F0F0F0 0% 0% no-repeat padding-box",
//             borderRadius: "10px 10px 0px 0px",
//             display: "flex",
//             justifyContent: "space-between",
//           }}
//           container>
//           <Grid item>
//             <Typography
//               sx={{
//                 font: "normal normal bold 18px/20px Roboto",
//                 color: "#474B4F",
//                 lineHeight: "70px",
//                 ml: 4,
//               }}>
//               {t("roles.name")}
//             </Typography>
//           </Grid>

//           <Grid item>
//             <Button
//               style={{
//                 background: `transparent url('${RefreshIcon}') 50% 50% no-repeat padding-box`,
//               }}
//               onClick={() => loadData(selectedValue)}>
//               <RefreshIcon></RefreshIcon>
//             </Button>

//             <AuthButton button="auth:role:save">
//               <Button
//                 style={{
//                   background: `transparent url('${AddIcon}') 50% 50% no-repeat padding-box`,
//                 }}
//                 onClick={() => {
//                   navigate("/add/premission/roles", {
//                     state: {
//                       type: "add",
//                       applicationCode: selectedValue,
//                     },
//                   });
//                   sessionStorage.setItem("APP_TYPE", selectedValue);
//                 }}>
//                 <AddIcon></AddIcon>
//               </Button>
//             </AuthButton>
//           </Grid>
//         </Grid>

//         <Grid
//           sx={{
//             width: "100%",
//             height: "72%",
//             background: "#fff",
//             borderRadius: "0px 0px 10px 10px",
//           }}>
//           <Grid>
//             <CustomTree
//               treeData={treeData}
//               buttonAuth={{
//                 update: "auth:role:update",
//                 detele: "auth:role:delete",
//                 view: "auth:role:query",
//               }}
//               handlerEditor={(id) => {
//                 navigate("/add/premission/roles", {
//                   state: {
//                     id,
//                     type: "editor",
//                     applicationCode: selectedValue,
//                   },
//                 });

//                 sessionStorage.setItem("APP_TYPE", selectedValue);
//               }}
//               handlerDetele={(id) => {
//                 setDeleteOpen(true);
//                 setCurrentId(id);
//               }}
//               handlerView={(id) => {
//                 navigate("/add/premission/roles", {
//                   state: {
//                     id,
//                     type: "view",
//                     applicationCode: selectedValue,
//                   },
//                 });
//               }}></CustomTree>
//           </Grid>
//         </Grid>
//       </>
