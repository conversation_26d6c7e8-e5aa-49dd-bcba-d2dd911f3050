import React from "react";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/ViewBox";
import { Avatar } from "@mui/material";
import { getBranchDetail } from "./utils";
import ViewBox from "@/components/ViewBox";
import { useLocation, useNavigate } from "react-router-dom";
function ViewEmplyee(props) {
  // 国际化
  const { t } = useTranslation();
  const { state } = useLocation();
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [imageUrl, setImageUrl] = useState("");
  useEffect(() => {
    //  获取 租户用户详情
    if (state?.type == "view" || state?.type == "editor") {
      getBranchDetail(state?.id, setData, setImageUrl);
    }
  }, []);

  return (
    <React.Fragment>
      <RightViewLayout
        title={t("principal_user.view")}
        navigateBack={"/principal/employee/list"}
        handleCancle={() => {
          navigate("/principal/employee/list");
        }}
        isShowSave={false}>
        <Grid container direction={"column"} gap={4}>
          <Avatar
            className="avatar radial-button"
            alt="ZK"
            src={imageUrl}
            sx={{
              width: "120px",
              height: "120px",
              borderRadius: "50%",
            }}></Avatar>

          <ViewBox
            title={t("branch_user.firstName")}
            content={data?.firstName}
          />

          <ViewBox title={t("branch_user.lastName")} content={data?.lastName} />

          <ViewBox title={t("partner_user.email")} content={data?.email} />

          <ViewBox
            title={t("common.common_mobile")}
            content={
              data?.countryCode && data?.phone
                ? `+ ${data?.countryCode} ${data?.phone}`
                : "-"
            }
          />

          <ViewBox title={t("principal.address")} content={data?.remark} />
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default ViewEmplyee;
