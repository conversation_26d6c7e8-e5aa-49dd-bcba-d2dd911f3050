import React from "react";
import { useStateUserInfo } from "@/hooks/user";
import {
  UserOutlined,
  CreditCardOutlined,
  MailOutlined,
  ClusterOutlined,
  MobileOutlined,
  EnvironmentOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { Typography } from "@mui/material";

function Left(props) {
  const { t } = useTranslation();
  const userInfor = useStateUserInfo();
  const [open, setOpen] = useState(false);
  const handleFace = () => {
    setOpen(!open);
  };
  const nickName = () => {
    if (userInfor.firstName && userInfor.lastName) {
      return userInfor.firstName + " " + userInfor.lastName;
    } else if (userInfor.firstName) {
      return userInfor.firstName;
    } else {
      return "SCREEN DIRECT";
    }
  };
  return (
    <div>
      <Paper
        elevation={0}
        sx={{
          minHeight: "calc(100vh - 40px)",
          boxShadow:
            "rgb(145 158 171 / 20%) 0px 0px 2px 0px, rgb(145 158 171 / 12%) 0px 3px 10px -4px",
          width: "100%",
          background: "white",
          borderRadius: "10px",
        }}
      >
        <Box sx={{ padding: "20px 0px 0 20px" }}>
          <Typography variant="h4">User's Profile</Typography>
        </Box>
        <Box
          sx={{
            height: 230,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Avatar
            className="avatar radial-button"
            alt="ZK"
            sx={{ width: "110px", height: "110px", borderRadius: "8px" }}
          ></Avatar>
        </Box>

        <Box sx={{ height: 230, padding: "10px 20px 0 20px" }}>
          <Stack
            direction="column"
            justifyContent="flex-start"
            alignItems="flex-start"
            spacing={2}
          >
            <span
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <UserOutlined style={{ marginRight: "10px" }} />
              {nickName()}
            </span>
            <span
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <CreditCardOutlined style={{ marginRight: "10px" }} />
              {userInfor?.roleName}
            </span>
            <span
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <EnvironmentOutlined style={{ marginRight: "10px" }} />
              {userInfor.location}
            </span>

            {userInfor.email && (
              <span
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <MailOutlined style={{ marginRight: "10px" }} />
                {userInfor.email}
              </span>
            )}
            {userInfor.phone && !/^\+00-/.test(userInfor.phone) && (
              <span
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <MobileOutlined style={{ marginRight: "10px" }} />
              </span>
            )}
            {userInfor.companyName && (
              <span
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <ClusterOutlined style={{ marginRight: "10px" }} />
                {userInfor.companyName}
              </span>
            )}
          </Stack>
        </Box>
      </Paper>
    </div>
  );
}

export default Left;
