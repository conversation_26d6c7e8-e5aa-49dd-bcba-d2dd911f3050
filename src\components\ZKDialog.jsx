import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BootstrapContent,
  Bootstrap<PERSON><PERSON><PERSON>,
  BootstrapDialogTitle,
} from "@c/dialog";
import { useTranslation } from "react-i18next";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@/assets/Icons/CloseIcon.svg?react";
import AnimateButton from "@c/@extended/AnimateButton";
import LoadingButton from "@mui/lab/LoadingButton";

function ZKDialog(props) {
  const { open, setOpen, title, children, handlerSubmit, sx, width, loading } =
    props;
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <BootstrapDialog
        open={open}
        onClose={() => setOpen(false)}
        aria-describedby="alert-dialog-slide-description"
        fullWidth
        sx={{
          "& .MuiDialog-paperFullWidth": {
            maxWidth: width,
          },
        }}>
        <BootstrapDialogTitle
          sx={{
            borderRadius: "10px",
          }}>
          <Typography variant="h5" component="p">
            {title}
          </Typography>

          <IconButton
            aria-label="close"
            onClick={() => setOpen(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}>
            <CloseIcon />
          </IconButton>
        </BootstrapDialogTitle>
        <BootstrapContent sx={sx}>{children}</BootstrapContent>
        <BootstrapActions
          sx={{
            display: "flex",
            justifyContent: "end",
          }}>
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              onClick={() => setOpen(false)}
              style={{
                width: "152px",
                height: "48px",
                borderRadius: "8px",
                opacity: 1,
              }}
              disableElevation
              color="info"
              size="medium">
              {t("common.common_edit_cancel")}
            </Button>

            <AnimateButton>
              <LoadingButton
                loading={loading}
                disableElevation
                disabled={loading}
                fullWidth
                type="submit"
                variant="contained"
                onClick={() => handlerSubmit()}
                style={{
                  width: "152px",
                  height: "48px",
                  borderRadius: "8px",
                  opacity: 1,
                  background:
                    "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                }}>
                {t("common.common_edit_ok")}
              </LoadingButton>
            </AnimateButton>
          </Stack>
        </BootstrapActions>
      </BootstrapDialog>
    </React.Fragment>
  );
}

export default ZKDialog;
