import React, { useEffect, useState } from "react";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import CustomInput from "@c/CustInput";
import { pxToRem } from "@u/zkUtils";
import { useTranslation } from "react-i18next";
import {
  getPermissionList,
  addRoles,
  editRoles,
  getRoleDetail,
} from "@s/api/premission";
import { toast } from "react-toastify";
import MenuTreeCard from "./MenuTreeCard";
import * as Yup from "yup";
import { useFormik } from "formik";
import { handlderSelectNode } from "./checkNode";
import { useLocation, useNavigate } from "react-router-dom";

function AddPermission(props) {
  // 国际化
  const { t } = useTranslation();
  const { state } = useLocation();

  const navigate = useNavigate();
  const [permissionTree, setPermissionTree] = useState([]);
  const [selectedNode, setSelectedNode] = useState([]);
  //保存按钮
  const [loading, setLoading] = React.useState(false);
  // 获取菜单权限列表
  useEffect(() => {
    let params = {
      applicationCode: state?.applicationCode,
    };
    getPermissionList(params).then((res) => {
      if (res?.code == "00000000") {
        setPermissionTree(res?.data);
      } else {
        setPermissionTree([]);
      }
    });

    if (state?.type == "editor" || state?.type == "view") {
      getRoleDetail(state?.id?.id).then((res) => {
        if (res?.code == "00000000") {
          areaFormik.setFieldValue("name", res?.data?.name);
          setSelectedNode(res?.data?.selectedResourceIds);
        } else {
          setSelectedNode([]);
        }
      });
    }
  }, [state?.applicationId]);

  // 渲染树节点
  const renderTree = (nodes) => {
    return nodes?.map((node) => (
      <MenuTreeCard
        menuList={[node]}
        selectedNode={selectedNode}
        onChange={(parentId, id) =>
          handlderSelectNode(parentId, permissionTree, setSelectedNode)
        }>
        {node.children && renderTree(node.children)}
      </MenuTreeCard>
    ));
  };

  // 添加表单
  const areaFormik = useFormik({
    initialValues: {
      name: "",
      applicationCode: state?.applicationCode,
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      setLoading(true);

      try {
        if (state.type == "editor") {
          let params = {
            ...values,
            id: state?.id?.id,
            resourceIds: selectedNode,
          };
          editRoles(params)
            .then((res) => {
              navigate("/premission/setting");
              toast.success(res?.message);
            })
            .finally(() => {
              setLoading(false);
            });
        } else {
          let params = {
            ...values,
            id: state?.id,
            resourceIds: selectedNode,
          };
          addRoles(params)
            .then((res) => {
              navigate("/premission/setting");
              toast.success(res?.message);
            })
            .finally(() => {
              setLoading(false);
            });
        }
      } finally {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
        setLoading(false);
      }
    },

    validationSchema: Yup.object().shape({
      name: Yup.string()
        .max(60, t("common.common_rule_area_len60"))
        .required(t("common.common_area_name_not_null")),
    }),
  });

  return (
    <React.Fragment>
      <RightViewLayout
        navigateBack={"/premission/setting"}
        title={
          state?.type === "editor"
            ? t("roles.edit_auth_level")
            : state?.type === "view"
            ? t("roles.view_auth_level")
            : t("roles.add_auth_level")
        }
        handleSubmit={areaFormik.handleSubmit}
        handleCancle={() => {
          navigate("/premission/setting");
        }}
        loading={loading}>
        <Grid
          sx={{
            p: 3,
            width: "100%",
          }}>
          <Grid
            sx={{
              border: "1px solid #c186861a",
              borderRadius: "15px",
              width: "100%",
              height: "113px",
              boxShadow: "0px 0px 6px #0000001A",
            }}>
            <Grid
              sx={{
                width: pxToRem(400),
                m: 2,
                pl: 4,
              }}>
              <CustomInput
                label={t("roles.name")}
                placeholder={t("roles.enter_roles_name")}
                name="name"
                required
                disabled={state?.type == "view"}
                value={areaFormik.values.name}
                handleBlur={areaFormik.handleBlur}
                handleChange={areaFormik.handleChange}></CustomInput>
            </Grid>
          </Grid>

          <Grid>{renderTree(permissionTree)}</Grid>
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddPermission;
