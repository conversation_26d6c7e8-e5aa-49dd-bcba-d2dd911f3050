import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@c/dialog";
import AnimateButton from "@c/@extended/AnimateButton";
import LoadingButton from "@mui/lab/LoadingButton";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@/assets/Icons/CloseIcon.svg?react";
import DeviceIcon from "@/assets/Images/device.png";
import { RespCode } from "@/enums/RespCode.js";
import { Stack } from "@mui/material";
import { timeZoneList } from "@/enums/TimeZone";
import { useFormik } from "formik";
import { queryOutletList } from "@s/api/outlet";
import { addDevice, editDevice } from "@/service/api/device.js";
import CustomAutocomplete from "@c/ZKSelect.jsx";
import ZoonAutocomplete from "@c/ZKSelect.jsx";
import ZkInput from "@c/CustInput";
function DialogDevice(props) {
  const { open, setOpen, preDeviceInfo, selectSence, data, applicationCode } =
    props;
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [loading, setLoading] = React.useState(false);
  const [outletList, setOutList] = useState([]);
  const [selectTimeZoon, setSelectTimeZoon] = useState(null);
  const [selectOutlet, setSelectOutlet] = useState(null);

  useEffect(() => {
    if (open) {
      queryOutletList({
        name: "",
        applicationCode: applicationCode,
        parentId: "",
      }).then((res) => {
        setOutList(res?.data);
      });
    }
  }, [open]);

  // 表单赋值
  const setFormData = (item) => {
    setSelectOutlet(item?.departmentId);
    areaFormik.setValues(
      {
        id: item?.id,
        outlet: item?.departmentId,
        category: item?.category,
        photo: item?.photo,
        departmentId: item?.departmentId,
        name: item?.name,
        wide: item?.wide,
        high: item?.high,
        direction: item?.direction,
        deviceType: item?.deviceType,
        timezone: item?.timezone,
      },
      true
    );
  };

  useEffect(() => {
    let data = outletList?.find((item) => {
      return item.id == selectOutlet;
    });

    let timezoon = timeZoneList?.find((item) => item.id == data?.timezone);

    setSelectTimeZoon(timezoon?.id);
  }, [selectOutlet, data]);

  const areaFormik = useFormik({
    initialValues: {
      id: "",
      outlet: "",
      photo: "",
      name: "",
      wide: "",
      high: "",
      direction: "",
      deviceType: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      let params = {
        ...values,
        dmsDeviceId: preDeviceInfo?.dmsDeviceId,
        mainOrSub: selectSence,
        dmsDeviceType: preDeviceInfo?.dmsDeviceType,
        sn: preDeviceInfo?.sn,
        departmentId: selectOutlet,
        timezone: selectTimeZoon,
        applicationCode: applicationCode,
      };

      try {
        if (state?.type == "add") {
          setLoading(true);
          const response = await addDevice(params);
          toast.success(response?.message);
        } else {
          setLoading(true);
          const response = await editDevice(params);
          toast.success(response?.message);
        }
      } finally {
        setLoading(false);
        setOpen(false);
        navigate("/device/manage/list");
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });

  useEffect(() => {
    setFormData(data);
  }, [open, data]);

  return (
    <React.Fragment>
      <BootstrapDialog
        open={open}
        onClose={() => setOpen(false)}
        aria-describedby="alert-dialog-slide-description"
        sx={{
          "& .MuiPaper-root": {
            width: "1200px",
            borderRadius: "8px",
          },
        }}>
        <BootstrapDialogTitle
          sx={{
            borderRadius: "10px",
          }}>
          <Typography variant="label" component="p">
            {t("device.bind_device")}
          </Typography>

          <IconButton
            aria-label="close"
            onClick={() => setOpen(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}>
            <CloseIcon />
          </IconButton>
        </BootstrapDialogTitle>
        <BootstrapContent>
          <Grid container xs={12}>
            <Grid
              item
              xs={12}
              sx={{
                width: "100%",
                height: "100px",
                borderRadius: "10px",
                border: "2px solid #eaeaea",
                display: "flex",
              }}>
              <img
                src={DeviceIcon}
                alt="加载失败"
                style={{
                  width: "75px",
                  height: "65px",
                  margin: "15px 0 10px 20px",
                }}
              />

              <Grid
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  ml: 2,
                }}>
                <Stack
                  sx={{
                    fontSize: "16px",
                  }}>
                  {preDeviceInfo?.dmsDeviceType}
                </Stack>
                <Stack
                  sx={{
                    fontSize: "16px",
                    color: "#abadaf",
                    marginTop: "5px",
                  }}>
                  {`SN:${preDeviceInfo?.sn}`}
                </Stack>
              </Grid>
            </Grid>

            <Grid
              item
              xs={12}
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
              }}>
              <Stack
                sx={{
                  fontSize: "14px",
                  color: "#7d7f82",
                  mt: 3,
                }}>
                {t("device.bind_device_tips")}
              </Stack>
              <Stack
                sx={{
                  fontSize: "14px",
                  color: "#63afdb",
                  mt: 1,
                }}>
                {t("device.bind_device_site")}
              </Stack>
            </Grid>

            <Grid container xs={12} spacing={2} mb={4} mt={2}>
              <Grid item xs={6}>
                <ZkInput
                  name="name"
                  placeholder={t("device.device_title")}
                  label={t("device.device_title")}
                  value={areaFormik.values.name}
                  required
                  formik={areaFormik}></ZkInput>
              </Grid>

              <Grid item xs={6}>
                <CustomAutocomplete
                  label={t("outlets.outlet")}
                  required
                  value={selectOutlet}
                  isClear={false}
                  onChange={(e) => {
                    setSelectOutlet(e.target.value);
                  }}
                  disabled={state?.type == "editor"}
                  labelOptions={{
                    label: "name",
                    value: "id",
                  }}
                  placeholder={t("common.common_plese_select")}
                  options={outletList}></CustomAutocomplete>
              </Grid>
              <Grid item xs={6}>
                <ZoonAutocomplete
                  label={t("outlets.time_zone")}
                  required
                  value={selectTimeZoon}
                  isClear={false}
                  disabled={true}
                  labelOptions={{
                    label: "name",
                    value: "id",
                  }}
                  options={timeZoneList}></ZoonAutocomplete>
              </Grid>
            </Grid>
          </Grid>
        </BootstrapContent>
        <BootstrapActions
          sx={{
            display: "flex",
            justifyContent: "end",
          }}>
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              onClick={() => setOpen(false)}
              style={{
                width: "132px",
                height: "48px",
                borderRadius: "8px",
                opacity: 1,
              }}
              disableElevation
              color="info"
              size="medium">
              {t("common.common_edit_cancel")}
            </Button>
            <AnimateButton>
              <LoadingButton
                loading={loading}
                disableElevation
                disabled={areaFormik?.isSubmitting}
                fullWidth
                type="submit"
                variant="contained"
                onClick={areaFormik.handleSubmit}
                style={{
                  width: "132px",
                  height: "48px",
                  borderRadius: "8px",
                  opacity: 1,
                  background:
                    "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                }}>
                {t("device.add")}
              </LoadingButton>
            </AnimateButton>
          </Stack>
        </BootstrapActions>
      </BootstrapDialog>
    </React.Fragment>
  );
}

export default DialogDevice;
