import React from "react";
import { Grid, Typography } from "@mui/material";
import { pxToRem } from "@u/zkUtils";
import _ from "lodash-es";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { t } from "i18next";
function LayoutList(props) {
  const {
    title,
    header,
    content,
    isSearch,
    serchName,
    onClick = () => {},

    setSeachName,
    onClear, // 新增 onClear 属性
    disabled,
    ...orther
  } = props;

  const throttledSetSearchName = useMemo(
    () =>
      _.throttle((value) => {
        setSeachName(value);
      }, 300),
    []
  );

  // 组件卸载时取消节流，防止内存泄露
  useEffect(() => {
    return () => {
      throttledSetSearchName.cancel();
    };
  }, [throttledSetSearchName]);

  return (
    <React.Fragment>
      <Grid
        sx={{
          display: "flex",
          flexDirection: "column",
          height: "100%",
          position: "relative",
        }}>
        <Grid
          sx={{
            width: "100%",
            background: "#fff",
            borderRadius: "8px",
            height: "80px",
            display: "flex",
            justifyContent: "space-between",
            boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
            boder: "1px solid rgba(0, 0, 0, 0.1)",
          }}>
          <Typography
            variant="h4"
            sx={{
              lineHeight: "80px",
              marginLeft: "30px",
            }}>
            {title}
          </Typography>
          {isSearch && (
            <Grid
              sx={{
                m: 2,
                pr: 2,
                minWidth: "350px",
              }}>
              <Stack
                sx={{
                  flexGrow: 1,
                  width: "100%",
                }}>
                <OutlinedInput
                  id={"zkInput_" + name}
                  type="text"
                  autoComplete="off"
                  value={serchName || ""}
                  onChange={(e) => throttledSetSearchName(e.target.value)}
                  onKeyDown={(event) => {
                    if (event.key == "Enter") {
                      onClick(); // 触发搜索操作
                    }
                  }}
                  placeholder={t("common.common_search_name")}
                  fullWidth
                  sx={{
                    width: "100%",
                    "& .MuiOutlinedInput-input": {
                      height: pxToRem(25),
                      fontSize: "14px",
                    },
                    borderRadius: "7px",
                  }}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => {
                          setSeachName(""); // 将 null 改为空字符串
                          onClear();
                        }}>
                        <ClearIcon
                          fontSize="small"
                          sx={{
                            color: "#BEBEBE",
                            cursor: "pointer",
                          }}
                        />
                      </IconButton>

                      <IconButton
                        aria-label="toggle password visibility"
                        edge="end"
                        size="large"
                        sx={{
                          color: "#cacaca",
                        }}
                        onClick={onClick}>
                        <SearchIcon></SearchIcon>
                      </IconButton>
                    </InputAdornment>
                  }
                  disabled={disabled}
                  {...orther}
                />
              </Stack>
            </Grid>
          )}
        </Grid>

        <Grid
          sx={{
            mt: 2,
          }}>
          {header}
        </Grid>

        <Grid
          sx={{
            mt: 2,
            flexGrow: 1,
            position: "relative",
            background: ` #FFFFFF 0% 0% no-repeat padding-box`,
            borderRadius: "10px",
            boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
            boder: "1px solid rgba(0, 0, 0, 0.1)",
          }}>
          {content}
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default LayoutList;
