const timeZoneData = [
  {
    id: "-12:00",
    name: "(GMT-12:00) International Date Line West",
  },
  {
    id: "-11:00",
    name: "(UTC-11)Coordinated Universal Time-11",
  },
  {
    id: "-10:00",
    name: "(UTC-10)Hawaii",
  },
  {
    id: "-09:00",
    name: "(UTC-9)Alaska",
  },
  {
    id: "-08:00",
    name: "(UTC-8)Pacific time (American and Canada) Baja California",
  },
  {
    id: "-07:00",
    name: "(UTC-7)La Paz, The mountain time (American and Canada), Arizona",
  },
  {
    id: "-06:00",
    name: "(UTC-6)Saskatchewan, Central time, Central America",
  },
  {
    id: "-05:00",
    name: "(UTC-5)Bogota, Lima, Quito, Rio Branco, Eastern time, Indiana(East)",
  },
  {
    id: "-04:30",
    name: "(UTC-4:30)Caracas",
  },
  {
    id: "-04:00",
    name: "(UTC-4)Atlantic time, Cuiaba, Georgetown, La Paz, Santiago",
  },
  {
    id: "-03:30",
    name: "(UTC-3:30)Newfoundland",
  },
  {
    id: "-03:00",
    name: "(UTC-3)Brasilia, Buenos Aires, Greenland, Cayenne",
  },
  {
    id: "-02:00",
    name: "(UTC-2)The International Date Line West-02",
  },
  {
    id: "-01:00",
    name: "(UTC-1)Cape Verde Islands, Azores",
  },
  {
    id: "-00:00",
    name: "(UTC)Dublin, Edinburgh, Lisbon, London, The International Date Line West",
  },
  {
    id: "+01:00",
    name: "(UTC+1)Amsterdam, Brussels, Sarajevo",
  },
  {
    id: "+02:00",
    name: "(UTC+2)Beirut, Damascus, Eastern Europe, Cairo,Athens, Jerusalem",
  },
  {
    id: "+03:00",
    name: "(UTC+3)Baghdad, Kuwait, Moscow, St Petersburg,Nairobi",
  },
  {
    id: "+03:30",
    name: "(UTC+3:30)Teheran or Tehran",
  },
  {
    id: "+04:00",
    name: "(UTC+4)Abu Dhabi, Yerevan, Baku, Port Louis, Samarra",
  },
  {
    id: "+04:30",
    name: "(UTC+4:30)Kabul",
  },
  {
    id: "+05:00",
    name: "(UTC+5)Ashgabat, Islamabad, Karachi",
  },
  {
    id: "+05:30",
    name: "(UTC+5:30)Chennai, Calcutta Mumbai, New Delhi",
  },
  {
    id: "+05:45",
    name: "(UTC+5:45)Kathmandu",
  },
  {
    id: "+06:00",
    name: "(UTC+6)Astana, Dhaka, Novosibirsk",
  },
  {
    id: "+06:30",
    name: "(UTC+6:30)Yangon",
  },
  {
    id: "+07:00",
    name: "(UTC+7)Bangkok, Hanoi, Jakarta",
  },
  {
    id: "+08:00",
    name: "(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi,Kuala Lumpur, Singapore",
  },
  {
    id: "+09:00",
    name: "(UTC+9)Osaka, Tokyo, Seoul, Yakutsk",
  },
  {
    id: "+09:30",
    name: "(UTC+9:30)Adelaide, Darwin",
  },
  {
    id: "+10:00",
    name: "(UTC+10)Brisbane, Vladivostok, Guam, Canberra",
  },
  {
    id: "+11:00",
    name: "(UTC+11)Solomon Islands, New Caledonia",
  },
  {
    id: "+12:00",
    name: "(UTC+12)Anadyr, Oakland, Wellington, Fiji",
  },
  {
    id: "+13:00",
    name: "(UTC+13)Nuku'alofa, The Samoa Islands",
  },
  {
    id: "+14:00",
    name: "(UTC+14)Christmas Island",
  },
];

export const timeZoneList = timeZoneData;
