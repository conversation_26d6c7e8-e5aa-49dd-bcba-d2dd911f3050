/* eslint-disable react-hooks/exhaustive-deps */
import React from "react";
import { operationlogList } from "@s/operationlog";
import { useFormik } from "formik";
import ZktecoTable from "@c/ZktecoTable";
import SearchForm from "@c/SearchForm";
import LayoutList from "@l/components/LayoutList";
import CustomInput from "@c/CustInput.jsx";
import { pxToRem } from "@u/zkUtils";
import CustomSelect from "@c/CustomSelect";
import ZkTooltip from "@c/ZkTooltip";
import useDict from "@/hooks/useDict.js";
import dayjs from "dayjs";

const OperationList = () => {
  const { t } = useTranslation();
  const dictData = useDict(["common_status", "site_timezone_type"]);
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      isAsc: "",
      orderBy: "DESC",
      startTime: "",
      endTime: "",
      keyWard: "",
    };

    return params;
  };

  // 获取数据
  const getTableData = () => {
    try {
      operationlogList(buildParams()).then((res) => {
        setData(res?.data?.data);
        setRowCount(res?.data?.total);
      });
    } finally {
      setIsError(true);
      setIsLoading(false);
    }
  };
  useEffect(() => {
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "operTitle", //access nested data with dot notation
        header: t("operation_log.title"),
        Cell: ({ row }) => {
          return (
            <ZkTooltip
              title={t(`${row.original.operTitle}`)}
              arrow
              placement="bottom">
              <span>{t(`${row.original.operTitle}`)}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "operName", //access nested data with dot notation
        header: t("operation_log.account"),
        Cell: ({ row }) => {
          return (
            <ZkTooltip title={row.original.operName} arrow placement="bottom">
              <span>{row.original.operName}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "ipAddr", //access nested data with dot notation
        header: t("operation_log.ip"),
        Cell: ({ row }) => {
          return (
            <ZkTooltip title={row.original.ipAddr} arrow placement="bottom">
              <span>{row.original.ipAddr}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "location", //access nested data with dot notation
        header: t("operation_log.address"),
        Cell: ({ row }) => {
          return (
            <ZkTooltip title={row.original.location} arrow placement="bottom">
              <span>{row.original.location}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "operationTime", //access nested data with dot notation
        header: t("operation_log.time"),
        Cell: ({ row }) => {
          return (
            <ZkTooltip
              title={dayjs(row.original.accessTime).format(
                "YYYY-MM-DD HH:mm:ss"
              )}
              arrow
              placement="bottom">
              <span>
                {dayjs(row.original.accessTime).format("YYYY-MM-DD HH:mm:ss")}
              </span>
            </ZkTooltip>
          );
        },
      },
    ],
    []
  );

  const loginStatus = [
    {
      id: "0",
      value: t("common.common_success"),
    },
    {
      id: "1",
      value: t("common.common_failed"),
    },
  ];

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      operName: "",
      location: "",
      status: "",
      ...buildParams(),
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      operationlogList(values)
        .then((res) => {
          if (res.code == "00000000") {
            setData(res?.data?.data);
            setRowCount(res?.data?.total);
          } else {
            setData([]);
            setRowCount(0);
          }
          setIsLoading(false);
          setIsRefetching(false);
        })
        .catch((err) => {
          setIsError(true);
          setIsLoading(false);
          setIsRefetching(false);
        });
    },
  });

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  return (
    <LayoutList
      title={t("operation_log.name")}
      header={
        <SearchForm formik={queryFormik} reloadData={getTableData}>
          <Grid container>
            <Grid item ml={2}>
              <CustomInput
                size="small"
                type="text"
                width={pxToRem(264)}
                name="account"
                fullWidth
                formik={queryFormik}
                placeholder={t("operation_log.account")}
              />
            </Grid>

            <Grid item ml={2}>
              <CustomInput
                size="small"
                type="text"
                name="userName"
                width={pxToRem(264)}
                fullWidth
                formik={queryFormik}
                placeholder={t("operation_log.ip")}
              />
            </Grid>

            <Grid item ml={2}>
              <CustomInput
                size="small"
                type="text"
                name="address"
                width={pxToRem(264)}
                fullWidth
                placeholder={t("operation_log.address")}
              />
            </Grid>

            <Grid item ml={2}>
              <CustomSelect
                name="status"
                items={loginStatus}
                formik={queryFormik}
                placeholder={t("login_log.status")}></CustomSelect>
            </Grid>
          </Grid>
        </SearchForm>
      }
      content={
        <ZktecoTable
          columns={columns}
          data={data}
          rowCount={rowCount}
          isLoading={isLoading}
          isRefetching={isRefetching}
          isError={isError}
          enableRowActions={false}
          loadDada={getTableData}
          paginationProps={{
            currentPage: pagination.pageIndex,
            rowsPerPage: pagination.pageSize,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
          }}
          topActions={{
            showAdd: false,
          }}
          isShowAction={false}
        />
      }></LayoutList>
  );
};

export default OperationList;
