import React from "react";
import PropTypes from "prop-types";
import Pagination from "@mui/material/Pagination";
function ZKPagination(props) {
  const { t } = useTranslation();
  const {
    totalRecords = 0,
    rowsPerPage = 10,
    currentPage = 0, // 默认从第0页开始
    onPageChange = () => {},
    onPageSizeChange = () => {},
  } = props;
  const totalPages = Math.ceil(totalRecords / rowsPerPage) || 0;

  const getPageDetails = () => {
    const startIndex = currentPage * rowsPerPage + 1;
    const endIndex = Math.min((currentPage + 1) * rowsPerPage, totalRecords);
    return `${startIndex}-${endIndex} of ${totalRecords}`;
  };

  return (
    <React.Fragment>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px 0px",
        }}>
        {/* 每页记录数选择器 */}
        <Box display="flex" alignItems="center">
          <InputLabel htmlFor="rowsPerPage">
            {t("common.common_perpage")}
          </InputLabel>
          <Select
            id="rowsPerPage"
            native
            value={rowsPerPage}
            onChange={(e) => onPageSizeChange(Number(e.target.value))}
            sx={{
              marginLeft: 1,
              "& .MuiNativeSelect-iconOutlined": {
                width: "1.6rem",
                height: "1.6rem",
              },
            }}>
            {[5, 10, 15, 20, 50].map((size) => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </Select>
        </Box>

        {/* 当前页详情 */}
        <Box flexGrow={1} pl={3}>
          <InputLabel>{getPageDetails()}</InputLabel>
        </Box>

        <Grid position={"absolute"} right={10}>
          <Pagination
            count={Number(totalPages) || 0}
            page={Number(currentPage) + 1} // 确保是数字并加1
            onChange={(e, page) => onPageChange(Number(page) - 1)} // 转换回 0 基索引
            variant="outlined"
            shape="rounded"
            // color="primary"
            sx={{
              "& .MuiPaginationItem-root": {
                backgroundColor: "#fff",
                color: "#637381",
              },
              "& .MuiPaginationItem-root.Mui-selected": {
                backgroundColor: "#E3E3E3",
                color: "#000",
              },
              "& .MuiPaginationItem-root:hover": {
                backgroundColor: "#ccc",
              },
            }}
          />
        </Grid>
      </Box>
    </React.Fragment>
  );
}

ZKPagination.propTypes = {
  totalRecords: PropTypes.number.isRequired,
  currentPage: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
};

export default ZKPagination;
