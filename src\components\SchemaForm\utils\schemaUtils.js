/**
 * Schema工具函数集合
 */

/**
 * 验证Schema格式
 * @param {Object} schema - 要验证的Schema
 * @returns {Object} 验证结果 { isValid: boolean, errors: Array }
 */
export const validateSchema = (schema) => {
  const errors = [];
  
  if (!schema || typeof schema !== 'object') {
    errors.push('Schema must be an object');
    return { isValid: false, errors };
  }

  // 递归验证Schema结构
  const validateNode = (node, path = '') => {
    if (!node || typeof node !== 'object') return;

    // 验证字段类型
    if (node.type && !isValidFieldType(node.type)) {
      errors.push(`Invalid field type "${node.type}" at path: ${path}`);
    }

    // 验证布局类型
    if (node.layout && !isValidLayoutType(node.layout.type)) {
      errors.push(`Invalid layout type "${node.layout.type}" at path: ${path}`);
    }

    // 递归验证子节点
    if (node.properties) {
      Object.entries(node.properties).forEach(([key, childNode]) => {
        validateNode(childNode, path ? `${path}.${key}` : key);
      });
    }

    if (node.fields) {
      node.fields.forEach((field, index) => {
        validateNode(field, `${path}.fields[${index}]`);
      });
    }

    if (node.items) {
      validateNode(node.items, `${path}.items`);
    }
  };

  validateNode(schema);
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 检查是否为有效的字段类型
 * @param {string} type - 字段类型
 * @returns {boolean}
 */
export const isValidFieldType = (type) => {
  const validTypes = [
    'string', 'number', 'integer', 'boolean', 'array', 'object',
    'email', 'password', 'url', 'tel', 'date', 'datetime', 'time',
    'select', 'radio', 'checkbox', 'textarea', 'file', 'image'
  ];
  return validTypes.includes(type);
};

/**
 * 检查是否为有效的布局类型
 * @param {string} type - 布局类型
 * @returns {boolean}
 */
export const isValidLayoutType = (type) => {
  const validTypes = [
    'group', 'grid', 'tabs', 'card', 'accordion', 'stepper', 'fieldset'
  ];
  return validTypes.includes(type);
};

/**
 * 处理条件逻辑
 * @param {Object} schema - 原始Schema
 * @param {Object} formData - 表单数据
 * @param {Object} uiSchema - UI Schema
 * @returns {Object} 处理后的Schema
 */
export const processConditionalLogic = (schema, formData, uiSchema = {}) => {
  if (!schema || !formData) return schema;

  const processNode = (node, data, path = '') => {
    if (!node || typeof node !== 'object') return node;

    const processedNode = { ...node };

    // 处理条件显示
    if (node.if && node.then) {
      const condition = evaluateCondition(node.if, data, path);
      if (condition) {
        Object.assign(processedNode, node.then);
      } else if (node.else) {
        Object.assign(processedNode, node.else);
      }
    }

    // 处理依赖关系
    if (node.dependencies) {
      Object.entries(node.dependencies).forEach(([depField, depSchema]) => {
        const depValue = getNestedValue(data, depField);
        if (depValue !== undefined && depValue !== null && depValue !== '') {
          if (typeof depSchema === 'object') {
            Object.assign(processedNode, depSchema);
          }
        }
      });
    }

    // 处理条件必填
    if (node.requiredIf) {
      const condition = evaluateCondition(node.requiredIf, data, path);
      processedNode.required = condition;
    }

    // 处理条件禁用
    if (node.disabledIf) {
      const condition = evaluateCondition(node.disabledIf, data, path);
      processedNode.disabled = condition;
    }

    // 处理条件只读
    if (node.readonlyIf) {
      const condition = evaluateCondition(node.readonlyIf, data, path);
      processedNode.readonly = condition;
    }

    // 递归处理子节点
    if (processedNode.properties) {
      processedNode.properties = Object.fromEntries(
        Object.entries(processedNode.properties).map(([key, childNode]) => [
          key,
          processNode(childNode, data, path ? `${path}.${key}` : key)
        ])
      );
    }

    if (processedNode.fields) {
      processedNode.fields = processedNode.fields.map((field, index) =>
        processNode(field, data, `${path}.fields[${index}]`)
      );
    }

    if (processedNode.items) {
      processedNode.items = processNode(processedNode.items, data, `${path}.items`);
    }

    return processedNode;
  };

  return processNode(schema, formData);
};

/**
 * 评估条件表达式
 * @param {Object|Function} condition - 条件配置或函数
 * @param {Object} data - 表单数据
 * @param {string} path - 当前路径
 * @returns {boolean}
 */
export const evaluateCondition = (condition, data, path = '') => {
  if (typeof condition === 'function') {
    return condition(data, path);
  }

  if (typeof condition === 'object') {
    const { field, operator = 'equals', value, values } = condition;
    const fieldValue = getNestedValue(data, field);

    switch (operator) {
      case 'equals':
      case '==':
        return fieldValue === value;
      case 'not_equals':
      case '!=':
        return fieldValue !== value;
      case 'in':
        return Array.isArray(values) && values.includes(fieldValue);
      case 'not_in':
        return Array.isArray(values) && !values.includes(fieldValue);
      case 'greater_than':
      case '>':
        return fieldValue > value;
      case 'greater_than_or_equal':
      case '>=':
        return fieldValue >= value;
      case 'less_than':
      case '<':
        return fieldValue < value;
      case 'less_than_or_equal':
      case '<=':
        return fieldValue <= value;
      case 'contains':
        return typeof fieldValue === 'string' && fieldValue.includes(value);
      case 'starts_with':
        return typeof fieldValue === 'string' && fieldValue.startsWith(value);
      case 'ends_with':
        return typeof fieldValue === 'string' && fieldValue.endsWith(value);
      case 'is_empty':
        return !fieldValue || fieldValue === '' || (Array.isArray(fieldValue) && fieldValue.length === 0);
      case 'is_not_empty':
        return fieldValue && fieldValue !== '' && (!Array.isArray(fieldValue) || fieldValue.length > 0);
      case 'regex':
        return typeof fieldValue === 'string' && new RegExp(value).test(fieldValue);
      default:
        console.warn(`Unknown condition operator: ${operator}`);
        return false;
    }
  }

  return Boolean(condition);
};

/**
 * 获取嵌套对象的值
 * @param {Object} obj - 对象
 * @param {string} path - 路径（支持点号和数组索引）
 * @returns {any}
 */
export const getNestedValue = (obj, path) => {
  if (!obj || !path) return undefined;
  
  return path.split('.').reduce((current, key) => {
    if (current === null || current === undefined) return undefined;
    
    // 处理数组索引 [0], [1] 等
    if (key.includes('[') && key.includes(']')) {
      const [arrayKey, indexStr] = key.split('[');
      const index = parseInt(indexStr.replace(']', ''), 10);
      return current[arrayKey]?.[index];
    }
    
    return current[key];
  }, obj);
};

/**
 * 设置嵌套对象的值
 * @param {Object} obj - 对象
 * @param {string} path - 路径
 * @param {any} value - 值
 * @returns {Object} 新的对象
 */
export const setNestedValue = (obj, path, value) => {
  if (!path) return obj;
  
  const keys = path.split('.');
  const result = { ...obj };
  let current = result;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    
    // 处理数组索引
    if (key.includes('[') && key.includes(']')) {
      const [arrayKey, indexStr] = key.split('[');
      const index = parseInt(indexStr.replace(']', ''), 10);
      
      if (!current[arrayKey]) {
        current[arrayKey] = [];
      } else if (!Array.isArray(current[arrayKey])) {
        current[arrayKey] = [current[arrayKey]];
      }
      
      if (!current[arrayKey][index]) {
        current[arrayKey][index] = {};
      }
      
      current = current[arrayKey][index];
    } else {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
  }
  
  const lastKey = keys[keys.length - 1];
  
  // 处理最后一个键的数组索引
  if (lastKey.includes('[') && lastKey.includes(']')) {
    const [arrayKey, indexStr] = lastKey.split('[');
    const index = parseInt(indexStr.replace(']', ''), 10);
    
    if (!current[arrayKey]) {
      current[arrayKey] = [];
    }
    
    current[arrayKey][index] = value;
  } else {
    current[lastKey] = value;
  }
  
  return result;
};

/**
 * 深度合并对象
 * @param {Object} target - 目标对象
 * @param {Object} source - 源对象
 * @returns {Object}
 */
export const deepMerge = (target, source) => {
  if (!source) return target;
  if (!target) return source;
  
  const result = { ...target };
  
  Object.keys(source).forEach(key => {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(result[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  });
  
  return result;
};

/**
 * 从Schema生成默认值
 * @param {Object} schema - Schema配置
 * @returns {Object}
 */
export const generateDefaultValues = (schema) => {
  if (!schema || typeof schema !== 'object') return {};
  
  const generateValue = (node) => {
    if (!node || typeof node !== 'object') return undefined;
    
    // 如果有默认值，直接返回
    if (node.default !== undefined) {
      return node.default;
    }
    
    // 根据类型生成默认值
    switch (node.type) {
      case 'string':
      case 'email':
      case 'password':
      case 'url':
      case 'tel':
        return '';
      case 'number':
      case 'integer':
        return node.minimum || 0;
      case 'boolean':
        return false;
      case 'array':
        return [];
      case 'object':
        if (node.properties) {
          const obj = {};
          Object.entries(node.properties).forEach(([key, childSchema]) => {
            const childValue = generateValue(childSchema);
            if (childValue !== undefined) {
              obj[key] = childValue;
            }
          });
          return obj;
        }
        return {};
      default:
        return undefined;
    }
  };
  
  if (schema.properties) {
    const defaults = {};
    Object.entries(schema.properties).forEach(([key, fieldSchema]) => {
      const value = generateValue(fieldSchema);
      if (value !== undefined) {
        defaults[key] = value;
      }
    });
    return defaults;
  }
  
  return generateValue(schema);
};
