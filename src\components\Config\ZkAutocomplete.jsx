import React, { useEffect, useState } from "react";
import {
  Input<PERSON>abel,
  Stack,
  Autocomplete,
  TextField,
  FormHelperText,
} from "@mui/material";
import RequirePoint from "../RequirePoint";
import { useTranslation } from "react-i18next";
import { pxToRem } from "@/utils/zkUtils.js";
function ZkAutocomplete({
  formik = null,
  placeholder = "",
  label = "",
  name = "",
  error = null,
  disabled = false,
  options = [],
  typevalue = "0",
  labelpostion = "column",
  spacing = 1,
  width = "100%",
  fontSize = "18px",
  readonly = false,
  required = false,
  ...otherProps
}) {
  const [data, setData] = useState(null);
  const { t } = useTranslation();

  // Define a mapping for typevalue to avoid repetitive if-else conditions
  const getOptionValue = (option) => {
    const mapping = {
      1: option,
      2: option?.id,
      3: option?.value,
      4: option?.label,
      5: option?.name,
    };
    return mapping[typevalue] || option?.value;
  };

  const handleChange = (event, newValue) => {
    setData(newValue);

    formik?.setFieldValue(name, newValue?.id);
  };

  useEffect(() => {
    let data = options.find((item) => item.id == formik.values[name]);
    setData(data);
    formik?.setFieldValue(name, data?.id);
  }, [formik.values]);

  return (
    <Stack spacing={1} sx={{ width: "100%" }}>
      {label && (
        <Stack
          direction={labelpostion === "left" ? "row" : "column"}
          alignItems={labelpostion === "left" ? "flex-start" : ""}>
          <InputLabel
            htmlFor={`CmpAutoComPlete_${name}`}
            shrink
            sx={{
              fontSize: "18px",
              color: "#474b4fcc",
            }}>
            {label} {required && <RequirePoint />}
          </InputLabel>
        </Stack>
      )}
      <Stack
        sx={{
          flexGrow: 1,
          width: "100%",
          borderRadius: "15px",
        }}>
        <Autocomplete
          id={`CmpAutoComPlete_${name}`}
          disablePortal
          fullWidth
          options={options || []}
          value={data || null}
          label={""}
          onChange={handleChange}
          disabled={disabled}
          isOptionEqualToValue={(option, value) => {
            return getOptionValue(option) === getOptionValue(value);
          }}
          getOptionLabel={(option) => getOptionValue(option) || ""}
          noOptionsText={t("No Options")}
          renderInput={(params) => (
            <TextField
              {...params}
              readOnly={readonly}
              placeholder={placeholder}
              sx={{
                "& .MuiOutlinedInput-input": {
                  height: "35px",
                  borderRadius: "25px",
                  fontSize: "14px",
                  marginTop: "-5px",
                },

                "& .MuiInputBase-root ": {
                  height: pxToRem(50),
                  marginTop: "-5px",
                },
              }}
            />
          )}
          {...otherProps}
        />

        {((formik && formik.touched[name] && formik.errors[name]) || error) && (
          <FormHelperText error id={`standard-weight-helper-text-${name}`}>
            {(formik && formik.errors[name]) || error}
          </FormHelperText>
        )}
      </Stack>
    </Stack>
  );
}

export default ZkAutocomplete;
