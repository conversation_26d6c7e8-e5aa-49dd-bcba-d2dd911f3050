import React, { useEffect, useState } from "react";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import { useTranslation } from "react-i18next";
import { getDeviceNumberConfig } from "../js/Config";
import { useLocation } from "react-router-dom";
import CustomCard from "./CustomCard";
import { handlerTime } from "../js/ulits";
import { getAllPackage } from "@s/api/subscription.js";

function Subscription(props) {
  const { formik, deviceConfig, setDeviceConfig } = props;

  const { t } = useTranslation();
  const { state } = useLocation();

  const [packageList, setPackageList] = useState(
    state?.type === "add" ? JSON.parse(sessionStorage.getItem("SUB_TYPE")) : []
  );

  useEffect(() => {
    if (state?.type === "add") {
      return;
    }
    getAllPackage({ applicationCode: sessionStorage.getItem("SUB_APP") }).then(
      (res) => {
        setPackageList(res?.data);
      }
    );
  }, []);

  useEffect(() => {
    let type = formik.values["unit"];
    let configInfo = getDeviceNumberConfig(t, state, type, packageList);
    setDeviceConfig(configInfo);
  }, [formik.values["unit"], packageList]);

  useEffect(() => {
    if (formik.values["deviceCount"]) {
      handlerTime(state, formik);
    } else {
      formik.setFieldValue("expiraTime", "");
    }
  }, [
    formik.values["unit"],
    formik.values["deviceCount"],
    formik.values["activateTime"],
  ]);

  return (
    <CustomCard title={t("subscription.subsctiption_content")}>
      <ZkFormik sx={6} formik={formik} formConfig={deviceConfig}></ZkFormik>
    </CustomCard>
  );
}

export default Subscription;
