import React from "react";
import RequirePoint from "@c/RequirePoint";
import LoadingButton from "@mui/lab/LoadingButton";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import { sendVerifyCode } from "@/service/api/user";

function VerifyCode(props) {
  const { t } = useTranslation();

  const { item, formik } = props;

  const { name, labelpostion } = item;

  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prevCount) => prevCount - 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  // 获取验证码
  const handleSendCode = async (values) => {
    setCountdown(60);
    const res = await sendVerifyCode({ email: values.email });
    toast.success(res?.message);
  };

  return (
    <React.Fragment>
      <Grid container xs={12} ml={3} mt={1}>
        <Stack
          direction={labelpostion === "left" ? "row" : "column"}
          sx={{
            alignItems: labelpostion === "left" ? "flex-start" : "",
          }}
          spacing={1}>
          <InputLabel
            style={{
              marginTop: labelpostion === "left" ? "12px" : "",
              color: "#474b4fcc",
              fontSize: "14px",
            }}
            htmlFor={"zkInput_" + name}>
            {t("system.base_system_verification_code")}
            {<RequirePoint></RequirePoint>}
          </InputLabel>

          <Stack
            sx={{
              flexGrow: 1,
              width: "100%",
            }}
            xs={12}>
            <OutlinedInput
              fullWidth
              id="quick-code-forget"
              type="text"
              name={name}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              placeholder={t("common.common_form_code_placeholder")}
              sx={{
                "&.MuiOutlinedInput-root": {
                  width: "462px",
                },
              }}
              endAdornment={
                <InputAdornment position="end">
                  <LoadingButton
                    disabled={countdown > 0}
                    disableElevation
                    variant="text"
                    color="primary"
                    onClick={() => {
                      const email = formik.values.email;
                      if (!email) {
                        toast.error(t("common.common_input_email"));
                        return;
                      }
                      handleSendCode(formik.values);
                    }}>
                    {countdown === 0
                      ? t("common.common_form_get_code")
                      : `${countdown}${t(
                          "common.common_form_get_code_surplus"
                        )}`}
                  </LoadingButton>
                </InputAdornment>
              }
              error={Boolean(formik?.touched[name] && formik?.errors[name])}
            />

            {formik?.touched[name] && formik?.errors[name] && (
              <FormHelperText error id={`standard-weight-helper-text-${name}`}>
                {formik?.errors[name]}
              </FormHelperText>
            )}
          </Stack>
        </Stack>
      </Grid>
    </React.Fragment>
  );
}

export default VerifyCode;
