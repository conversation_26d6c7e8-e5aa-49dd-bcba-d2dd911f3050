import request from "@u/request";
const baseProfixURI = `${import.meta.env.VITE_APICODE}`;
/**
 *
 * @param {·根据类型获取数据权限·} type
 * @returns
 */
export const selectDataPermission = (params) => {
  return request({
    url: `${baseProfixURI}/auth/datascope/query/list`,
    method: "GET",
    params: params,
  });
};

export const addDataPermission = (payload) => {
  return request({
    url: `${baseProfixURI}/auth/datascope`,
    method: "POST",
    data: payload,
  });
};

export const editDataPermission = (payload) => {
  return request({
    url: `${baseProfixURI}/auth/datascope`,
    method: "PUT",
    data: payload,
  });
};

/**
 * 根据类型查询数据权限树
 */

export const getDataPermissionTree = (params) => {
  return request({
    url: `${baseProfixURI}/auth/datascope/query/tree`,
    method: "GET",
    params: params,
  });
};

/**
 * 通过ID 删除数据权限
 */

export const deletePermission = (ids) => {
  return request({
    url: `${baseProfixURI}/auth/datascope/${ids}`,
    method: "DELETE",
  });
};

/**
 * 根据 ID 查询数据权限
 */

export const getDataDetail = (id) => {
  return request({
    url: `${baseProfixURI}/auth/datascope/query/${id}`,
    method: "GET",
  });
};



/**
 * 
 *  查询零售商下面的门店
 * 
 */

export const getStoreList = (params) => {
  return request({
    url: `${baseProfixURI}/outlet/principal_and_outlet?departmentIds=${params}`,
    method: "GET",
  })
}

/**
 * 分页查询数据权限
 */

export const getDataPermissionList = (params) => {
  return request({
    url: `${baseProfixURI}/auth/datascope/query/page`,
    method: "GET",
    params: params,
  })
}