import request from "@/utils/request";

/**
 *  查询操作日志列表
 */
export const getMessagelogList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/message/records/query/page`,
    method: "get",
    params: params,
  });
};

/**
 *  查询操作日志详情
 */
export const getMessagelogDetail = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/message/records/query/${id}`,
    method: "get",
  });
};
