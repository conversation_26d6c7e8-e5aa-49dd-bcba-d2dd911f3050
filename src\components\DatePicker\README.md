# DatePicker - Ant Design API 兼容组件

基于 Material-UI 构建的 DatePicker 组件，完全兼容 Ant Design DatePicker 的 API 接口。

## 特性

✅ **完全兼容 Ant Design API** - 支持所有 Ant Design DatePicker 的属性和方法  
✅ **Material-UI 样式** - 使用 Material-UI 组件和主题系统  
✅ **TypeScript 支持** - 完整的类型定义  
✅ **多种选择模式** - 日期、时间、日期时间、范围、月份、年份选择
✅ **预设快捷选项** - 支持自定义预设日期  
✅ **国际化支持** - 内置中文支持，可扩展其他语言  
✅ **响应式设计** - 适配不同屏幕尺寸  

## 安装

```bash
npm install @mui/material @mui/x-date-pickers dayjs
```

> **注意**: 此组件仅使用 MUI X 的免费版本 `@mui/x-date-pickers`，不需要付费的 `@mui/x-date-pickers-pro`。

## 基本用法

```jsx
import DatePicker from '@/components/AntdDatePicker';
import dayjs from 'dayjs';

function App() {
  const [value, setValue] = useState();
  
  return (
    <DatePicker
      value={value}
      onChange={setValue}
      placeholder="请选择日期"
      allowClear
      showTime
    />
  );
}
```

## API 参考

### DatePicker Props

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| `value` | 日期 | `dayjs \| null` | - |
| `defaultValue` | 默认日期 | `dayjs \| null` | - |
| `onChange` | 时间发生变化的回调 | `function(date, dateString)` | - |
| `placeholder` | 输入框提示文字 | `string` | "请选择日期" |
| `size` | 输入框大小 | `large \| middle \| small` | `middle` |
| `disabled` | 禁用 | `boolean` | `false` |
| `allowClear` | 是否显示清除按钮 | `boolean` | `true` |
| `autoFocus` | 自动获取焦点 | `boolean` | `false` |
| `format` | 展示的日期格式 | `string` | `"YYYY-MM-DD"` |
| `open` | 控制弹层是否展开 | `boolean` | - |
| `showTime` | 增加时间选择功能 | `boolean \| object` | `false` |
| `picker` | 设置选择器类型 | `date \| week \| month \| quarter \| year` | `date` |
| `presets` | 预设时间范围快捷选择 | `Array<{label, value}>` | - |
| `disabledDate` | 不可选择的日期 | `function(currentDate)` | - |
| `needConfirm` | 是否需要确认按钮 | `boolean` | `false` |

### RangePicker Props

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| `value` | 日期范围 | `[dayjs, dayjs] \| null` | - |
| `defaultValue` | 默认日期范围 | `[dayjs, dayjs] \| null` | - |
| `onChange` | 时间发生变化的回调 | `function(dates, dateStrings)` | - |
| `onCalendarChange` | 待选日期发生变化的回调 | `function(dates, dateStrings, info)` | - |
| `placeholder` | 输入框提示文字 | `[string, string]` | `["开始日期", "结束日期"]` |
| `separator` | 设置分隔符 | `string` | `"~"` |
| `allowEmpty` | 允许起始项部分为空 | `[boolean, boolean]` | `[false, false]` |

### TimePicker Props

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| `value` | 时间 | `dayjs \| null` | - |
| `defaultValue` | 默认时间 | `dayjs \| null` | - |
| `onChange` | 时间发生变化的回调 | `function(time, timeString)` | - |
| `placeholder` | 输入框提示文字 | `string` | "请选择时间" |
| `format` | 展示的时间格式 | `string` | `"HH:mm:ss"` |
| `hourStep` | 小时选项间隔 | `number` | `1` |
| `minuteStep` | 分钟选项间隔 | `number` | `1` |
| `secondStep` | 秒选项间隔 | `number` | `1` |
| `use12Hours` | 使用 12 小时制 | `boolean` | `false` |
| `disabledHours` | 禁止选择部分小时选项 | `function()` | - |
| `disabledMinutes` | 禁止选择部分分钟选项 | `function(selectedHour)` | - |
| `disabledSeconds` | 禁止选择部分秒选项 | `function(selectedHour, selectedMinute)` | - |

## 使用示例

### 基础日期选择

```jsx
import DatePicker from '@/components/AntdDatePicker';

function BasicExample() {
  const [value, setValue] = useState();
  
  return (
    <DatePicker
      value={value}
      onChange={setValue}
      placeholder="请选择日期"
      format="YYYY-MM-DD"
    />
  );
}
```

### 日期时间选择

```jsx
<DatePicker
  value={value}
  onChange={setValue}
  showTime
  format="YYYY-MM-DD HH:mm:ss"
  placeholder="请选择日期时间"
/>
```

### 日期范围选择

```jsx
<DatePicker.RangePicker
  value={rangeValue}
  onChange={setRangeValue}
  placeholder={["开始日期", "结束日期"]}
/>
```

### 月份选择

```jsx
<DatePicker
  value={monthValue}
  onChange={setMonthValue}
  picker="month"
  placeholder="请选择月份"
  format="YYYY-MM"
/>
```

### 年份选择

```jsx
<DatePicker
  value={yearValue}
  onChange={setYearValue}
  picker="year"
  placeholder="请选择年份"
  format="YYYY"
/>
```

### 纯时间选择

```jsx
<DatePicker.TimePicker
  value={timeValue}
  onChange={setTimeValue}
  placeholder="请选择时间"
  format="HH:mm:ss"
/>
```

### 12小时制时间选择

```jsx
<DatePicker.TimePicker
  value={timeValue}
  onChange={setTimeValue}
  use12Hours
  format="h:mm:ss a"
  placeholder="请选择时间"
/>
```

### 预设快捷选择

```jsx
const presets = [
  {
    label: '今天',
    value: () => dayjs(),
  },
  {
    label: '昨天',
    value: () => dayjs().subtract(1, 'day'),
  },
  {
    label: '一周前',
    value: () => dayjs().subtract(1, 'week'),
  },
];

<DatePicker
  value={value}
  onChange={setValue}
  presets={presets}
/>
```

### 范围预设

```jsx
const rangePresets = [
  {
    label: '最近7天',
    value: () => [dayjs().subtract(7, 'day'), dayjs()],
  },
  {
    label: '最近30天',
    value: () => [dayjs().subtract(30, 'day'), dayjs()],
  },
  {
    label: '本月',
    value: () => [dayjs().startOf('month'), dayjs().endOf('month')],
  },
];

<DatePicker.RangePicker
  value={rangeValue}
  onChange={setRangeValue}
  presets={rangePresets}
/>
```

### 禁用日期

```jsx
// 禁用今天之前的日期
<DatePicker
  value={value}
  onChange={setValue}
  disabledDate={(current) => current && current < dayjs().startOf('day')}
/>

// 禁用今天之后的日期
<DatePicker
  value={value}
  onChange={setValue}
  disabledDate={(current) => current && current > dayjs().endOf('day')}
/>
```

### 自定义格式

```jsx
<DatePicker
  value={value}
  onChange={setValue}
  format="DD/MM/YYYY"
  placeholder="请选择日期"
/>
```

### 受控模式

```jsx
function ControlledExample() {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState();
  
  return (
    <DatePicker
      open={open}
      value={value}
      onChange={setValue}
      onOpenChange={setOpen}
      placeholder="受控模式"
    />
  );
}
```

### 状态控制

```jsx
<DatePicker
  value={value}
  onChange={setValue}
  status="error"  // error | warning
  disabled={false}
  size="large"    // large | middle | small
/>
```

## 事件回调

```jsx
<DatePicker
  onChange={(date, dateString) => {
    console.log('日期变化:', date, dateString);
  }}
  onOk={() => {
    console.log('确认选择');
  }}
  onOpenChange={(open) => {
    console.log('面板状态:', open);
  }}
  onPanelChange={(value, mode) => {
    console.log('面板变化:', value, mode);
  }}
/>
```

## 样式定制

```jsx
<DatePicker
  style={{ width: '100%' }}
  popupStyle={{ zIndex: 1000 }}
  className="custom-date-picker"
  size="large"
  variant="filled"  // outlined | filled | standard
/>
```

## 国际化

```jsx
import 'dayjs/locale/zh-cn';
import dayjs from 'dayjs';

// 设置全局语言
dayjs.locale('zh-cn');

<DatePicker
  value={value}
  onChange={setValue}
  locale="zh-cn"
/>
```

## 与 Ant Design 的差异

### 视觉差异
1. **Material Design 风格**: 使用 Material-UI 的设计语言
2. **圆角和阴影**: 更柔和的视觉效果
3. **颜色系统**: 集成 Material-UI 主题色彩
4. **动画效果**: 使用 Material-UI 的过渡动画

### 功能增强
1. **主题集成**: 完全集成 Material-UI 主题系统
2. **响应式**: 更好的移动端适配
3. **无障碍**: 更好的可访问性支持

### API 兼容性
- ✅ 100% 兼容 Ant Design DatePicker API
- ✅ 支持所有原有属性和方法
- ✅ 支持所有事件回调
- ✅ 支持所有日期格式

### 免费版本实现
- ✅ **完全免费**: 仅使用 MUI X 免费版本，无需付费许可证
- ✅ **RangePicker**: 使用双日历实现范围选择，功能完整
- ✅ **范围样式**: 自定义样式实现开始、结束、中间日期的视觉效果
- ✅ **性能优化**: 基于免费组件的高效实现

## 迁移指南

从 Ant Design DatePicker 迁移到此组件：

```jsx
// 之前 (Ant Design)
import { DatePicker } from 'antd';

// 现在 (Material-UI 风格)
import DatePicker from '@/components/AntdDatePicker';

// API 完全相同，无需修改代码
<DatePicker
  value={value}
  onChange={onChange}
  placeholder="请选择日期"
  showTime
  allowClear
/>
```

## 最佳实践

1. **日期格式**: 使用 dayjs 进行日期处理
2. **性能优化**: 使用 useMemo 缓存复杂计算
3. **用户体验**: 提供清晰的预设选项
4. **无障碍**: 确保键盘导航和屏幕阅读器支持

## 相关链接

- [Material-UI 官方文档](https://mui.com/)
- [Ant Design DatePicker API](https://ant.design/components/date-picker/)
- [dayjs 文档](https://dayjs.gitee.io/)
