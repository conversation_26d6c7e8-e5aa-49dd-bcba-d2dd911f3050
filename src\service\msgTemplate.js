import request from "@/utils/request";

/**
 *  查询消息模板列表
 */
export const getMsgTemplateList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/message/template/query/page`,
    method: "get",
    params: params,
  });
};

/**
 *  新增消息模板
 */
export const addMsgTemplate = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/message/template`,
    method: "post",
    data,
  });
};

/**
 *  修改消息模板
 */

export const changeMsgTemplate = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/message/template`,
    method: "put",
    data,
  });
};

/**
 *  修改消息模板
 */

export const deteleMsgTemplate = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/message/template/${id}`,
    method: "detele",
  });
};

/**
 *  获取消息模板详情
 */

export const getMsgTemplateDetail = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/message/template/query/${id}`,
    method: "detele",
  });
};
