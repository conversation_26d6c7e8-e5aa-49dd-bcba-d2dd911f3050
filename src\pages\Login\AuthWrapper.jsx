import PropTypes from "prop-types";
import * as React from "react";
// material-ui
import { Box, Grid, Typography } from "@mui/material";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
// utils
import { setStoreLang } from "@/utils/langUtils";

// project import
import AuthCard from "./AuthCard";

// lang
import { useTranslation } from "react-i18next";
// css
import "./auth.less";
import loginBg from "@/assets/Images/BroundImage/GlobalBg.png";

import { getStoreLang } from "@/utils/langUtils";
import config from "@/config/config";
import Stack from "@mui/material/Stack";

const AuthWrapper = ({ children, width }) => {
  const { i18n } = useTranslation();
  const [defaultLang, setDefaultLang] = React.useState(
    getStoreLang() ? getStoreLang() : config.i18n
  );
  // 语言切换
  const switchLanguage = (event) => {
    setDefaultLang(event.target.value);
    i18n.changeLanguage(event.target.value);
    // 当前语言保存到浏览器
    setStoreLang(event.target.value);
    // 刷新
    location.reload();
  };
  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: `url(${loginBg})`,
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        backgroundAttachment: "fixed",
      }}>
      <Grid
        container
        direction="column"
        justifyContent="flex-center"
        sx={{
          minHeight: "100vh",
        }}>
        <Grid item xs={12}>
          <div className="header">
            <div className="lang">
              <Select
                sx={{
                  color: "DimGray",
                  "	.MuiSelect-icon": { color: "DimGray" },
                  "&.hover": { color: "white" },
                }}
                labelId="demo-select-small"
                id="demo-select-small"
                label="语言"
                onChange={switchLanguage}
                value={defaultLang}
                // IconComponent={}
              >
                <MenuItem value="zh">
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={1}>
                    {/* <img
                      loading="lazy"
                      width="20"
                      src={`https://flagcdn.com/w20/cn.png`}
                      srcSet={`https://flagcdn.com/w40/cn.png 2x`}
                      alt=""
                    /> */}
                    <Typography>中文(简体)</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem value="en">
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={1}>
                    {/* <img
                      loading="lazy"
                      width="20"
                      src={`https://flagcdn.com/w20/us.png`}
                      srcSet={`https://flagcdn.com/w40/us.png 2x`}
                      alt=""
                    /> */}
                    <Typography>English</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem value="es">
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={1}>
                    {/* <img
                      loading="lazy"
                      width="20"
                      src={`https://flagcdn.com/w20/es.png`}
                      srcSet={`https://flagcdn.com/w40/es.png 2x`}
                      alt=""
                    /> */}
                    <Typography>Español</Typography>
                  </Stack>
                </MenuItem>

                <MenuItem value="jp">
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={1}>
                    {/* <img
                      loading="lazy"
                      width="20"
                      src={`https://flagcdn.com/w20/es.png`}
                      srcSet={`https://flagcdn.com/w40/es.png 2x`}
                      alt=""
                    /> */}
                    <Typography>日本語</Typography>
                  </Stack>
                </MenuItem>

                <MenuItem value="pt">
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    alignItems="center"
                    spacing={1}>
                    {/* <img
                      loading="lazy"
                      width="20"
                      src={`https://flagcdn.com/w20/es.png`}
                      srcSet={`https://flagcdn.com/w40/es.png 2x`}
                      alt=""
                    /> */}
                    <Typography>Português</Typography>
                  </Stack>
                </MenuItem>
              </Select>
            </div>
          </div>
        </Grid>

        <Grid item xs={12}>
          <Grid container direction="column" justifyContent="flex-center">
            <Grid
              item
              xs={12}
              container
              justifyContent="center"
              alignItems="center"
              sx={{
                minHeight: {
                  xs: "calc(100vh - 184px)",
                  md: "calc(100vh - 152px)",
                },
              }}>
              <Grid
                item
                sx={{
                  marginRight: {
                    xs: "0px",
                    md: "100px",
                    sm: "100px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    flexDirection: "column",
                  },
                }}>
                <AuthCard width={width}>{children}</AuthCard>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

AuthWrapper.propTypes = {
  children: PropTypes.node,
};

export default AuthWrapper;
