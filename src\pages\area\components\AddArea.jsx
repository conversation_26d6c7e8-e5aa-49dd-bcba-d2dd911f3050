/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { forwardRef, useRef } from "react";
import { pxToRem } from "@u/zkUtils";
import AnimateButton from "@/components/@extended/AnimateButton";
import AuthButton from "@/components/AuthButton";
import {
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import Treeselect from "@/components/zktreeselect";
import { useStateUserInfo } from "@/hooks/user";
import SelectMapBaidu from "@/components/baiduMap/selectMap"; //百度地图
import SelectMapGoogle from "@/components/googleMap/selectMapGoogle"; //谷歌地图
import { getTreeSelect, save } from "@/service/api/area";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  FormHelperText,
  Grid,
  InputAdornment,
  InputBase,
  InputLabel,
  OutlinedInput,
  Stack,
  Typography,
} from "@mui/material";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import * as Yup from "yup";

const AddArea = forwardRef((props, ref) => {
  const selectMapRef = useRef(null);
  const userInfor = useStateUserInfo();
  // 国际化
  const { t } = useTranslation();
  // 添加分组弹窗
  const [open, setOpen] = React.useState(false);
  //保存按钮
  const [loading, setLoading] = React.useState(false);

  //下拉树数据
  const [treeData, setTreeData] = React.useState([]);
  const [parentIdError, setParentIdError] = React.useState();
  const treeSelectRef = React.useRef(null);

  // 添加表单
  const areaFormik = useFormik({
    initialValues: {
      name: "",
      lng: "",
      lat: "",
      parentId: "",
      sort: 0,
      status: "0",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handleSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },

    validationSchema: Yup.object().shape({
      name: Yup.string()
        .max(60, t("common.common_rule_area_len60"))
        .matches(/^[a-zA-Z\u4e00-\u9fa5]+[a-zA-Z\d\u4e00-\u9fa5_\s]*$/, {
          message: t("common.common_area_name_regTip"),
          excludeEmptyString: true,
        })
        .required(t("common_area_name_not_null")),
      // parentId: Yup.string().test({
      //   name: "parentId",
      //   test: (value, ctx) => {
      //     if (value) {
      //       setParentIdError(undefined);
      //       return true;
      //     }
      //     setParentIdError(t("area.ips_enter_region"));
      //     return ctx.createError({
      //       message: t("area.ips_enter_region"),
      //     });
      //   },
      // }),
      lng: Yup.string().required(
        t("common.common_please_area_center_location")
      ),
      lat: Yup.string().required(
        t("common.common_please_area_center_location")
      ),
      sort: Yup.number()
        .required(t("common_input_sort_null"))
        .min(0, t("common_connot_lower_zero")),
    }),
  });

  React.useImperativeHandle(ref, () => ({
    handleClose,
    handleOpen,
  }));
  const handleClose = () => {
    areaFormik.setFieldValue("name", null);
    areaFormik.setFieldValue("parentId", null);
    areaFormik.setFieldValue("sort", 0);
    areaFormik.setFieldValue("status", "0");
    treeSelectRef.current.handleClose();
    setOpen(false);
  };
  const handleOpen = () => {
    areaFormik.handleReset();
    setParentIdError(undefined);
    setOpen(true);
    getTreeData();
  };

  const handleSubmit = async (values) => {
    //处理经纬度，使用逗号连接
    const requestValues = { ...values };
    requestValues.location = `${values.lng},${values.lat}`;
    setLoading(true);
    await save(requestValues)
      .then((res) => {
        props.callback();
        handleClose();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getTreeData = () => {
    getTreeSelect().then((res) => {
      if (res.data) {
        setTreeData(res.data);
      }
    });
  };

  // 点图选点回调事件
  const getLocationGoogle = (lat, lng, location) => {
    areaFormik.setFieldValue("lat", lat);
    areaFormik.setFieldValue("lng", lng);
  };

  const getLocation = (value, location) => {
    areaFormik.setFieldValue("lng", value.lng);
    areaFormik.setFieldValue("lat", value.lat);
  };

  // 切换地图
  const handleSwitchMap = () => {
    const lang = localStorage.getItem("zkBioCloudMediaLang");

    if (lang == "en") {
      return (
        <SelectMapGoogle ref={selectMapRef} getLocation={getLocationGoogle} />
      );
    } else {
      return <SelectMapBaidu ref={selectMapRef} getLocation={getLocation} />;
    }
  };

  return (
    <>
      <BootstrapDialog
        open={open}
        onClose={() => setOpen(false)}
        aria-describedby="alert-dialog-slide-description">
        <BootstrapDialogTitle onClose={() => setOpen(false)}>
          <Typography variant="h5" component="p">
            {t("area.new_area")}
          </Typography>
          {/* <IconButton
            aria-label="close"
            onClick={() => setOpen(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}>
            <CloseIcon />
          </IconButton> */}
        </BootstrapDialogTitle>

        <BootstrapContent
          sx={{
            minWidth: "600px",

            background: "#FFFFFF 0% 0% no-repeat padding-box",
            boxShadow: "0px 2px 6px #00000029",
            borderRadius: "10px",
          }}>
          <form noValidate onSubmit={areaFormik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel
                    htmlFor="area-name"
                    sx={{
                      overflow: "initial",
                      width: "30%",
                      fontSize: "14px",
                    }}>
                    {t("area.area_area_name")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    id="area-name"
                    type="text"
                    name="name"
                    value={areaFormik.values.name}
                    onBlur={areaFormik.handleBlur}
                    onChange={areaFormik.handleChange}
                    placeholder={t("area.enter_region_name")}
                    fullWidth
                    error={Boolean(
                      areaFormik.touched.name && areaFormik.errors.name
                    )}
                    sx={{
                      width: "100%",
                      "& .MuiOutlinedInput-input": {
                        height: pxToRem(35),
                        fontSize: "14px",
                      },
                      borderRadius: "7px",
                    }}
                  />
                  {areaFormik.touched.name && areaFormik.errors.name && (
                    <FormHelperText
                      error
                      id="standard-weight-helper-text-area-name">
                      {areaFormik.errors.name}
                    </FormHelperText>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel
                    htmlFor="area-parentId"
                    sx={{
                      overflow: "initial",
                      width: "30%",
                      fontSize: "14px",
                    }}>
                    {t("area.superior_area")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <Treeselect
                    ref={treeSelectRef}
                    data={treeData}
                    optionValue="id"
                    optionLabel="name"
                    placeholder={t("area.select_an_area")}
                    onChange={(valuas) => {
                      const parentId = valuas.id;
                      areaFormik.setFieldValue("parentId", parentId);
                    }}
                    onClear={() => {
                      areaFormik.setFieldValue("parentId", undefined);
                      setParentIdError(undefined);
                    }}
                    error={Boolean(parentIdError)}
                    sx={{
                      width: "100%",
                      fontSize: "14px",
                      "& .MuiOutlinedInput-input": {
                        height: pxToRem(35),
                      },
                      borderRadius: "7px",
                    }}
                    disableParent={true}
                  />
                  {parentIdError && (
                    <FormHelperText
                      error
                      id="standard-weight-helper-text-area-parentId">
                      {parentIdError}
                    </FormHelperText>
                  )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel
                    htmlFor="area-parentId"
                    sx={{
                      overflow: "initial",
                      width: "30%",
                      fontSize: "14px",
                    }}>
                    {t("area.region_center")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    id="location"
                    type="text"
                    sx={{
                      width: "100%",
                      cursor: "notAllowed",
                      "& .MuiOutlinedInput-input": {
                        height: pxToRem(35),
                        fontSize: "14px",
                      },
                      borderRadius: "7px",
                    }}
                    name="lng"
                    readOnly
                    onClick={() => selectMapRef.current.handleClickOpen()}
                    value={areaFormik.values.lng}
                    onBlur={areaFormik.handleBlur}
                    onChange={areaFormik.handleChange}
                    placeholder={t("common.common_Longitude")}
                    fullWidth
                    startAdornment={
                      <InputAdornment position="start">
                        <InputBase
                          readOnly
                          sx={{
                            width: "184px",
                            cursor: "notAllowed",
                            fontSize: "14px",
                          }}
                          onBlur={areaFormik.handleBlur}
                          onChange={areaFormik.handleChange}
                          value={areaFormik.values.lat}
                          placeholder={t("common.common_latitude")}
                          endAdornment={<div>-</div>}
                          error={Boolean(
                            areaFormik.touched.lat && areaFormik.errors.lat
                          )}
                        />
                      </InputAdornment>
                    }
                    error={Boolean(
                      areaFormik.touched.lng && areaFormik.errors.lng
                    )}
                  />
                  {areaFormik.touched.lng && areaFormik.errors.lng && (
                    <FormHelperText error id="lng-error">
                      {areaFormik.errors.lng}
                    </FormHelperText>
                  )}
                </Stack>
              </Grid>

              {/* <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel
                    control={<Checkbox />}
                    label={t("area.accelerate")}
                    sx={{
                      color: "#474B4F",
                      "& .MuiTypography-root": {
                        fontSize: "14px",
                      },
                    }}
                  />
                </FormGroup>
              </Grid> */}

              <Grid item xs={12}>
                <AuthButton button="">
                  <AnimateButton>
                    <LoadingButton
                      loading={loading}
                      disableElevation
                      disabled={areaFormik.isSubmitting}
                      fullWidth
                      type="submit"
                      style={{
                        width: "100%",
                        height: "48px",
                        borderRadius: "10px",
                        color: "#FFFFFF",
                        textAlign: "center",
                        font: `normal normal medium 14px/18px Proxima Nova`,
                        background:
                          "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                      }}>
                      {t("common.common_submit")}
                    </LoadingButton>
                  </AnimateButton>
                </AuthButton>
              </Grid>
              {areaFormik.errors.submit && (
                <Grid item xs={12}>
                  <FormHelperText error>
                    {areaFormik.errors.submit}
                  </FormHelperText>
                </Grid>
              )}
            </Grid>
          </form>
        </BootstrapContent>
      </BootstrapDialog>

      {/* 谷歌地图  注意回显方法 */}
      {handleSwitchMap()}
    </>
  );
});

export default AddArea;
