export const tablePaperSx = {
    borderRadius: "5px",
    "& .MuiTableCell-body": {
        justifyContent: "center",
        alignItems: "center",
        textAlign: "center",
    },
};

export const tableHeadRowSx = {
    backgroundColor: "#F0F0F0",
    boxShadow: "none",
    height: "60px",
    "& .Mui-TableHeadCell-Content": {
        justifyContent: "center",
        alignItems: "center",
        lineHeight: "34px",
        fontSize: "14px",
        whiteSpace: "nowrap",
        textOverflow: "ellipsis",
    },
};

export const tableBodyCellSx = {
    backgroundColor: "white",
    maxHeight: "200px",
    "& .MuiTableCell-body": {
        justifyContent: "center",
        alignItems: "center",
        textAlign: "center",
        fontSize: "14px",
        overflowX: "auto",
    },
};

export const dashedBorderSx = {
    borderRadius: "0",
    border: "1px dashed #e0e0e0",
};

export const tableBodyTextSx = {
    "& .MuiTableCell-body": {
        justifyContent: "center",
        alignItems: "center",
        textAlign: "center",
        fontSize: "14px",
    },
};
