import React from "react";
import EmptyIcon from "@/assets/Images/<EMAIL>"; // 替换为你的图标路径
import { Grid, Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
function EmptyFallBack() {
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <Grid
        container
        alignItems="center"
        justifyContent="center"
        sx={{ height: "300px" }} // 自定义高度
      >
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center">
          <img
            src={EmptyIcon}
            alt="No Data"
            style={{ width: "120px", marginBottom: "8px" }}
          />
          <Typography variant="body1" color="textSecondary">
            {t("common.common_no_content_found")}
          </Typography>
          <Typography variant="body1" color="textSecondary">
            {t("common.common_click_add_content")}
          </Typography>
        </Box>
      </Grid>
    </React.Fragment>
  );
}

export default EmptyFallBack;
