import ZkInput from "../components/ZKInput.jsx";
import { useTranslation } from "react-i18next";
const ZkSearch = (props) => {
  const { dataType, searchName, setSearchName, rightTableRef } = props;
  const { t } = useTranslation();

  const handleSearch = () => {
    if (rightTableRef.current) {
      rightTableRef.current.loadData(searchName);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      handleSearch();
    }
  };
  return (
    <Grid item xs={2} mb={2}>
      <ZkInput
        placeholder={
          dataType == "6"
            ? t("datascope.principals_selection_placeholder")
            : t("datascope.partner_selection_placeholder")
        }
        sx={{
          "& .MuiOutlinedInput-input": {
            fontSize: "14px",
          },
        }}
        name="name"
        value={searchName}
        handleChange={(e) => setSearchName(e.target.value)}
        onClick={handleSearch}
        onKeyPress={handleKeyPress}
        handlerClear={() => {
          setSearchName(null);
          rightTableRef.current.loadData();
        }}></ZkInput>
    </Grid>
  );
};

export default ZkSearch;
