import React, { useState, useCallback } from 'react';
import {
  TextField,
  InputAdornment,
  IconButton,
  FormHelperText,
  Box,
  Typography
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Clear
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

/**
 * Schema驱动的输入框组件
 */
const SchemaInput = ({
  name,
  path,
  schema,
  uiSchema = {},
  value = '',
  error,
  touched,
  disabled = false,
  readonly = false,
  required = false,
  onChange,
  onBlur,
  onValidate,
  formData,
  registry,
  ...props
}) => {
  const { t } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);

  // 从schema中提取配置
  const {
    title,
    description,
    placeholder,
    minLength,
    maxLength,
    pattern,
    format,
    multiline = false,
    rows = 4,
    type: schemaType = 'string'
  } = schema;

  // 从uiSchema中提取UI配置
  const {
    label = title,
    help = description,
    placeholder: uiPlaceholder = placeholder,
    clearable = true,
    showPasswordToggle = schemaType === 'password' || format === 'password',
    variant = 'outlined',
    size = 'medium',
    fullWidth = true,
    autoFocus = false,
    autoComplete,
    inputProps = {},
    ...uiProps
  } = uiSchema;

  // 确定输入类型
  const getInputType = useCallback(() => {
    if (schemaType === 'password' || format === 'password') {
      return showPassword ? 'text' : 'password';
    }
    
    switch (format) {
      case 'email':
        return 'email';
      case 'url':
        return 'url';
      case 'tel':
        return 'tel';
      default:
        switch (schemaType) {
          case 'number':
          case 'integer':
            return 'number';
          default:
            return 'text';
        }
    }
  }, [schemaType, format, showPassword]);

  // 处理值变化
  const handleChange = useCallback((event) => {
    let newValue = event.target.value;
    
    // 数字类型处理
    if (schemaType === 'number' || schemaType === 'integer') {
      if (newValue === '') {
        newValue = undefined;
      } else {
        newValue = schemaType === 'integer' ? parseInt(newValue, 10) : parseFloat(newValue);
        if (isNaN(newValue)) {
          return; // 忽略无效数字输入
        }
      }
    }
    
    onChange?.(newValue);
  }, [onChange, schemaType]);

  // 处理失焦
  const handleBlur = useCallback((event) => {
    onBlur?.(event);
    
    // 执行验证
    if (onValidate) {
      const errors = [];
      
      // 必填验证
      if (required && (!value || value === '')) {
        errors.push(t('validation.required', { field: label || name }));
      }
      
      // 长度验证
      if (value && typeof value === 'string') {
        if (minLength && value.length < minLength) {
          errors.push(t('validation.minLength', { field: label || name, min: minLength }));
        }
        if (maxLength && value.length > maxLength) {
          errors.push(t('validation.maxLength', { field: label || name, max: maxLength }));
        }
      }
      
      // 模式验证
      if (value && pattern) {
        const regex = new RegExp(pattern);
        if (!regex.test(value)) {
          errors.push(t('validation.pattern', { field: label || name }));
        }
      }
      
      onValidate(errors.length > 0 ? errors[0] : null);
    }
  }, [onBlur, onValidate, value, required, minLength, maxLength, pattern, label, name, t]);

  // 切换密码显示
  const handleTogglePassword = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  // 清空输入
  const handleClear = useCallback(() => {
    onChange?.('');
  }, [onChange]);

  // 构建输入属性
  const inputProps = {
    ...inputProps,
    autoComplete,
    ...(schemaType === 'number' || schemaType === 'integer' ? {
      step: schemaType === 'integer' ? 1 : 'any',
      min: schema.minimum,
      max: schema.maximum
    } : {})
  };

  // 构建结束装饰器
  const endAdornment = [];
  
  // 清空按钮
  if (clearable && value && !disabled && !readonly) {
    endAdornment.push(
      <IconButton
        key="clear"
        size="small"
        onClick={handleClear}
        edge="end"
      >
        <Clear fontSize="small" />
      </IconButton>
    );
  }
  
  // 密码切换按钮
  if (showPasswordToggle && !disabled && !readonly) {
    endAdornment.push(
      <IconButton
        key="password-toggle"
        size="small"
        onClick={handleTogglePassword}
        edge="end"
      >
        {showPassword ? <VisibilityOff /> : <Visibility />}
      </IconButton>
    );
  }

  return (
    <Box className="schema-form-field">
      <TextField
        name={name}
        label={label}
        placeholder={uiPlaceholder || t('common.pleaseEnter', { field: label || name })}
        value={value || ''}
        type={getInputType()}
        variant={variant}
        size={size}
        fullWidth={fullWidth}
        multiline={multiline}
        rows={multiline ? rows : undefined}
        required={required}
        disabled={disabled}
        InputProps={{
          readOnly: readonly,
          endAdornment: endAdornment.length > 0 ? (
            <InputAdornment position="end">
              {endAdornment}
            </InputAdornment>
          ) : null
        }}
        inputProps={inputProps}
        error={Boolean(error && touched)}
        helperText={
          (error && touched) ? error : (
            help ? (
              <Typography variant="caption" color="text.secondary">
                {help}
              </Typography>
            ) : null
          )
        }
        autoFocus={autoFocus}
        onChange={handleChange}
        onBlur={handleBlur}
        {...uiProps}
        {...props}
      />
    </Box>
  );
};

export default SchemaInput;
