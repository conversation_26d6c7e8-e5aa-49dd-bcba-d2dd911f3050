import React, { forwardRef, useState, useRef, useEffect } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";
import { timeZoneList } from "@/enums/TimeZone";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  Stack,
  Grid,
  Alert,
  AlertTitle,
  Button,
  InputLabel,
  Typography,
  FormHelperText,
  OutlinedInput,
  Tooltip,
} from "@mui/material";
import { toast } from "react-toastify";
import { useNavigate, useLocation } from "react-router-dom";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@c/dialog";
import ZKSelect from "@/components/ZKSelect";
import InfoIcon from "@mui/icons-material/Info";
import CloseIcon from "@/assets/Icons/CloseIcon.svg?react";
import AnimateButton from "@c/@extended/AnimateButton";
import { addDevice, editDevice } from "@s/api/device.js";
import { useTranslation } from "react-i18next";
import CustomAutocomplete from "@c/ZKSelect.jsx";
import { queryOutletList } from "@s/api/outlet";

function CMSDevice({
  category,
  preDeviceInfo,
  selectSence,
  open,
  setOpen,
  data,
  applicationCode,
}) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [loading, setIsLoading] = useState(false);
  const [outletList, setOutList] = useState([]);
  const [selectOutlet, setSelectOutlet] = useState("");
  const deviceTypeOptions = [
    { id: "0", value: t("LCD") },
    { id: "1", value: t("LED") },
  ];

  const directionOptions = [
    { id: "0", value: t("横向") },
    { id: "1", value: t("纵向") },
  ];
  useEffect(() => {
    if (open) {
      queryOutletList({
        // type: category,
        name: "",
        parentId: "",
        category: category,
      }).then((res) => {
        setOutList(res?.data);
      });
    }
  }, [open]);

  useEffect(() => {
    let data = outletList?.find((item) => {
      return item.id == selectOutlet;
    });

    let timezoon = timeZoneList?.find((item) => item.id == data?.timezone);

    screenFormik.setFieldValue("timezone", timezoon?.id);
  }, [selectOutlet, open]);

  const handelSaveSubmit = async (values) => {
    setIsLoading(true);

    let params = {
      ...values,
      dmsDeviceId: preDeviceInfo?.dmsDeviceId,
      departmentId: selectOutlet,
      mainOrSub: selectSence,
      sn: preDeviceInfo?.sn,
      // category: category,
      applicationCode: applicationCode,
    };
    try {
      if (state?.type == "add") {
        setIsLoading(true);
        await addDevice(params).then(({ message }) => {
          toast.success(message);
          navigate("/device/manage/list");
        });
      } else {
        setIsLoading(true);
        await editDevice(params)
          .then((res) => {
            toast.success(res?.message);
            setOpen(false);
          })
          .catch((err) => {
            throw err;
          });
      }
    } finally {
      setIsLoading(false);
      setOpen(false);
    }
  };

  //  表单
  const screenFormik = useFormik({
    initialValues: {
      id: "",
      outlet: "",
      // category: category,
      photo: "",
      departmentId: "",
      name: "",
      wide: "",
      high: "",
      direction: "",
      deviceType: "",
      timezone: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handelSaveSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      name: Yup.string()
        .required(t("common.common_input_device_name"))
        .min(0, t("common.common_rule_device_name"))
        .max(20, t("common.common_rule_device_name_length_max")),
      direction: Yup.string().required(
        t("common.common_plese_screen_direction")
      ),
      deviceType: Yup.string().required(t("common.common_please_type")),

      wide: Yup.string()
        .matches(/^[0-9]*$/, {
          message: t("ips.ips_resolution_input_number"),
          excludeEmptyString: true,
        })
        .required(t("common.common_input_width")),
      high: Yup.string()
        .matches(/^[0-9]*$/, {
          message: t("ips.ips_resolution_input_number"),
          excludeEmptyString: true,
        })
        .required(t("common.common_input_height")),
    }),
  });

  // 表单赋值
  const setFormData = (item) => {
    setSelectOutlet(item?.departmentId);
    screenFormik.setValues(
      {
        id: item?.id,
        outlet: item?.departmentId,
        category: item?.category,
        photo: item?.photo,
        // departmentId: item?.departmentId,
        name: item?.name,
        wide: item?.wide,
        high: item?.high,
        direction: item?.direction,
        deviceType: item?.deviceType,
        timezone: item?.timezone,
        applicationCode: item?.applicationCode,
      },
      true
    );
  };

  useEffect(() => {
    setFormData(data);
  }, [data, open]);

  return (
    <BootstrapDialog
      open={open}
      onClose={() => setOpen(false)}
      aria-describedby="alert-dialog-slide-description"
      sx={{
        "& .MuiPaper-root": {
          maxWidth: "1200px",
        },
      }}>
      <BootstrapDialogTitle
        sx={{
          borderRadius: "10px",
        }}>
        <Typography variant="label" component="p">
          {t("device.bind_device")}
        </Typography>

        <IconButton
          aria-label="close"
          onClick={() => setOpen(false)}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}>
          <CloseIcon />
        </IconButton>
      </BootstrapDialogTitle>
      <BootstrapContent>
        <Grid container spacing={3} sx={{ padding: "20px" }}>
          <Grid item xs={12} md={4} lg={4}>
            <Stack spacing={1}>
              <InputLabel htmlFor="screen-name">
                {t("ips.ips_device")} <i style={{ color: "red" }}>*</i>
              </InputLabel>
              <OutlinedInput
                id="dictLabel"
                value={screenFormik.values.name}
                type="text"
                fullWidth
                name="name"
                error={Boolean(
                  screenFormik.touched.name && screenFormik.errors.name
                )}
                onBlur={screenFormik.handleBlur}
                onChange={screenFormik.handleChange}
                placeholder={t("common.common_please_input")}
              />
              {screenFormik.touched.name && screenFormik.errors.name && (
                <FormHelperText error id="name-error">
                  {screenFormik.errors.name}
                </FormHelperText>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} md={4} lg={4}>
            <Stack spacing={1}>
              <InputLabel htmlFor="deviceType">
                {t("common.common_deviceType")}
                <i style={{ color: "red" }}>*</i>
              </InputLabel>
              <ZKSelect
                displayEmpty
                name="deviceType"
                value={screenFormik.values.deviceType}
                placeholder={t("common.common_plese_select")}
                options={deviceTypeOptions}
                onChange={screenFormik.handleChange}
                onBlur={screenFormik.handleBlur}
                isClear={false}
                labelOptions={{
                  label: "value",
                  value: "id",
                }}
                error={Boolean(
                  screenFormik.touched.deviceType &&
                    screenFormik.errors.deviceType
                )}
              />
              {screenFormik.touched.deviceType &&
                screenFormik.errors.deviceType && (
                  <FormHelperText error id="direction-error">
                    {screenFormik.errors.deviceType}
                  </FormHelperText>
                )}
            </Stack>
          </Grid>
          <Grid item xs={12} md={4} lg={4}>
            <Stack spacing={1}>
              <InputLabel htmlFor="direction">
                {t("ips.ips_screen_direction")}
                <i style={{ color: "red" }}>*</i>
              </InputLabel>
              <ZKSelect
                displayEmpty
                name="direction"
                value={screenFormik.values.direction}
                placeholder={t("common.common_plese_select")}
                options={directionOptions}
                onChange={screenFormik.handleChange}
                onBlur={screenFormik.handleBlur}
                isClear={false}
                labelOptions={{
                  label: "value",
                  value: "id",
                }}
                error={Boolean(
                  screenFormik.touched.direction &&
                    screenFormik.errors.direction
                )}
              />
              {screenFormik.touched.direction &&
                screenFormik.errors.direction && (
                  <FormHelperText error id="direction-error">
                    {screenFormik.errors.direction}
                  </FormHelperText>
                )}
            </Stack>
          </Grid>

          <Grid item xs={12} md={4} lg={4}>
            <CustomAutocomplete
              label={"Outlet"}
              required
              value={selectOutlet}
              onChange={(e) => {
                setSelectOutlet(e.target.value);
              }}
              labelOptions={{
                label: "name",
                value: "id",
              }}
              placeholder={t("common.common_plese_select")}
              options={outletList}></CustomAutocomplete>
          </Grid>

          <Grid item xs={12} md={4} lg={4}>
            <Stack spacing={1}>
              <InputLabel htmlFor="resolution_wide">
                {t("ips.ips_resolution_wide")} <i style={{ color: "red" }}>*</i>
              </InputLabel>
              <OutlinedInput
                id="wide"
                value={screenFormik.values.wide}
                type="text"
                fullWidth
                name="wide"
                error={Boolean(
                  screenFormik.touched.wide && screenFormik.errors.wide
                )}
                onBlur={screenFormik.handleBlur}
                onChange={screenFormik.handleChange}
                placeholder={t("common.common_please_input")}
              />
              {screenFormik.touched.wide && screenFormik.errors.wide && (
                <FormHelperText error id="wide-error">
                  {screenFormik.errors.wide}
                </FormHelperText>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} md={4} lg={4}>
            <Stack spacing={1}>
              <InputLabel htmlFor="resolution_high">
                {t("ips.ips_resolution_high")} <i style={{ color: "red" }}>*</i>
              </InputLabel>
              <OutlinedInput
                id="high"
                value={screenFormik.values.high}
                type="text"
                fullWidth
                name="high"
                error={Boolean(
                  screenFormik.touched.high && screenFormik.errors.high
                )}
                onBlur={screenFormik.handleBlur}
                onChange={screenFormik.handleChange}
                placeholder={t("common.common_please_input")}
              />
              {screenFormik.touched.high && screenFormik.errors.high && (
                <FormHelperText error id="high-error">
                  {screenFormik.errors.high}
                </FormHelperText>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} md={4} lg={4}>
            <Stack spacing={1}>
              <InputLabel htmlFor="deviceType">
                {t("outlets.time_zone")}
                <i style={{ color: "red" }}>*</i>
              </InputLabel>
              <ZKSelect
                label={t("")}
                required
                displayEmpty
                name="timezone"
                value={screenFormik.values.timezone}
                placeholder={t("common.common_plese_select")}
                isClear={false}
                disabled={true}
                onChange={screenFormik.handleChange}
                onBlur={screenFormik.handleBlur}
                labelOptions={{
                  label: "name",
                  value: "id",
                }}
                error={Boolean(
                  screenFormik.touched.timezone && screenFormik.errors.timezone
                )}
                options={timeZoneList}></ZKSelect>

              {screenFormik.touched.timezone &&
                screenFormik.errors.timezone && (
                  <FormHelperText error id="timezone-error">
                    {screenFormik.errors.timezone}
                  </FormHelperText>
                )}
            </Stack>
          </Grid>
          <Grid item xs={12}>
            <Alert severity="success" icon={<InfoIcon />}>
              <AlertTitle>
                {t("common.common_add_screen_alert_title")}
              </AlertTitle>
              <div
                dangerouslySetInnerHTML={{
                  __html: t("common.common_add_screen_alert_desc"),
                }}
              />
            </Alert>
          </Grid>
        </Grid>
      </BootstrapContent>
      <BootstrapActions
        sx={{
          display: "flex",
          justifyContent: "end",
        }}>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            onClick={() => setOpen(false)}
            style={{
              width: "132px",
              height: "48px",
              borderRadius: "8px",
              opacity: 1,
            }}
            disableElevation
            color="info"
            size="medium">
            {t("取消")}
          </Button>
          <AnimateButton>
            <LoadingButton
              loading={loading}
              disableElevation
              disabled={screenFormik?.isSubmitting}
              fullWidth
              type="submit"
              variant="contained"
              onClick={screenFormik.handleSubmit}
              style={{
                width: "132px",
                height: "48px",
                borderRadius: "8px",
                opacity: 1,
                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
              }}>
              {t("Save")}
            </LoadingButton>
          </AnimateButton>
        </Stack>
      </BootstrapActions>
    </BootstrapDialog>
  );
}

export default CMSDevice;
