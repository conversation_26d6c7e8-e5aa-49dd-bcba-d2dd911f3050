import { useTranslation } from "react-i18next";
export const PrincipalsColumns = () => {
  const { t } = useTranslation();
  return [
    {
      accessorKey: "name",
      header: t("principal.principal_name"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
    {
      accessorKey: "email",
      header: t("outlets.email"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
    {
      accessorKey: "areaName",
      header: t("common.common_area_name"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
  ];
};

export const OutletsColumns = () => {
  const { t } = useTranslation();
  return [
    {
      accessorKey: "name",
      header: t("outlets.name"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
    {
      accessorKey: "email",
      header: t("outlets.email"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
  ];
};

export const RetailColumns = () => {
  const { t } = useTranslation();
  return [
    {
      accessorKey: "principalName",
      header: t("datascope.retail_client_name"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
    {
      accessorKey: "principalEmail",
      header: t("datascope.retail_client_account"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
    {
      accessorKey: "areaName",
      header: t("area.area_area_name"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
    {
      accessorKey: "outletName",
      header: t("outlets.name"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
  ];
};

export const PartnerColumns = () => {
  const { t } = useTranslation();
  return [
    {
      accessorKey: "name",
      header: t("partner.partner_name"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
    {
      accessorKey: "email",
      header: t("outlets.email"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },

    {
      accessorKey: "phone",
      header: t("ips.ips_store_phone"),
      enableColumnActions: false,
      enableClickToCopy: false, // 启用点击复制
      enableSorting: false,
    },
  ];
};
