import React, { useCallback, useMemo } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Chip,
  Box,
  Typography,
  ListSubheader,
  Checkbox,
  ListItemText
} from '@mui/material';
import { useTranslation } from 'react-i18next';

/**
 * Schema驱动的选择框组件
 */
const SchemaSelect = ({
  name,
  path,
  schema,
  uiSchema = {},
  value,
  error,
  touched,
  disabled = false,
  readonly = false,
  required = false,
  onChange,
  onBlur,
  onValidate,
  formData,
  registry,
  ...props
}) => {
  const { t } = useTranslation();

  // 从schema中提取配置
  const {
    title,
    description,
    enum: enumValues,
    enumNames,
    oneOf,
    anyOf,
    type: schemaType
  } = schema;

  // 从uiSchema中提取UI配置
  const {
    label = title,
    help = description,
    placeholder,
    multiple = false,
    clearable = true,
    searchable = false,
    groupBy,
    variant = 'outlined',
    size = 'medium',
    fullWidth = true,
    renderValue,
    maxHeight = 300,
    ...uiProps
  } = uiSchema;

  // 构建选项列表
  const options = useMemo(() => {
    let opts = [];

    // 从enum构建选项
    if (enumValues) {
      opts = enumValues.map((val, index) => ({
        value: val,
        label: enumNames?.[index] || val,
        group: null
      }));
    }
    
    // 从oneOf构建选项
    else if (oneOf) {
      opts = oneOf.map(option => ({
        value: option.const || option.enum?.[0],
        label: option.title || option.const || option.enum?.[0],
        group: option.group || null
      }));
    }
    
    // 从anyOf构建选项
    else if (anyOf) {
      opts = anyOf.map(option => ({
        value: option.const || option.enum?.[0],
        label: option.title || option.const || option.enum?.[0],
        group: option.group || null
      }));
    }

    // 按组分组
    if (groupBy) {
      const grouped = {};
      opts.forEach(opt => {
        const group = opt[groupBy] || t('common.other');
        if (!grouped[group]) {
          grouped[group] = [];
        }
        grouped[group].push(opt);
      });
      return grouped;
    }

    return opts;
  }, [enumValues, enumNames, oneOf, anyOf, groupBy, t]);

  // 处理值变化
  const handleChange = useCallback((event) => {
    const newValue = event.target.value;
    onChange?.(newValue);
  }, [onChange]);

  // 处理失焦
  const handleBlur = useCallback((event) => {
    onBlur?.(event);
    
    // 执行验证
    if (onValidate) {
      const errors = [];
      
      // 必填验证
      if (required && (value === undefined || value === null || value === '')) {
        errors.push(t('validation.required', { field: label || name }));
      }
      
      onValidate(errors.length > 0 ? errors[0] : null);
    }
  }, [onBlur, onValidate, value, required, label, name, t]);

  // 自定义渲染选中值
  const renderSelectedValue = useCallback((selected) => {
    if (renderValue) {
      return renderValue(selected, options);
    }

    if (multiple) {
      if (!Array.isArray(selected) || selected.length === 0) {
        return <em>{placeholder || t('common.pleaseSelect')}</em>;
      }
      
      return (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {selected.map((val) => {
            const option = Array.isArray(options) 
              ? options.find(opt => opt.value === val)
              : Object.values(options).flat().find(opt => opt.value === val);
            return (
              <Chip
                key={val}
                label={option?.label || val}
                size="small"
                variant="outlined"
              />
            );
          })}
        </Box>
      );
    }

    if (selected === undefined || selected === null || selected === '') {
      return <em>{placeholder || t('common.pleaseSelect')}</em>;
    }

    const option = Array.isArray(options) 
      ? options.find(opt => opt.value === selected)
      : Object.values(options).flat().find(opt => opt.value === selected);
    
    return option?.label || selected;
  }, [renderValue, options, multiple, placeholder, t]);

  // 渲染选项
  const renderOptions = useCallback(() => {
    if (Array.isArray(options)) {
      return options.map((option) => (
        <MenuItem key={option.value} value={option.value}>
          {multiple && (
            <Checkbox checked={Array.isArray(value) && value.includes(option.value)} />
          )}
          <ListItemText primary={option.label} />
        </MenuItem>
      ));
    }

    // 分组选项
    return Object.entries(options).map(([group, groupOptions]) => [
      <ListSubheader key={group}>{group}</ListSubheader>,
      ...groupOptions.map((option) => (
        <MenuItem key={option.value} value={option.value}>
          {multiple && (
            <Checkbox checked={Array.isArray(value) && value.includes(option.value)} />
          )}
          <ListItemText primary={option.label} />
        </MenuItem>
      ))
    ]).flat();
  }, [options, multiple, value]);

  const selectValue = multiple ? (Array.isArray(value) ? value : []) : (value || '');

  return (
    <Box className="schema-form-field">
      <FormControl
        variant={variant}
        size={size}
        fullWidth={fullWidth}
        error={Boolean(error && touched)}
        disabled={disabled}
        required={required}
      >
        {label && (
          <InputLabel id={`${name}-label`}>
            {label}
          </InputLabel>
        )}
        
        <Select
          labelId={`${name}-label`}
          name={name}
          value={selectValue}
          label={label}
          multiple={multiple}
          displayEmpty={!label}
          renderValue={renderSelectedValue}
          onChange={handleChange}
          onBlur={handleBlur}
          readOnly={readonly}
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: maxHeight
              }
            }
          }}
          {...uiProps}
          {...props}
        >
          {!multiple && placeholder && (
            <MenuItem value="" disabled>
              <em>{placeholder}</em>
            </MenuItem>
          )}
          {renderOptions()}
        </Select>
        
        {((error && touched) || help) && (
          <FormHelperText>
            {(error && touched) ? error : (
              help && (
                <Typography variant="caption" color="text.secondary">
                  {help}
                </Typography>
              )
            )}
          </FormHelperText>
        )}
      </FormControl>
    </Box>
  );
};

export default SchemaSelect;
