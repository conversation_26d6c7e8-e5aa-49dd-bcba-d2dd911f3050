import React, { useEffect, useState } from "react";
import {
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
} from "@mui/material";
import CheckboxIcon from "@mui/icons-material/CheckBoxOutlineBlank"; // 未选中状态图标
import RequirePoint from "../RequirePoint";
import { styled } from "@mui/material/styles";
const CheckboxCheckedIcon = styled("svg")(({ theme }) => ({
  width: 24,
  height: 24,
}));

// 创建一个自定义的 Radio 组件
const GradientRadio = styled(Radio)(({ theme }) => ({
  "&.Mui-checked": {
    "& .MuiSvgIcon-root": {
      display: "none", // 隐藏默认的选中图标
    },
    "& + .MuiFormControlLabel-label": {
      color: theme.palette.text.primary, // 可以根据需要调整文本颜色
    },
  },
}));
function ZkRadio(props) {
  const {
    formik = null,
    placeholder = "",
    handleBlur,
    handleChange,
    labelOptions = { label: "label", value: "value" },
    label,
    name,
    error,
    value = "",
    options = [],
    labelpostion,
    disabled,
    ...orther
  } = props;

  const CustomRadio = ({ value, label, disabledData }) => {
    return (
      <FormControlLabel
        value={value}
        control={
          <GradientRadio
            icon={<CheckboxIcon />}
            sx={{
              "& .MuiFormControlLabel-root": {
                color: "#000",
              },
            }}
            name={name}
            disabled={disabledData || disabled}
            checkedIcon={
              <CheckboxCheckedIcon>
                <CheckboxCheckedIcon>
                  <rect width="24" height="24" rx="4" fill="url(#gradient)" />
                  <path
                    d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"
                    fill="white"
                  />
                  <defs>
                    <linearGradient
                      id="gradient"
                      x1="100%"
                      y1="50%"
                      x2="0%"
                      y2="50%">
                      <stop offset="0%" stopColor="#1487CA" />
                      <stop offset="100%" stopColor="#78BC27" />
                    </linearGradient>
                  </defs>
                </CheckboxCheckedIcon>
              </CheckboxCheckedIcon>
            }
          />
        }
        label={label}
      />
    );
  };

  const changeFn = (e) => {
    if (formik?.handleChange) {
      formik?.handleChange(e);
    }
    if (handleBlur) {
      handleBlur(e);
    }
  };

  return (
    <Stack spacing={1}>
      <Stack
        direction={labelpostion === "left" ? "row" : "column"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={1}>
        {label && (
          <InputLabel
            style={{
              marginTop: labelpostion === "left" ? "12px" : "",
              color: "#474b4fcc",
            }}
            htmlFor={"zkSelect" + name}>
            {label} {props.required && <RequirePoint></RequirePoint>}
          </InputLabel>
        )}
        <Stack
          sx={{
            width: "100%",
            marginTop: "1px !important",
            maxWidth:
              label && labelpostion === "left" ? "calc(100% - 60px )" : "100%",
          }}>
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            name={name}
            value={formik ? formik.values[name] ?? "" : value ?? ""}
            onChange={changeFn}
            row>
            {options?.map((item, index) => {
              return (
                <CustomRadio
                  key={item?.value || `radio-${index}`}
                  value={item?.value}
                  label={item?.label}
                  disabledData={item.disabledData}></CustomRadio>
              );
            })}
          </RadioGroup>
          {((formik?.touched[name] && formik?.errors[name]) || error) && (
            <FormHelperText error id={`standard-weight-helper-text-${name}`}>
              {formik?.errors[name] || error}
            </FormHelperText>
          )}
        </Stack>
      </Stack>
    </Stack>
  );
}

export default ZkRadio;
