/*
 * @Author: your name
 * @Date: 2019-10-09 10:50:39
 * @LastEditTime: 2020-05-20 16:54:10
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \smart-dbs-web-pc\distStart.js
 */
import express from 'express'
import proxyMiddleWare from 'http-proxy-middleware'
var serverUrl = 'http://**********:9090'
var proxyOption = {
  target: serverUrl,
  changeOrigin: true,
  pathRewrite: {
    '^/dev': ''
  }
}
var app = express()
app.use(express.static('dist'))
app.use('^/dev/**', proxyMiddleWare(proxyOption))
app.get('*', function(req, res) {
  res.sendfile('dist/index.html')
})
var server = app.listen(8089, function() {
  var host = server.address().address
  var port = server.address().port
  host = 'localhost'
  console.log('应用实例，访问地址为 http://%s:%s', host, port)
})
